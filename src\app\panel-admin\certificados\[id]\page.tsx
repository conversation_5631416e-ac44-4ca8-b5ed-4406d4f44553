"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { use } from "react";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import QRCodeGenerator from "@/components/ui/qr-code-generator";

// Define types for our data
type Certificate = {
  id: string;
  certificate_number: string;
  issue_date: string;
  expiry_date: string | null;
  status: string;
  qr_code_url: string | null;
  user_id: string;
  course_id: string;
};

type Student = {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
};

type Course = {
  id: string;
  title: string;
  description: string | null;
};

export default function CertificateDetail({ params }: { params: { id: string } }) {
  const router = useRouter();
  const unwrappedParams = use(params);
  const certificateId = unwrappedParams.id;

  // State
  const [certificate, setCertificate] = useState<Certificate | null>(null);
  const [student, setStudent] = useState<Student | null>(null);
  const [course, setCourse] = useState<Course | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Status colors
  const statusColors = {
    ACTIVE: "bg-green-100 text-green-800",
    REVOKED: "bg-red-100 text-red-800",
    EXPIRED: "bg-yellow-100 text-yellow-800",
  };

  // Fetch certificate data
  useEffect(() => {
    async function fetchCertificateData() {
      try {
        setLoading(true);
        setError(null);

        // Fetch certificate
        const { data: certificateData, error: certificateError } = await supabase
          .from("certificates")
          .select("*")
          .eq("id", certificateId)
          .single();

        if (certificateError) throw certificateError;
        if (!certificateData) throw new Error("Certificado no encontrado");

        setCertificate(certificateData);

        // Fetch student data
        const { data: studentData, error: studentError } = await supabase
          .from("users")
          .select("id, first_name, last_name, email")
          .eq("id", certificateData.user_id)
          .single();

        if (studentError) throw studentError;
        setStudent(studentData);

        // Fetch course data
        const { data: courseData, error: courseError } = await supabase
          .from("courses")
          .select("id, title, description")
          .eq("id", certificateData.course_id)
          .single();

        if (courseError) throw courseError;
        setCourse(courseData);
      } catch (error: any) {
        console.error("Error fetching certificate data:", error);
        setError(error.message || "Error al cargar los datos del certificado");
      } finally {
        setLoading(false);
      }
    }

    fetchCertificateData();
  }, [certificateId]);

  // Handle certificate deletion
  const handleDelete = async () => {
    try {
      setDeleting(true);
      setError(null);

      const { error } = await supabase
        .from("certificates")
        .delete()
        .eq("id", certificateId);

      if (error) throw error;

      router.push("/panel-admin/certificados");
    } catch (error: any) {
      console.error("Error deleting certificate:", error);
      setError(error.message || "Error al eliminar el certificado");
      setDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Handle status change
  const handleStatusChange = async (newStatus: string) => {
    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase
        .from("certificates")
        .update({ status: newStatus })
        .eq("id", certificateId);

      if (error) throw error;

      // Update local state
      if (certificate) {
        setCertificate({
          ...certificate,
          status: newStatus,
        });
      }
    } catch (error: any) {
      console.error("Error updating certificate status:", error);
      setError(error.message || "Error al actualizar el estado del certificado");
    } finally {
      setLoading(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Cargando datos del certificado...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-red-50 border-l-4 border-red-500 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
            <div className="mt-4">
              <Link
                href="/panel-admin/certificados"
                className="text-sm text-red-700 font-medium hover:text-red-600"
              >
                ← Volver a la lista de certificados
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // No certificate found
  if (!certificate || !student || !course) {
    return (
      <div className="bg-yellow-50 border-l-4 border-yellow-500 p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">
              Certificado no encontrado
            </h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>El certificado solicitado no existe o ha sido eliminado.</p>
            </div>
            <div className="mt-4">
              <Link
                href="/panel-admin/certificados"
                className="text-sm text-yellow-700 font-medium hover:text-yellow-600"
              >
                ← Volver a la lista de certificados
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div>
      {/* Header with actions */}
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Certificado {certificate.certificate_number}
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/panel-admin/certificados"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            ← Volver
          </Link>
          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Eliminar
          </button>
        </div>
      </div>

      {/* Certificate details */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
          <div>
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Información del Certificado
            </h3>
            <p className="mt-1 max-w-2xl text-sm text-gray-500">
              Detalles y propiedades del certificado emitido.
            </p>
          </div>
          <div>
            <span
              className={`inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium ${
                statusColors[certificate.status as keyof typeof statusColors] ||
                "bg-gray-100 text-gray-800"
              }`}
            >
              {certificate.status === "ACTIVE"
                ? "Activo"
                : certificate.status === "REVOKED"
                ? "Revocado"
                : certificate.status === "EXPIRED"
                ? "Expirado"
                : certificate.status}
            </span>
          </div>
        </div>
        <div className="border-t border-gray-200">
          <dl>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Número de Certificado
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {certificate.certificate_number}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Alumno</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {`${student.first_name} ${student.last_name}`.trim()} ({student.email})
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Curso</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {course.title}
                {course.description && (
                  <p className="text-xs text-gray-500 mt-1">
                    {course.description}
                  </p>
                )}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Fecha de Emisión
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {new Date(certificate.issue_date).toLocaleDateString("es-ES", {
                  day: "2-digit",
                  month: "long",
                  year: "numeric",
                })}
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Fecha de Vencimiento
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {certificate.expiry_date
                  ? new Date(certificate.expiry_date).toLocaleDateString(
                      "es-ES",
                      {
                        day: "2-digit",
                        month: "long",
                        year: "numeric",
                      }
                    )
                  : "No vence"}
              </dd>
            </div>
            <div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Estado</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <div className="flex items-center">
                  <select
                    value={certificate.status}
                    onChange={(e) => handleStatusChange(e.target.value)}
                    className="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="ACTIVE">Activo</option>
                    <option value="REVOKED">Revocado</option>
                    <option value="EXPIRED">Expirado</option>
                  </select>
                </div>
              </dd>
            </div>
            <div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">
                Enlace de Verificación
              </dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <a
                  href={`${window.location.origin}/verificar-certificado/certificado/${certificate.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-indigo-600 hover:text-indigo-500"
                >
                  {`${window.location.origin}/verificar-certificado/certificado/${certificate.id}`}
                </a>
              </dd>
            </div>
          </dl>
        </div>

        {/* QR Code */}
        <div className="border-t border-gray-200 px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
            Código QR
          </h3>
          <div className="flex justify-center">
            <div className="p-4 bg-white border border-gray-200 rounded-lg">
              {certificate.id ? (
                <QRCodeGenerator
                  url={`/verificar-certificado/certificado/${certificate.id}`}
                  size={200}
                  className="w-48 h-48"
                  onError={(error) => {
                    console.error("Error en QRCodeGenerator:", error);
                    // El componente ya maneja los errores internamente
                  }}
                />
              ) : (
                <div className="w-48 h-48 flex items-center justify-center bg-gray-100 text-gray-400 text-sm">
                  QR no disponible
                </div>
              )}
            </div>
          </div>
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-500">
              Este código QR dirige a la página de verificación del certificado.
            </p>
            {(certificate.qr_code || certificate.qr_code_url) && (
              <a
                href={certificate.qr_code || certificate.qr_code_url}
                download={`certificado-${certificate.certificate_number}.png`}
                className="mt-2 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                onClick={(e) => {
                  // Verificar si la URL es válida antes de intentar descargar
                  const url = certificate.qr_code || certificate.qr_code_url;
                  if (!url || url.startsWith('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNk+A8AAQUBAScY42YAAAAASUVORK5CYII=')) {
                    e.preventDefault();
                    alert('No se puede descargar el código QR porque no está disponible o está dañado.');
                  }
                }}
              >
                Descargar QR
              </a>
            )}
            <div className="mt-2">
              <a
                href={`/verificar-certificado/certificado/${certificate.id}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-indigo-600 hover:text-indigo-500 text-sm"
              >
                Ver página de verificación
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Delete confirmation modal */}
      {showDeleteConfirm && (
        <div className="fixed z-10 inset-0 overflow-y-auto">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={() => setShowDeleteConfirm(false)}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <span
              className="hidden sm:inline-block sm:align-middle sm:h-screen"
              aria-hidden="true"
            >
              &#8203;
            </span>

            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg
                      className="h-6 w-6 text-red-600"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      aria-hidden="true"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                      />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3
                      className="text-lg leading-6 font-medium text-gray-900"
                      id="modal-title"
                    >
                      Eliminar Certificado
                    </h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        ¿Estás seguro de que deseas eliminar este certificado?
                        Esta acción no se puede deshacer y eliminará
                        permanentemente el certificado {certificate.certificate_number} del sistema.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDelete}
                  disabled={deleting}
                >
                  {deleting ? "Eliminando..." : "Eliminar"}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancelar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}