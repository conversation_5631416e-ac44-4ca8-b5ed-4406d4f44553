/**
 * Supabase Database Adapter Implementation
 *
 * This file implements the database adapter interfaces using Supabase as the backend.
 * This allows us to decouple business logic from Supabase-specific APIs while maintaining
 * all current functionality.
 *
 * The adapter pattern enables easy migration to other database providers in the future.
 */

import { createClient, SupabaseClient } from '@supabase/supabase-js';
import {
  DatabaseAdapter,
  AuthAdapter,
  StorageAdapter,
  AppAdapter,
  DatabaseResponse,
  AuthResponse,
  StorageResponse,
  QueryOptions,
  QueryFilter,
  InsertOptions,
  UpdateOptions,
  DeleteOptions,
  SignInCredentials,
  SignUpCredentials,
  AuthSession,
  AuthUser,
  UploadOptions,
  DatabaseError
} from './types';

// ============================================================================
// Supabase Database Adapter
// ============================================================================

export class SupabaseDatabaseAdapter implements DatabaseAdapter {
  constructor(private client: SupabaseClient) {}

  async select<T = any>(
    table: string,
    options?: QueryOptions
  ): Promise<DatabaseResponse<T[]>> {
    try {
      let query = this.client.from(table).select(options?.select || '*');

      // Apply filters
      if (options?.filters) {
        for (const filter of options.filters) {
          query = this.applyFilter(query, filter);
        }
      }

      // Apply ordering
      if (options?.orderBy) {
        for (const order of options.orderBy) {
          query = query.order(order.column, { ascending: order.ascending ?? true });
        }
      }

      // Apply pagination
      if (options?.limit) {
        query = query.limit(options.limit);
      }
      if (options?.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 1000) - 1);
      }

      const { data, error, count } = await query;

      return {
        data: data as T[],
        error: error ? this.mapError(error) : null,
        count
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapError(err)
      };
    }
  }

  async selectOne<T = any>(
    table: string,
    options?: QueryOptions
  ): Promise<DatabaseResponse<T>> {
    try {
      let query = this.client.from(table).select(options?.select || '*');

      // Apply filters
      if (options?.filters) {
        for (const filter of options.filters) {
          query = this.applyFilter(query, filter);
        }
      }

      const { data, error } = await query.single();

      return {
        data: data as T,
        error: error ? this.mapError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapError(err)
      };
    }
  }

  async insert<T = any>(
    table: string,
    data: Partial<T> | Partial<T>[],
    options?: InsertOptions
  ): Promise<DatabaseResponse<T>> {
    try {
      let query = this.client.from(table).insert(data);

      if (options?.returning) {
        query = query.select(options.returning);
      }

      const { data: result, error } = await query;

      return {
        data: result as T,
        error: error ? this.mapError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapError(err)
      };
    }
  }

  async update<T = any>(
    table: string,
    data: Partial<T>,
    filters: QueryFilter[],
    options?: UpdateOptions
  ): Promise<DatabaseResponse<T>> {
    try {
      let query = this.client.from(table).update(data);

      // Apply filters
      for (const filter of filters) {
        query = this.applyFilter(query, filter);
      }

      if (options?.returning) {
        query = query.select(options.returning);
      }

      const { data: result, error } = await query;

      return {
        data: result as T,
        error: error ? this.mapError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapError(err)
      };
    }
  }

  async delete<T = any>(
    table: string,
    filters: QueryFilter[],
    options?: DeleteOptions
  ): Promise<DatabaseResponse<T>> {
    try {
      let query = this.client.from(table).delete();

      // Apply filters
      for (const filter of filters) {
        query = this.applyFilter(query, filter);
      }

      if (options?.returning) {
        query = query.select(options.returning);
      }

      const { data: result, error } = await query;

      return {
        data: result as T,
        error: error ? this.mapError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapError(err)
      };
    }
  }

  async rpc<T = any>(
    functionName: string,
    params?: Record<string, any>
  ): Promise<DatabaseResponse<T>> {
    try {
      const { data, error } = await this.client.rpc(functionName, params);

      return {
        data: data as T,
        error: error ? this.mapError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapError(err)
      };
    }
  }

  async transaction<T = any>(
    operations: ((adapter: DatabaseAdapter) => Promise<any>)[]
  ): Promise<DatabaseResponse<T>> {
    // Note: Supabase doesn't have explicit transaction support in the client
    // This is a simplified implementation - in production, you'd use database-level transactions
    try {
      const results = [];
      for (const operation of operations) {
        const result = await operation(this);
        results.push(result);
      }

      return {
        data: results as T,
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapError(err)
      };
    }
  }

  // Helper methods
  private applyFilter(query: any, filter: QueryFilter): any {
    switch (filter.operator) {
      case 'eq':
        return query.eq(filter.column, filter.value);
      case 'neq':
        return query.neq(filter.column, filter.value);
      case 'gt':
        return query.gt(filter.column, filter.value);
      case 'gte':
        return query.gte(filter.column, filter.value);
      case 'lt':
        return query.lt(filter.column, filter.value);
      case 'lte':
        return query.lte(filter.column, filter.value);
      case 'like':
        return query.like(filter.column, filter.value);
      case 'ilike':
        return query.ilike(filter.column, filter.value);
      case 'in':
        return query.in(filter.column, filter.value);
      case 'is':
        return query.is(filter.column, filter.value);
      case 'not':
        return query.not(filter.column, filter.value);
      default:
        return query;
    }
  }

  private mapError(error: any): DatabaseError {
    return {
      code: error?.code,
      message: error?.message || 'Unknown database error',
      details: error
    };
  }
}

// ============================================================================
// Supabase Auth Adapter
// ============================================================================

export class SupabaseAuthAdapter implements AuthAdapter {
  constructor(private client: SupabaseClient) {}

  async signIn(credentials: SignInCredentials): Promise<AuthResponse<AuthSession>> {
    try {
      const { data, error } = await this.client.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password
      });

      if (error) {
        return {
          data: null,
          error: this.mapAuthError(error)
        };
      }

      return {
        data: this.mapAuthSession(data),
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapAuthError(err)
      };
    }
  }

  async signUp(credentials: SignUpCredentials): Promise<AuthResponse<AuthUser>> {
    try {
      const { data, error } = await this.client.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: credentials.options
      });

      if (error) {
        return {
          data: null,
          error: this.mapAuthError(error)
        };
      }

      return {
        data: this.mapAuthUser(data.user),
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapAuthError(err)
      };
    }
  }

  async signOut(): Promise<AuthResponse<void>> {
    try {
      const { error } = await this.client.auth.signOut();

      return {
        data: null,
        error: error ? this.mapAuthError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapAuthError(err)
      };
    }
  }

  async getSession(): Promise<AuthResponse<AuthSession>> {
    try {
      const { data, error } = await this.client.auth.getSession();

      if (error) {
        return {
          data: null,
          error: this.mapAuthError(error)
        };
      }

      return {
        data: data.session ? this.mapAuthSession(data) : null,
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapAuthError(err)
      };
    }
  }

  async refreshSession(): Promise<AuthResponse<AuthSession>> {
    try {
      const { data, error } = await this.client.auth.refreshSession();

      if (error) {
        return {
          data: null,
          error: this.mapAuthError(error)
        };
      }

      return {
        data: this.mapAuthSession(data),
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapAuthError(err)
      };
    }
  }

  async getUser(): Promise<AuthResponse<AuthUser>> {
    try {
      const { data, error } = await this.client.auth.getUser();

      if (error) {
        return {
          data: null,
          error: this.mapAuthError(error)
        };
      }

      return {
        data: this.mapAuthUser(data.user),
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapAuthError(err)
      };
    }
  }

  async updateUser(updates: Partial<AuthUser>): Promise<AuthResponse<AuthUser>> {
    try {
      const { data, error } = await this.client.auth.updateUser({
        email: updates.email,
        data: updates
      });

      if (error) {
        return {
          data: null,
          error: this.mapAuthError(error)
        };
      }

      return {
        data: this.mapAuthUser(data.user),
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapAuthError(err)
      };
    }
  }

  // Helper methods
  private mapAuthSession(data: any): AuthSession {
    return {
      user: this.mapAuthUser(data.session?.user || data.user),
      access_token: data.session?.access_token || data.access_token,
      refresh_token: data.session?.refresh_token || data.refresh_token,
      expires_at: data.session?.expires_at || data.expires_at
    };
  }

  private mapAuthUser(user: any): AuthUser {
    return {
      id: user?.id,
      email: user?.email,
      role: user?.user_metadata?.role || user?.app_metadata?.role,
      last_sign_in_at: user?.last_sign_in_at,
      created_at: user?.created_at,
      updated_at: user?.updated_at
    };
  }

  private mapAuthError(error: any): DatabaseError {
    return {
      code: error?.code,
      message: error?.message || 'Unknown authentication error',
      details: error
    };
  }
}

// ============================================================================
// Supabase Storage Adapter
// ============================================================================

export class SupabaseStorageAdapter implements StorageAdapter {
  constructor(private client: SupabaseClient) {}

  async upload(
    bucket: string,
    path: string,
    file: File | Blob,
    options?: UploadOptions
  ): Promise<StorageResponse<{ path: string }>> {
    try {
      const { data, error } = await this.client.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: options?.cacheControl || '3600',
          upsert: options?.upsert || false,
          contentType: options?.contentType
        });

      if (error) {
        return {
          data: null,
          error: this.mapStorageError(error)
        };
      }

      return {
        data: { path: data.path },
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapStorageError(err)
      };
    }
  }

  async download(bucket: string, path: string): Promise<StorageResponse<Blob>> {
    try {
      const { data, error } = await this.client.storage
        .from(bucket)
        .download(path);

      if (error) {
        return {
          data: null,
          error: this.mapStorageError(error)
        };
      }

      return {
        data,
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapStorageError(err)
      };
    }
  }

  async delete(bucket: string, paths: string[]): Promise<StorageResponse<void>> {
    try {
      const { error } = await this.client.storage
        .from(bucket)
        .remove(paths);

      return {
        data: null,
        error: error ? this.mapStorageError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapStorageError(err)
      };
    }
  }

  getPublicUrl(bucket: string, path: string): string {
    const { data } = this.client.storage
      .from(bucket)
      .getPublicUrl(path);

    return data.publicUrl;
  }

  async getSignedUrl(
    bucket: string,
    path: string,
    expiresIn: number
  ): Promise<StorageResponse<string>> {
    try {
      const { data, error } = await this.client.storage
        .from(bucket)
        .createSignedUrl(path, expiresIn);

      if (error) {
        return {
          data: null,
          error: this.mapStorageError(error)
        };
      }

      return {
        data: data.signedUrl,
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapStorageError(err)
      };
    }
  }

  async createBucket(
    name: string,
    options?: { public?: boolean }
  ): Promise<StorageResponse<void>> {
    try {
      const { error } = await this.client.storage.createBucket(name, {
        public: options?.public || false
      });

      return {
        data: null,
        error: error ? this.mapStorageError(error) : null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapStorageError(err)
      };
    }
  }

  async listBuckets(): Promise<StorageResponse<string[]>> {
    try {
      const { data, error } = await this.client.storage.listBuckets();

      if (error) {
        return {
          data: null,
          error: this.mapStorageError(error)
        };
      }

      return {
        data: data.map(bucket => bucket.name),
        error: null
      };
    } catch (err) {
      return {
        data: null,
        error: this.mapStorageError(err)
      };
    }
  }

  private mapStorageError(error: any): DatabaseError {
    return {
      code: error?.code,
      message: error?.message || 'Unknown storage error',
      details: error
    };
  }
}

// ============================================================================
// Combined Supabase Adapter
// ============================================================================

export class SupabaseAppAdapter implements AppAdapter {
  public database: DatabaseAdapter;
  public auth: AuthAdapter;
  public storage: StorageAdapter;

  constructor(supabaseUrl: string, supabaseKey: string) {
    const client = createClient(supabaseUrl, supabaseKey);

    this.database = new SupabaseDatabaseAdapter(client);
    this.auth = new SupabaseAuthAdapter(client);
    this.storage = new SupabaseStorageAdapter(client);
  }
}

// ============================================================================
// Factory Function
// ============================================================================

export function createSupabaseAdapter(
  supabaseUrl: string,
  supabaseKey: string
): AppAdapter {
  return new SupabaseAppAdapter(supabaseUrl, supabaseKey);
}
