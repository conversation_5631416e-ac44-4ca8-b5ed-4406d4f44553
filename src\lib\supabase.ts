import { createClient } from '@supabase/supabase-js';

// These environment variables are already defined in .env.local
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL as string;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string;

// Create Supabase client with properly configured cookies
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    persistSession: true,
    // Use cookies instead of localStorage for better cross-context persistence
    storage: {
      getItem: (key) => {
        if (typeof document === 'undefined') return null;
        const value = document.cookie
          .split('; ')
          .find((row) => row.startsWith(`${key}=`))
          ?.split('=')[1];
        return value ? decodeURIComponent(value) : null;
      },
      setItem: (key, value) => {
        if (typeof document === 'undefined') return;
        // Set cookie with appropriate attributes for better security and persistence
        document.cookie = `${key}=${encodeURIComponent(value)}; path=/; max-age=2592000; SameSite=Lax`;
      },
      removeItem: (key) => {
        if (typeof document === 'undefined') return;
        document.cookie = `${key}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
      },
    },
    autoRefreshToken: true,
    detectSessionInUrl: true,
    flowType: 'implicit',
    debug: false // Cambiar a false en producción para evitar log excesivo
  },
  // Configuración global para todas las solicitudes a la API de Supabase
  global: {
    headers: {
      'Content-Type': 'application/json'
    },
    // Aumentar tiempos de espera para evitar errores de timeout
    fetch: (url, options) => {
      const timeout = 10000; // 10 segundos de timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const fetchPromise = fetch(url, {
        ...options,
        signal: controller.signal
      });

      return fetchPromise.finally(() => clearTimeout(timeoutId));
    }
  },
  // Reintentar solicitudes fallidas automáticamente
  realtime: {
    params: {
      eventsPerSecond: 2
    }
  }
});

// Helper function para verificar y refrescar la sesión si es necesario
export async function ensureValidSession() {
  const { data: { session }, error } = await supabase.auth.getSession();

  if (error) {
    console.error("Error checking session:", error);
    return false;
  }

  if (!session) {
    return false;
  }

  // Verificar si la sesión está cerca de expirar (menos de 5 minutos)
  const expiresAt = session.expires_at || 0;
  const fiveMinutesFromNow = Math.floor(Date.now() / 1000) + 300;

  if (expiresAt < fiveMinutesFromNow) {
    // Intentar refrescar la sesión
    const { data: refreshData, error: refreshError } = await supabase.auth.refreshSession();

    if (refreshError) {
      console.error("Error refreshing session:", refreshError);
      return false;
    }

    return !!refreshData.session;
  }

  return true;
}

// Removed getDatabaseSchema function as it was unreliable due to permission issues
// and its usage was removed from calling components.

// Función para verificar existencia de tablas (mejorada y optimizada para evitar errores en producción)
export async function tableExists(tableName: string): Promise<boolean> {
  try {
    // Reducimos el logging para evitar llenar la consola
    // console.log(`Verificando existencia de tabla: ${tableName}`);

    // Lista de tablas conocidas para evitar consultas innecesarias
    const knownTables = ['users', 'certificates', 'courses', 'certificate_templates'];

    // Si estamos en una página de previsualización, asumimos que las tablas existen
    // para evitar errores innecesarios
    if (typeof window !== 'undefined' &&
        (window.location.pathname.includes('/certificado-preview/') ||
         window.location.pathname.includes('/plantillas/') ||
         window.location.pathname.includes('/certificado/'))) {
      // console.log(`Asumiendo que la tabla ${tableName} existe en página de previsualización`);
      return true;
    }

    // Si es una tabla conocida, asumimos que existe para reducir consultas
    if (knownTables.includes(tableName)) {
      return true;
    }

    // Intentar verificar directamente con una consulta LIMIT 0 (más eficiente)
    const { error } = await supabase
      .from(tableName)
      .select('*', { head: true })
      .limit(0);

    if (!error) {
      // Si no hay error, la tabla existe y es accesible.
      // console.log(`Tabla ${tableName} existe y es accesible.`);
      return true;
    } else {
      // Analizar el error para diferenciar "no existe" de otros problemas.
      if (error.code === '42P01' || error.message.includes('does not exist')) {
        // console.log(`Tabla ${tableName} no existe.`);
        return false;
      } else {
        // Otro tipo de error (ej: permisos RLS, error de red)
        console.warn(`Error al verificar tabla ${tableName}:`, error.message);
        // En producción, asumimos que la tabla existe para evitar bloqueos
        // Esto puede generar otros errores más adelante, pero al menos la UI no se bloqueará
        return true;
      }
    }
  } catch (error) {
    console.error(`Error al verificar existencia de tabla ${tableName}:`, error);
    // En caso de error, asumimos que la tabla existe para evitar bloqueos en la UI
    return true;
  }
}

// Removed GET_TABLES_INFO_SQL constant as the RPC function is not used.

// Types based on your existing database schema
export type User = {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  rut?: string;
  identity_document?: string; // RUT chileno u otro documento de identidad
  phone?: string;
  role: 'admin' | 'student' | 'instructor';
  company_id?: string;
  created_at?: string;
  updated_at?: string;
  // No need for last_sign_in as we use auth.users.last_sign_in_at
  // Additional fields from the users table
}

export type Certificate = {
  id: string;
  user_id: string;
  course_id: string;
  issue_date: string;
  expiry_date: string | null;
  certificate_number: string;
  status: string;
  qr_code: string | null; // URL del código QR
  qr_code_url?: string | null; // Campo antiguo, mantenido por compatibilidad
  attendance_percentage?: number; // Porcentaje de asistencia
  created_at: string;
  updated_at: string;
}