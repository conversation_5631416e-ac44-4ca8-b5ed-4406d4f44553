# 📝 PLANNING.md — Project Constitution

_Last Updated: 2025-01-12_

## Table of Contents

1. [Project Vision & Purpose](#1-project-vision--purpose)
2. [Tech Stack & Architecture](#2-tech-stack--architecture)
3. [Key Principles & Constraints](#3-key-principles--constraints)
4. [AI Context & Automation](#4-ai-context--automation)
5. [Feature Epics](#5-feature-epics)
6. [Modular Structure](#6-modular-structure)
7. [How to Extend](#7-how-to-extend)
8. [References](#8-references)

---

## 1. Project Vision & Purpose

**Vision:** A robust, open-source scaffold for launching online course and certification platforms, designed for rapid rebranding, multi-tenancy, and AI coding agent collaboration.

**Purpose:** To provide a production-ready, context-engineered template that enables rapid deployment of course management and certification systems while maintaining high code quality, security, and AI-agent compatibility.

---

## 2. Tech Stack & Architecture

**General Architecture:** Monolithic Next.js application with Supabase backend, designed for easy multi-tenant deployment and AI agent collaboration.

**Tech Stack:**
- **Primary Language:** TypeScript
- **Frontend Framework:** Next.js 14+ (App Router, SSR/SSG, RSC)
- **UI Framework:** React 19, TailwindCSS, Shadcn/ui
- **Backend/Database:** Supabase (PostgreSQL, Auth, Storage, Edge Functions, RLS)
- **Authentication:** Supabase Auth with Row Level Security
- **Deployment:** Vercel (Frontend), Supabase (Backend)
- **Testing:** Playwright (E2E), Vitest (Unit)
- **CI/CD:** GitHub Actions, automated dependency updates
- **AI Tools:** Context Engineering for AI coding assistants

---

## 3. Key Principles & Constraints

**Design Principles:**

- **Context Engineering:** All docs and code are structured for AI agent and human consumption
- **Modularity:** Files < 2000 LoC, comprehensive unit testing, continuous documentation
- **Reusability:** Easy to fork, rebrand, and deploy for any client or vertical
- **Security-First:** RLS, audit logging, and best practices by default

**Constraints:**

- Use environment variables for all secrets and configuration
- Use only well-vetted, popular libraries with active maintenance
- All code must be formatted with Prettier and follow TypeScript strict mode
- Maintain backward compatibility during refactoring phases
- Document all architectural decisions and business logic

---

## 4. AI Context & Automation

- All docs and code are structured for both human and AI agent (bot) consumption.
- **Jules**: Automates code changes, refactors, and documentation from issues labeled `jules`. See [AI_CONTEXT.md](./AI_CONTEXT.md).
- **coderabbitai**: Provides code suggestions, refactoring, and documentation in PRs and issues.
- **Renovate/Dependabot**: Keep all dependencies up-to-date via automated PRs, triggering CI/CD and auto-merge if safe.
- **Metadata & Embeddings**: Automated scripts generate project metadata and vector embeddings for LLM/AI agent context, updated on every push to main.
- **Vercel/Google Cloud Build**: Automated deploys for preview and production, with status notifications in PRs.
- **All bots are configured in the bypass list for seamless automation.**
- **All workflows and bot actions are documented in [AI_CONTEXT.md](./AI_CONTEXT.md).**

---

## 5. Feature Epics

- See [TASKS.md](./TASKS.md) for granular tasks and progress.
- See [RULES.md](./RULES.md) for coding rules and workflow.
- See [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md) for best practices.

---

## 6. Modular Structure

- `src/app/` — Next.js App Router, pages, layouts
- `src/components/` — UI and domain components
- `src/lib/` — Utilities, Supabase client, helpers
- `public/` — Static assets, logos, images
- `docs/` — Planning, rules, design system, security, backup/migration
- `.github/workflows/` — CI/CD automation

---

## 7. How to Extend

- Add new features as atomic modules/components
- Document all new features in `docs/`
- Update this file with new epics and context as the project evolves
- For rules, see [RULES.md](./RULES.md)
- For design, see [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- For security, see [SECURITY.md](./SECURITY.md)
- For backup/migration, see [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)

---

## 8. References

- [Context Engineering Template](https://github.com/iberi22/context-engineering-template)
- [README.md](../README.md)
- [RULES.md](./RULES.md)
- [TASKS.md](./TASKS.md)
- [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md)
- [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- [SECURITY.md](./SECURITY.md)
- [AI_CONTEXT.md](./AI_CONTEXT.md)
- [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)

---

## Modernization Roadmap (Phased Plan)

> See the [README](../README.md#-plan-de-modernización-y-mejora-continua-faseshitos) for the full modernization plan and phase details.

- **Branding & Documentation:** [DESIGN_SYSTEM.md](./DESIGN_SYSTEM.md), [INDEX.md](./INDEX.md)
- **Architecture & Adapters:** [ARCHITECTURE_SUSTAINABILITY.md](./ARCHITECTURE_SUSTAINABILITY.md)
- **CI/CD & Backups:** [BACKUP_MIGRATION.md](./BACKUP_MIGRATION.md)
- **AI Context & Bots:** [AI_CONTEXT.md](./AI_CONTEXT.md)
- **Onboarding & Sustainability:** [INDEX.md](./INDEX.md)

Each phase is cross-referenced in the relevant manual. All docs follow the [context-engineering-template](https://github.com/iberi22/context-engineering-template) style for modularity and AI-readiness.

---

### Phase 7: Code Quality Automation with Biome

**Objetivo:** Integrar Biome como linter y formateador único en el pipeline de CI/CD para asegurar calidad de código continua y automatizada.

- Biome validará linting y formato en cada PR y push.
- Se automatizarán fixes triviales y se reportarán errores en el pipeline.
- Se documentarán reglas y convenciones para el equipo.
- El objetivo es mantener la calidad y consistencia del código de forma robusta y automatizada.

**Ver detalles y tareas en:** [TASKS.md](./TASKS.md)

---

> This file is the "constitution" for the project. All other docs are referenced here for modularity and clarity.