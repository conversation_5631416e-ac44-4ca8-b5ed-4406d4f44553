"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import { Trash2 } from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// Define types for our course data
interface Instructor {
  first_name: string | null;
  last_name: string | null;
}

interface CertificateTemplate {
  name: string;
}

interface Course {
  id: string;
  title: string;
  description: string;
  status: string;
  code: string | null;
  instructor: Instructor | null;
  certificate_template: CertificateTemplate | null;
  certificateCount: number;
  created_at: string;
  [key: string]: any; // For any other properties
}

export default function CursosPage() {
  const router = useRouter();
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [courseToDelete, setCourseToDelete] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      setError(null);

      // First get all courses with their basic info
      const { data, error } = await supabase
        .from("courses")
        .select(`
          *,
          instructor:instructor_id(first_name, last_name),
          certificate_template:certificate_template_id(name)
        `)
        .order("created_at", { ascending: false });

      if (error) {
        throw error;
      }

      if (!data || data.length === 0) {
        setCourses([]);
        return;
      }

      // Then get certificate counts for each course
      const coursesWithCertificates = await Promise.all(
        data.map(async (course) => {
          const { count, error: countError } = await supabase
            .from("certificates")
            .select("*", { count: "exact", head: true })
            .eq("course_id", course.id);

          if (countError) {
            console.error("Error fetching certificate count:", countError);
            return { ...course, certificateCount: 0 };
          }

          return { ...course, certificateCount: count || 0 };
        })
      );

      setCourses(coursesWithCertificates);
    } catch (error: any) {
      console.error("Error fetching courses:", error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCourses();
  }, []);

  const handleDeleteClick = (e: React.MouseEvent, courseId: string) => {
    e.preventDefault();
    e.stopPropagation();
    setCourseToDelete(courseId);
    setShowDeleteDialog(true);
  };

  const handleDeleteCourse = async () => {
    if (!courseToDelete) return;

    try {
      setIsDeleting(true);

      // Check if course has certificates
      const course = courses.find(c => c.id === courseToDelete);
      if (course && course.certificateCount && course.certificateCount > 0) {
        toast({
          title: "No se puede eliminar",
          description: `Este curso tiene ${course.certificateCount} certificado(s) asociado(s). Debe eliminar los certificados primero.`,
          variant: "destructive",
        });
        setIsDeleting(false);
        setShowDeleteDialog(false);
        return;
      }

      // Double-check certificate count directly from the database
      const { count, error: countError } = await supabase
        .from("certificates")
        .select("*", { count: "exact", head: true })
        .eq("course_id", courseToDelete);

      if (countError) {
        throw countError;
      }

      if (count && count > 0) {
        toast({
          title: "No se puede eliminar",
          description: `Este curso tiene ${count} certificado(s) asociado(s). Debe eliminar los certificados primero.`,
          variant: "destructive",
        });
        setIsDeleting(false);
        setShowDeleteDialog(false);
        return;
      }

      // Delete course
      const { error } = await supabase
        .from("courses")
        .delete()
        .eq("id", courseToDelete);

      if (error) throw error;

      // Update local state
      setCourses(courses.filter(course => course.id !== courseToDelete));

      toast({
        title: "Curso eliminado",
        description: "El curso ha sido eliminado correctamente.",
      });
    } catch (error: any) {
      console.error("Error deleting course:", error);
      toast({
        title: "Error",
        description: "No se pudo eliminar el curso: " + error.message,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setShowDeleteDialog(false);
      setCourseToDelete(null);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge className="bg-green-500">Publicado</Badge>;
      case "draft":
        return <Badge className="bg-yellow-500">Borrador</Badge>;
      case "archived":
        return <Badge className="bg-gray-500">Archivado</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Cursos</h1>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Skeleton key={i} className="h-48 w-full" />
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
          <h3 className="text-lg font-medium">Error al cargar los cursos</h3>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Cursos</h1>
        <Link href="/panel-admin/cursos/nuevo">
          <Button>Nuevo Curso</Button>
        </Link>
      </div>

      {courses.length === 0 ? (
        <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-4">
          <h3 className="text-lg font-medium">No hay cursos</h3>
          <p>No se encontraron cursos. Crea uno nuevo para comenzar.</p>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {courses.map((course) => (
              <div key={course.id} className="relative group">
                <Link href={`/panel-admin/cursos/${course.id}`}>
                  <Card className="h-full cursor-pointer hover:shadow-md transition-shadow">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{course.title}</CardTitle>
                        {getStatusBadge(course.status)}
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-500 line-clamp-2 mb-4">
                        {course.description}
                      </p>
                      <div className="flex flex-col space-y-1 text-sm">
                        <div className="flex justify-between">
                          <span className="font-medium">Instructor:</span>
                          <span>
                            {course.instructor
                              ? `${course.instructor.first_name || 'Nombre no disponible'} ${course.instructor.last_name || 'Apellido no disponible'}`
                              : "No asignado"}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">Código:</span>
                          <span>{course.code || "N/A"}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="font-medium">Plantilla:</span>
                          <span>
                            {course.certificate_template
                              ? course.certificate_template.name
                              : "Predeterminada"}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter className="pt-0 flex justify-end">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700 hover:bg-red-50"
                        onClick={(e) => handleDeleteClick(e, course.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Eliminar
                      </Button>
                    </CardFooter>
                  </Card>
                </Link>
              </div>
            ))}
          </div>

          <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta acción no se puede deshacer. Se eliminará permanentemente el curso seleccionado.
                  {courseToDelete && (() => {
                    const course = courses.find(c => c.id === courseToDelete);
                    if (!course || !course.certificateCount || course.certificateCount <= 0) {
                      return null;
                    }

                    return (
                      <div className="mt-4 p-4 rounded-md" style={{backgroundColor: "#FFEBEE", borderLeft: "4px solid #D32F2F"}}>
                        <div className="flex items-start">
                          <span className="text-xl mr-3">⚠️</span>
                          <div style={{color: "#D32F2F", fontWeight: 500}}>
                            <div className="text-base font-bold mb-1">No se puede eliminar este curso</div>
                            <div>
                              Este curso tiene <strong>{course.certificateCount}</strong> certificado(s) asociado(s).<br />
                              <strong>Debe eliminar los certificados primero.</strong>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })()}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel disabled={isDeleting}>Cancelar</AlertDialogCancel>
                {(() => {
                  const course = courses.find(c => c.id === courseToDelete);
                  const hasCertificates = course?.certificateCount > 0;

                  return (
                    <AlertDialogAction
                      onClick={handleDeleteCourse}
                      disabled={isDeleting || hasCertificates}
                      className={`${hasCertificates
                        ? 'bg-gray-400 hover:bg-gray-400 text-gray-200 cursor-not-allowed opacity-70'
                        : 'bg-red-500 hover:bg-red-600'}`}
                      style={hasCertificates ? {pointerEvents: 'none'} : {}}
                    >
                      {isDeleting ? "Eliminando..." : "Eliminar"}
                    </AlertDialogAction>
                  );
                })()}
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </>
      )}
    </div>
  );
}
