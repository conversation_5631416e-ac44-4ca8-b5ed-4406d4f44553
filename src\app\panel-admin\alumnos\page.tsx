"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { supabase, tableExists } from "@/lib/supabase"; // Removed getDatabaseSchema
import { useSearchParams } from "next/navigation";
import { ImportUsersButton } from "@/components/users/ImportUsersButton";
import { ImportPreview } from "@/components/users/ImportPreview";

// Define types for User (representing a student)
type User = {
  id: string;
  full_name?: string; // Will be constructed
  first_name?: string; // From users table metadata or raw_user_meta_data
  last_name?: string; // From users table metadata or raw_user_meta_data
  email: string; // From auth.users
  role: string; // From users table
  created_at: string; // From auth.users
  // last_sign_in_at: string | null; // Removed - Fetched securely if needed via trigger/API
  certificate_count: number; // Calculated
  identity_document?: string; // RUT or other identity document
};

export default function StudentManagement() {
  // Estado
  const [users, setUsers] = useState<User[]>([]); // Renamed state
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Estados para la importación de usuarios
  const [showImportPreview, setShowImportPreview] = useState(false);
  const [importData, setImportData] = useState<any[]>([]);

  // Obtener parámetros de URL para mostrar mensajes
  const searchParams = useSearchParams();
  const created = searchParams.get("created");

  // We know the table is 'users'
  const userTableName = 'users';
  // Field names might be in raw_user_meta_data if not directly in users table
  const firstNameField = 'first_name';
  const lastNameField = 'last_name';

  // Simplified: Assume 'users' table exists. Check on fetch error instead.
  useEffect(() => {
     // Check if 'users' table exists on initial load for better error message
     async function checkUserTable() {
        setLoading(true);
        const hasUsers = await tableExists(userTableName);
        if (!hasUsers) {
            console.error(`Error: La tabla requerida '${userTableName}' no existe.`);
            setError(`Error crítico: La tabla '${userTableName}' no se encontró en la base de datos.`);
            setLoading(false);
        } else {
            // If table exists, proceed to fetch data (handled by the next useEffect)
            // We set loading false here, the fetch effect will set it true again
            setLoading(false);
        }
     }
     checkUserTable();
  }, []); // Run only once on mount

  // Mostrar mensaje de éxito si viene de creación
  useEffect(() => {
    if (created === "true") {
      setSuccessMessage("El alumno ha sido registrado exitosamente");

      // Ocultar el mensaje después de 5 segundos
      const timer = setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [created]);

  // Función para cargar usuarios con rol 'student'
  const fetchStudentUsers = async () => {
    // Don't fetch if table check failed earlier
    if (error?.includes('Error crítico')) {
        setLoading(false);
        return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log(`Cargando usuarios con rol 'student' desde tabla: ${userTableName}`);

      // Step 1: Get IDs of users with role 'student' from the public.users table
      const { data: studentIdsData, error: studentIdsError } = await supabase
        .from(userTableName)
        .select("id")
        .eq("role", "student");

      if (studentIdsError) {
        console.error(`Error fetching student IDs from ${userTableName}:`, studentIdsError);
        throw new Error(`Error fetching student IDs: ${studentIdsError.message}`);
      }

      const studentIds = studentIdsData?.map(u => u.id) || [];

      if (studentIds.length === 0) {
        console.log(`No users with role 'student' found in ${userTableName}`);
        setUsers([]);
        setLoading(false);
        return;
      }

      // Step 2: Get user metadata (like names) from public.users table
      // We already have IDs, fetch corresponding metadata.
      // Avoid fetching auth.users data directly on the client for security.
      const { data: usersPublicData, error: publicDataError } = await supabase
        .from(userTableName)
        // Select ID, name fields, and identity_document from public.users
        .select(`id, ${firstNameField}, ${lastNameField}, identity_document`)
        .in('id', studentIds);

      if (publicDataError) {
         console.error(`Error fetching public user data for names:`, publicDataError);
         // Non-critical error, proceed without names if necessary
         // throw new Error(`Error fetching public user data: ${publicDataError.message}`);
      }

      // Create a map for easy lookup of public data by ID
      const publicDataMap = new Map(usersPublicData?.map(u => [u.id, u]) || []);

      // We will now iterate through the studentIds we confirmed have the 'student' role
      const studentAuthData = studentIds.map(id => ({ id })); // Create placeholder objects with IDs

      // Check if we found corresponding auth data
      if (!studentAuthData || studentAuthData.length === 0) {
        console.log(`No corresponding auth data found for student IDs: ${studentIds.join(', ')}`);
        setUsers([]);
        setLoading(false);
        return; // Exit if no matching auth users found
      }

      console.log(`Se encontraron ${studentAuthData.length} usuarios de autenticación con rol 'student'`);

      // Comprobar existencia de tabla de certificados
      const certificatesExist = await tableExists('certificates');

      // Procesar cada estudiante con sus datos completos
      const processedUsers: User[] = []; // Renamed variable

      for (const idObj of studentAuthData) { // Iterate over the IDs
        const userId = idObj.id;
        const publicData = publicDataMap.get(userId);
        // Extract data for the user
        const userData = {
          id: userId,
          // Email and created_at are not fetched from public.users in this step
          email: 'No disponible (cliente)', // Indicate email is not available client-side
          role: 'student',
          created_at: new Date().toISOString(), // Use placeholder or remove if not needed
          // Extract names and identity_document from the public data we fetched
          first_name: publicData?.[firstNameField] || '',
          last_name: publicData?.[lastNameField] || '',
          identity_document: publicData?.identity_document || '',
        };

        // Construct full name
        const fullName = userData.first_name && userData.last_name
          ? `${userData.first_name} ${userData.last_name}`
          : userData.first_name || userData.last_name || userData.email; // Fallback to email

        // Contar certificados
        let certificateCount = 0;

        if (certificatesExist) {
          try {
            const { count, error: certError } = await supabase
              .from("certificates")
              .select("id", { count: "exact", head: true })
              .eq("user_id", userData.id);

            if (!certError && count !== null) {
              certificateCount = count;
            }
          } catch (err) {
            console.warn(`Error al contar certificados para ${userData.id}:`, err);
          }
        }

        // Add processed user
        processedUsers.push({
          ...userData,
          full_name: fullName,
          certificate_count: certificateCount,
        });
      }

      console.log("Procesamiento de usuarios (rol student) completado");
      setUsers(processedUsers); // Update state with processed users
    } catch (error) {
      console.error("Error en el proceso de fetchStudentUsers:", error);
      setError(error instanceof Error ? error.message : "Error procesando los usuarios");
    } finally {
      setLoading(false);
    }
  };

  // Fetch users with role 'student' on component mount
  useEffect(() => {
    fetchStudentUsers();
  }, [error]); // Re-run if the initial table check error changes (e.g., gets cleared)

  // Filter users based on search term
  const filteredUsers = users.filter((user) => { // Use 'users' state
    const searchLower = searchTerm.toLowerCase();
    const fullName = user.full_name || '';
    const documentoIdentidad = user.identity_document || '';
    return (
      fullName.toLowerCase().includes(searchLower) ||
      user.email.toLowerCase().includes(searchLower) ||
      documentoIdentidad.toLowerCase().includes(searchLower)
    );
  });

  return (
    <div>
      {/* Header with actions */}
      <div className="md:flex md:items-center md:justify-between mb-6">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Gestión de Alumnos
          </h2>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <div className="flex space-x-3">
            <ImportUsersButton
              onImportComplete={(results) => {
                if (results && results.users) {
                  setImportData(results.users);
                  setShowImportPreview(true);
                }
              }}
            />
            <Link
              href="/panel-admin/alumnos/nuevo"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Nuevo Alumno
            </Link>
          </div>
        </div>
      </div>

      {/* Success message */}
      {successMessage && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Search box */}
      <div className="mb-6">
        <div className="mt-1 relative rounded-md shadow-sm">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg
              className="h-5 w-5 text-gray-400"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              aria-hidden="true"
            >
              <path
                fillRule="evenodd"
                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <input
            type="text"
            name="search"
            id="search"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
            placeholder="Buscar por nombre, email o RUT..."
          />
        </div>
      </div>

      {/* Students list */}
      <div className="flex flex-col">
        <div className="-my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
          <div className="py-2 align-middle inline-block min-w-full sm:px-6 lg:px-8">
            <div className="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
              {loading ? (
                <div className="flex justify-center items-center h-64">
                  <p className="text-gray-500">Cargando usuarios...</p>
                </div>
              ) : filteredUsers.length === 0 ? ( // Use filteredUsers
                <div className="text-center py-12 px-6">
                  {searchTerm ? (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        No se encontraron usuarios
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        No hay usuarios que coincidan con &quot;{searchTerm}&quot;. Intenta con otra búsqueda.
                      </p>
                    </div>
                  ) : (
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">
                        No hay usuarios con rol &apos;student&apos; registrados
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Comienza registrando un nuevo usuario con el rol &apos;student&apos;.
                      </p>
                      <div className="mt-6">
                        <Link
                          href="/panel-admin/alumnos/nuevo"
                          className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                          Nuevo Alumno
                        </Link>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Alumno
                      </th>
                      {/* Removed Fecha de Registro column as created_at is not reliably available */}
                      {/* Removed Último Acceso column */}
                      {/* Columna de Documento de Identidad eliminada */}
                      <th
                        scope="col"
                        className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                      >
                        Certificados
                      </th>
                      <th scope="col" className="relative px-6 py-3">
                        <span className="sr-only">Acciones</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredUsers.map((user) => ( // Use filteredUsers and user
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                <span className="text-indigo-800 font-medium">
                                  {(user.full_name || user.email).charAt(0).toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.full_name || 'Sin nombre'}
                              </div>
                              <div className="text-sm text-gray-500">
                                {user.identity_document ? (
                                  <span>RUT: {user.identity_document}</span>
                                ) : (
                                  <span>{user.id}</span>
                                )}
                              </div>
                            </div>
                          </div>
                        </td>
                        {/* Removed cell for Fecha de Registro */}
                        {/* Removed cell for Último Acceso */}
                        {/* Celda de Documento de Identidad eliminada */}
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {user.certificate_count}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <Link
                            href={`/panel-admin/alumnos/${user.id}`}
                            className="text-indigo-600 hover:text-indigo-900 mr-4"
                          >
                            Ver
                          </Link>
                          <Link
                            href={`/panel-admin/alumnos/editar/${user.id}`}
                            className="text-indigo-600 hover:text-indigo-900"
                          >
                            Editar
                          </Link>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Modal de vista previa de importación */}
      {showImportPreview && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 overflow-y-auto">
          <ImportPreview
            data={importData}
            onCancel={() => setShowImportPreview(false)}
            onConfirm={async () => {
              try {
                // Filtrar solo usuarios válidos
                const validUsers = importData.filter(user => user.status === 'valid');

                // Enviar solicitud para confirmar la importación
                const response = await fetch('/api/admin/confirm-import-users', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                  },
                  body: JSON.stringify({ users: validUsers }),
                });

                if (!response.ok) {
                  const errorData = await response.json();
                  throw new Error(errorData.error || 'Error al confirmar la importación');
                }

                const result = await response.json();

                // Cerrar el modal
                setShowImportPreview(false);

                // Mostrar mensaje de éxito
                setSuccessMessage(`Se importaron ${result.imported} usuarios correctamente.`);

                // Ocultar el mensaje después de 5 segundos
                setTimeout(() => {
                  setSuccessMessage(null);
                }, 5000);

                // Recargar la lista de usuarios
                fetchStudentUsers();
              } catch (error: any) {
                console.error("Error al confirmar la importación:", error);
                throw error;
              }
            }}
          />
        </div>
      )}
    </div>
  );
}