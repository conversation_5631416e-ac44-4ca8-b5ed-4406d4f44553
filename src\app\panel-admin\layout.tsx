"use client";

import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { supabase } from "@/lib/supabase";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Este efecto asegura que el componente solo se renderice en el cliente
  // para evitar errores de hidratación
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    await supabase.auth.signOut();
    router.push("/login");
  };

  // Si no está montado, devolvemos un div vacío para evitar errores de hidratación
  if (!mounted) {
    return <div className="min-h-screen bg-background"></div>;
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="bg-primary pb-32">
        <nav className="bg-primary border-b border-primary/25 lg:border-none">
          <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
            <div className="relative h-16 flex items-center justify-between lg:border-b lg:border-primary-foreground/25">
              <div className="px-2 flex items-center lg:px-0">
                <div className="flex-shrink-0">
                  <Link
                    href="/panel-admin"
                    className="text-primary-foreground font-bold text-xl"
                  >
                    QR CURSE | Admin
                  </Link>
                </div>
                <div className="hidden lg:block lg:ml-10">
                  <div className="flex space-x-4">
                    <Link
                      href="/panel-admin"
                      className={`${
                        pathname === "/panel-admin"
                          ? "bg-primary-foreground/20 text-primary-foreground"
                          : "text-primary-foreground hover:bg-primary-foreground/10"
                      } rounded-md py-2 px-3 text-sm font-medium transition-colors`}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/panel-admin/alumnos"
                      className={`${
                        pathname === "/panel-admin/alumnos"
                          ? "bg-primary-foreground/20 text-primary-foreground"
                          : "text-primary-foreground hover:bg-primary-foreground/10"
                      } rounded-md py-2 px-3 text-sm font-medium transition-colors`}
                    >
                      Alumnos
                    </Link>
                    <Link
                      href="/panel-admin/certificados"
                      className={`${
                        pathname === "/panel-admin/certificados"
                          ? "bg-primary-foreground/20 text-primary-foreground"
                          : "text-primary-foreground hover:bg-primary-foreground/10"
                      } rounded-md py-2 px-3 text-sm font-medium transition-colors`}
                    >
                      Certificados
                    </Link>
                    {/* <Link
                      href="/panel-admin/notas-asistencia"
                      className={`${
                        pathname === "/panel-admin/notas-asistencia"
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Notas y Asistencia
                    </Link> */}
                    <Link
                      href="/panel-admin/cursos"
                      className={`${
                        pathname.startsWith("/panel-admin/cursos")
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Cursos
                    </Link>
                    <Link
                      href="/panel-admin/instructores"
                      className={`${
                        pathname.startsWith("/panel-admin/instructores")
                          ? "bg-primary-foreground/20 text-primary-foreground"
                          : "text-primary-foreground hover:bg-primary-foreground/10"
                      } rounded-md py-2 px-3 text-sm font-medium transition-colors`}
                    >
                      Instructores
                    </Link>
                    {/* <Link
                      href="/panel-admin/plantillas"
                      className={`${
                        pathname.startsWith("/panel-admin/plantillas")
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Plantillas
                    </Link> */}
                  </div>
                </div>
              </div>
              <div className="flex lg:hidden">
                {/* Mobile menu button */}
                <button
                  type="button"
                  className="bg-primary p-2 rounded-md inline-flex items-center justify-center text-primary-foreground/70 hover:text-primary-foreground hover:bg-primary-foreground/10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary focus:ring-primary-foreground transition-colors"
                  aria-expanded="false"
                  aria-controls="mobile-menu"
                  aria-label="Abrir menú principal"
                >
                  <svg
                    className="block h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </button>
              </div>
              <div className="hidden lg:block lg:ml-4">
                <div className="flex items-center space-x-4">
                  <ThemeToggle
                    variant="ghost"
                    className="text-primary-foreground hover:bg-primary-foreground/10"
                  />
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className="flex-shrink-0 bg-primary p-1 text-primary-foreground/70 rounded-full hover:text-primary-foreground focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-primary focus:ring-primary-foreground transition-colors"
                  >
                    <span className="sr-only">Cerrar sesión</span>
                    <svg
                      className="h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </nav>
        <header className="py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-3xl font-bold text-primary-foreground">
              {pathname === "/panel-admin" && "Panel de Administración"}
              {pathname === "/panel-admin/alumnos" && "Gestión de Alumnos"}
              {pathname === "/panel-admin/certificados" &&
                "Gestión de Certificados"}
              {pathname === "/panel-admin/notas-asistencia" &&
                "Notas y Asistencia"}
              {pathname.startsWith("/panel-admin/cursos") &&
                "Gestión de Cursos"}
              {pathname.startsWith("/panel-admin/instructores") &&
                "Gestión de Instructores"}
              {pathname.startsWith("/panel-admin/plantillas") &&
                "Plantillas de Certificados"}
            </h1>
          </div>
        </header>
      </div>

      <main className="-mt-32">
        <div className="max-w-7xl mx-auto pb-12 px-4 sm:px-6 lg:px-8">
          <div className="bg-card rounded-lg shadow-lg border px-5 py-6 sm:px-6">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}
