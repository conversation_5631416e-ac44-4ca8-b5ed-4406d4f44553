"use client";

import { useParams } from 'next/navigation';
import dynamic from 'next/dynamic';

// Importar el componente de forma dinámica para evitar problemas de SSR con styled-components
const CertificateViewer = dynamic(
  () => import('@/components/certificates/CertificateViewer'),
  { ssr: false, loading: () => <div className="min-h-screen flex items-center justify-center">Cargando certificado...</div> }
);

export default function CertificadoPage() {
  const params = useParams();
  const certificateId = params.id as string;

  return <CertificateViewer certificateId={certificateId} showHeader={false} />;
}
