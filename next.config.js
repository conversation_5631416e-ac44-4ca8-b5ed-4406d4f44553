/** @type {import('next').NextConfig} */
const path = require('path');

const nextConfig = {
  env: {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || "https://mrgcukvyvivpsacohdqh.supabase.co",
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im1yZ2N1a3Z5dml2cHNhY29oZHFoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDE4NTI2MDIsImV4cCI6MjA1NzQyODYwMn0.UHN1JujAjGStmJ37gVRKgRoAwhiJHShzV7RU4cy5t-4",
  },
  reactStrictMode: true,
  images: {
    domains: ["mrgcukvyvivpsacohdqh.supabase.co"],
  },
  // Desactivar completamente el linting durante el build
  eslint: {
    ignoreDuringBuilds: true,
    dirs: [], // No ejecutar ESLint en ningún directorio
  },
  // Ignorar errores de TypeScript durante el build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Configuración del compilador SWC
  compiler: {
    // Habilitar la eliminación de console.log en producción
    removeConsole: process.env.NODE_ENV === 'production',
    // Configuración de styledComponents
    styledComponents: true,
  },
  // Desactivar comprobaciones estrictas
  experimental: {
    strictNextHead: false,
    // Asegurar que se use SWC para next/font
    forceSwcTransforms: true,
  },
  // Configuración de webpack para resolver rutas de importación
  webpack: (config, { isServer }) => {
    // Configuración de alias para resolver rutas absolutas
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.join(__dirname, './src'),
    };

    return config;
  },
};

module.exports = nextConfig;
