# 🚀 Developer Onboarding Guide - DOMUS OTEC

¡Bienvenido al equipo de desarrollo de DOMUS OTEC! Esta guía te ayudará a configurar tu entorno de desarrollo y comenzar a contribuir al proyecto de manera efectiva.

## 📋 Tabla de Contenidos

- [Requisitos Previos](#requisitos-previos)
- [Configuración del Entorno](#configuración-del-entorno)
- [Arquitectura del Proyecto](#arquitectura-del-proyecto)
- [Flujo de Desarrollo](#flujo-de-desarrollo)
- [Estándares de Código](#estándares-de-código)
- [Testing](#testing)
- [Deployment](#deployment)
- [Recursos Adicionales](#recursos-adicionales)

## 🔧 Requisitos Previos

### Software Requerido

- **Node.js**: Versión 18.17 o superior
- **npm**: Versión 9 o superior (incluido con Node.js)
- **Git**: Para control de versiones
- **VS Code**: Editor recomendado con extensiones específicas

### Cuentas Necesarias

- **GitHub**: Para acceso al repositorio
- **Supabase**: Para desarrollo con base de datos
- **Vercel**: Para deployment (opcional para desarrollo)

### Extensiones de VS Code Recomendadas

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

## ⚙️ Configuración del Entorno

### 1. Clonar el Repositorio

```bash
# Clonar el repositorio
git clone https://github.com/iberi22/scaffolding-curses-nextjs-supabase.git
cd domus-otec

# Configurar upstream (si es un fork)
git remote add upstream https://github.com/iberi22/scaffolding-curses-nextjs-supabase.git
```

### 2. Instalar Dependencias

```bash
# Instalar dependencias del proyecto
npm install

# Verificar instalación
npm run --version
```

### 3. Configurar Variables de Entorno

```bash
# Copiar archivo de ejemplo
cp .env.example .env.local

# Editar variables de entorno
# Solicitar valores al team lead o consultar documentación interna
```

**Variables de Entorno Requeridas:**

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="DOMUS OTEC"

# Development
NODE_ENV=development
```

### 4. Configurar Base de Datos

```bash
# Instalar Supabase CLI (opcional pero recomendado)
npm install -g supabase

# Inicializar Supabase localmente (opcional)
supabase init
supabase start
```

### 5. Ejecutar el Proyecto

```bash
# Modo desarrollo
npm run dev

# El proyecto estará disponible en http://localhost:3000
```

### 6. Verificar Configuración

```bash
# Ejecutar tests
npm test

# Verificar linting
npm run lint

# Verificar tipos
npm run type-check

# Verificar build
npm run build
```

## 🏗️ Arquitectura del Proyecto

### Estructura de Directorios

```
domus-otec/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── (auth)/            # Rutas de autenticación
│   │   ├── (dashboard)/       # Rutas del dashboard
│   │   ├── api/               # API routes
│   │   └── globals.css        # Estilos globales
│   ├── components/            # Componentes React
│   │   ├── ui/               # Componentes base (Shadcn/UI)
│   │   ├── dashboard/        # Componentes del dashboard
│   │   └── theme/            # Sistema de temas
│   ├── lib/                  # Lógica de negocio
│   │   ├── services/         # Capa de servicios
│   │   ├── repositories/     # Capa de datos
│   │   ├── types/           # Definiciones TypeScript
│   │   └── utils/           # Utilidades
│   └── __tests__/           # Tests y utilidades de testing
├── docs/                    # Documentación del proyecto
├── scripts/                 # Scripts de automatización
└── .github/                # Workflows de CI/CD
```

### Patrones Arquitectónicos

1. **Clean Architecture**: Separación clara entre capas
2. **Service Layer Pattern**: Lógica de negocio centralizada
3. **Repository Pattern**: Abstracción de acceso a datos
4. **Component Composition**: Componentes reutilizables
5. **Custom Hooks**: Lógica compartida en React

### Stack Tecnológico

- **Frontend**: Next.js 14, React 18, TypeScript
- **UI**: Shadcn/UI, Radix UI, Tailwind CSS
- **Backend**: Next.js API Routes, Supabase
- **Database**: PostgreSQL (via Supabase)
- **Testing**: Jest, React Testing Library
- **Deployment**: Vercel

## 🔄 Flujo de Desarrollo

### 1. Crear Nueva Feature

```bash
# Crear rama desde main
git checkout main
git pull upstream main
git checkout -b feature/nombre-de-la-feature

# Ejemplo:
git checkout -b feature/user-profile-management
```

### 2. Desarrollo

1. **Escribir Tests Primero** (TDD recomendado)
2. **Implementar Funcionalidad**
3. **Actualizar Documentación**
4. **Verificar Estándares de Código**

### 3. Commit y Push

```bash
# Verificar cambios
git status
git diff

# Agregar cambios
git add .

# Commit con mensaje descriptivo
git commit -m "feat: add user profile management functionality

- Add UserProfile component with form validation
- Implement updateProfile service method
- Add unit tests for profile update flow
- Update API documentation"

# Push a rama remota
git push origin feature/nombre-de-la-feature
```

### 4. Pull Request

1. **Crear PR** desde GitHub
2. **Completar Template** de PR
3. **Solicitar Review** de al menos 1 desarrollador
4. **Resolver Comentarios** si los hay
5. **Merge** después de aprobación

### Convenciones de Commit

Seguimos [Conventional Commits](https://www.conventionalcommits.org/):

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**Tipos:**
- `feat`: Nueva funcionalidad
- `fix`: Corrección de bug
- `docs`: Cambios en documentación
- `style`: Cambios de formato (no afectan lógica)
- `refactor`: Refactoring de código
- `test`: Agregar o modificar tests
- `chore`: Tareas de mantenimiento

## 📝 Estándares de Código

### TypeScript

```typescript
// ✅ Bueno: Interfaces bien definidas
interface UserProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: 'admin' | 'instructor' | 'student';
}

// ✅ Bueno: Funciones con tipos explícitos
async function updateUserProfile(
  userId: string, 
  data: Partial<UserProfile>
): Promise<ServiceResponse<UserProfile>> {
  // Implementation
}
```

### React Components

```tsx
// ✅ Bueno: Componente funcional con props tipadas
interface UserCardProps {
  user: UserProfile;
  onEdit?: (user: UserProfile) => void;
  className?: string;
}

export function UserCard({ user, onEdit, className }: UserCardProps) {
  return (
    <Card className={cn("p-4", className)}>
      <CardHeader>
        <CardTitle>{user.firstName} {user.lastName}</CardTitle>
        <CardDescription>{user.email}</CardDescription>
      </CardHeader>
      {onEdit && (
        <CardFooter>
          <Button onClick={() => onEdit(user)}>
            Editar
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
```

### Servicios

```typescript
// ✅ Bueno: Servicio que extiende BaseService
export class UserService extends BaseService {
  async updateProfile(
    userId: string, 
    data: UpdateProfileRequest
  ): Promise<ServiceResponse<UserProfile>> {
    // Validación
    const validation = this.validateRequired(userId, 'userId');
    if (validation) return validation;

    try {
      // Lógica de negocio
      const updatedUser = await this.repositories.users.update(userId, data);
      return this.success(updatedUser, 'Profile updated successfully');
    } catch (error) {
      return this.error('UPDATE_FAILED', 'Failed to update profile', error);
    }
  }
}
```

### Estilos con Tailwind

```tsx
// ✅ Bueno: Clases organizadas y responsivas
<div className="
  flex flex-col gap-4 p-6
  md:flex-row md:gap-6 md:p-8
  lg:gap-8 lg:p-12
  bg-background border rounded-lg
  hover:shadow-md transition-shadow
">
  <div className="flex-1">
    <h2 className="text-xl font-semibold mb-2">Title</h2>
    <p className="text-muted-foreground">Description</p>
  </div>
</div>
```

## 🧪 Testing

### Estructura de Tests

```
src/
├── components/
│   └── __tests__/
│       └── user-card.test.tsx
├── lib/
│   ├── services/
│   │   └── __tests__/
│   │       └── user-service.test.ts
│   └── utils/
│       └── __tests__/
│           └── validation.test.ts
└── __tests__/
    ├── integration/
    └── utils/
```

### Ejemplo de Test de Componente

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { UserCard } from '../user-card';
import { TestWrapper } from '@/__tests__/utils/test-utils';

describe('UserCard', () => {
  const mockUser = {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: 'student' as const,
  };

  it('should render user information', () => {
    render(
      <TestWrapper>
        <UserCard user={mockUser} />
      </TestWrapper>
    );

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should call onEdit when edit button is clicked', () => {
    const mockOnEdit = jest.fn();
    
    render(
      <TestWrapper>
        <UserCard user={mockUser} onEdit={mockOnEdit} />
      </TestWrapper>
    );

    fireEvent.click(screen.getByText('Editar'));
    expect(mockOnEdit).toHaveBeenCalledWith(mockUser);
  });
});
```

### Comandos de Testing

```bash
# Ejecutar todos los tests
npm test

# Ejecutar tests en modo watch
npm run test:watch

# Ejecutar tests con coverage
npm run test:coverage

# Ejecutar tests específicos
npm test -- --testPathPattern="user-service"
```

## 🚀 Deployment

### Desarrollo Local

```bash
# Servidor de desarrollo
npm run dev

# Build de producción local
npm run build
npm start
```

### Staging y Producción

El deployment se maneja automáticamente via GitHub Actions:

1. **Push a `develop`** → Deploy a Staging
2. **Push a `main`** → Deploy a Production
3. **Pull Request** → Preview deployment

### Variables de Entorno por Ambiente

- **Development**: `.env.local`
- **Staging**: Configuradas en Vercel
- **Production**: Configuradas en Vercel

## 📚 Recursos Adicionales

### Documentación Interna

- [PLANNING.md](../PLANNING.md) - Arquitectura y decisiones técnicas
- [API Documentation](../api/) - Documentación de APIs
- [Component Guide](../components/) - Guía de componentes UI
- [Testing Guide](../testing/) - Estrategias de testing

### Documentación Externa

- [Next.js Documentation](https://nextjs.org/docs)
- [React Documentation](https://react.dev)
- [Supabase Documentation](https://supabase.com/docs)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Shadcn/UI](https://ui.shadcn.com)

### Herramientas de Desarrollo

- [React DevTools](https://react.dev/learn/react-developer-tools)
- [Supabase Dashboard](https://supabase.com/dashboard)
- [Vercel Dashboard](https://vercel.com/dashboard)

### Comunidad y Soporte

- **Slack/Discord**: Canal de desarrollo (solicitar acceso)
- **GitHub Issues**: Para reportar bugs o solicitar features
- **Code Reviews**: Proceso de revisión de código
- **Daily Standups**: Reuniones diarias del equipo

## 🎯 Próximos Pasos

1. **Completar configuración** siguiendo esta guía
2. **Revisar issues abiertos** en GitHub
3. **Elegir primera tarea** (buscar label `good-first-issue`)
4. **Unirse a canales de comunicación** del equipo
5. **Programar sesión de onboarding** con team lead

---

¡Bienvenido al equipo! Si tienes preguntas, no dudes en contactar al team lead o crear un issue en GitHub.

**Team Lead**: [Nombre] - [<EMAIL>]
**Slack/Discord**: #domus-otec-dev
