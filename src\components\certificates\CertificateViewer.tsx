"use client";

import { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Skeleton } from '@/components/ui/skeleton';
import PrintableCertificate from '@/components/certificates/PrintableCertificate';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Printer, QrCode } from 'lucide-react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface CertificateViewerProps {
  certificateId: string;
  showHeader?: boolean;
  onBack?: () => void;
  backUrl?: string;
}

export default function CertificateViewer({
  certificateId,
  showHeader = false,
  onBack,
  backUrl = '/verificar-certificado'
}: CertificateViewerProps) {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificateData, setCertificateData] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [courseData, setCourseData] = useState<any>(null);
  const [instructorData, setInstructorData] = useState<any>(null);
  const [templateData, setTemplateData] = useState<any>(null);

  useEffect(() => {
    async function fetchCertificateData() {
      if (!certificateId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch certificate details
        const { data: certificate, error: certError } = await supabase
          .from('certificates')
          .select('*')
          .eq('id', certificateId)
          .single();

        if (certError || !certificate) {
          console.error('Error al obtener certificado:', certError);
          throw new Error('No se encontró el certificado');
        }

        setCertificateData(certificate);

        // Fetch user data
        const { data: user, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', certificate.user_id)
          .single();

        if (userError || !user) {
          console.error('Error al obtener datos del usuario:', userError);
          throw new Error('No se encontró la información del estudiante');
        }

        setUserData(user);

        // Fetch course data
        const { data: course, error: courseError } = await supabase
          .from('courses')
          .select('*, instructor_id')
          .eq('id', certificate.course_id)
          .single();

        if (courseError || !course) {
          console.error('Error al obtener datos del curso:', courseError);
          throw new Error('No se encontró la información del curso');
        }

        setCourseData(course);

        // Fetch instructor data
        let instructorSignatureUrl = null;

        if (course.instructor_id) {
          const { data: instructor, error: instructorError } = await supabase
            .from('users')
            .select('first_name, last_name')
            .eq('id', course.instructor_id)
            .single();

          if (!instructorError && instructor) {
            setInstructorData(instructor);

            // Try to get instructor signature from instructors table
            const instructorName = `${instructor.first_name} ${instructor.last_name}`;
            const { data: instructorDetails, error: instructorDetailsError } = await supabase
              .from('instructors')
              .select('signature_url')
              .eq('name', instructorName)
              .single();

            if (!instructorDetailsError && instructorDetails && instructorDetails.signature_url) {
              instructorSignatureUrl = instructorDetails.signature_url;
            }
          } else {
            console.warn('No se pudo obtener información del instructor:', instructorError);
            // No lanzamos error aquí, ya que el instructor no es crítico
          }
        }

        // Fetch template data - first try to get course-specific template
        let templateFound = false;

        try {
          // Check if course has a specific template assigned
          if (course.certificate_template_id) {
            const { data: template, error: templateError } = await supabase
              .from('certificate_templates')
              .select('*')
              .eq('id', course.certificate_template_id)
              .single();

            if (!templateError && template) {
              setTemplateData(template);
              templateFound = true;
            } else {
              console.warn('No se encontró la plantilla específica del curso:', templateError);
            }
          }

          // If no course-specific template, get the default template
          if (!templateFound) {
            const { data: defaultTemplate, error: defaultTemplateError } = await supabase
              .from('certificate_templates')
              .select('*')
              .eq('is_default', true)
              .single();

            if (!defaultTemplateError && defaultTemplate) {
              setTemplateData(defaultTemplate);
              templateFound = true;
            } else {
              console.warn('No se encontró plantilla predeterminada:', defaultTemplateError);

              // If no default template found, try to get any template
              const { data: anyTemplate, error: anyTemplateError } = await supabase
                .from('certificate_templates')
                .select('*')
                .limit(1)
                .single();

              if (!anyTemplateError && anyTemplate) {
                setTemplateData(anyTemplate);
                templateFound = true;
              } else {
                console.warn('No se encontró ninguna plantilla:', anyTemplateError);
              }
            }
          }
        } catch (templateError) {
          console.error('Error al buscar plantillas:', templateError);
          // No lanzamos error aquí, usaremos la plantilla predeterminada del componente
        }
      } catch (error: any) {
        console.error('Error fetching certificate data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    }

    fetchCertificateData();
  }, [certificateId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-gray-600">Cargando certificado...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white">
        <section className="hero-gradient w-full py-12">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-xl mx-auto text-center">
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">
                Verificación de Certificado
              </h1>
              <p className="text-md md:text-lg text-white/90 mb-4 leading-relaxed">
                Sistema de validación de certificaciones profesionales
              </p>
            </div>
          </div>
        </section>

        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <Card className="max-w-xl mx-auto shadow-xl border border-red-200 overflow-hidden">
              <CardHeader className="text-center bg-red-50">
                <CardTitle className="text-xl font-bold text-red-600">Error al verificar el certificado</CardTitle>
                <CardDescription className="text-gray-700">
                  No se pudo encontrar o verificar el certificado solicitado.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <Alert variant="destructive" className="mb-6">
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Error</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
                <div className="flex justify-center">
                  {onBack ? (
                    <Button
                      onClick={onBack}
                      className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" /> Volver
                    </Button>
                  ) : (
                    <Button
                      asChild
                      className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                    >
                      <Link href={backUrl}>
                        <ArrowLeft className="mr-2 h-4 w-4" /> Volver
                      </Link>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  if (!certificateData || !userData || !courseData) {
    return (
      <div className="min-h-screen bg-white">
        <section className="hero-gradient w-full py-12">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-xl mx-auto text-center">
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">
                Verificación de Certificado
              </h1>
              <p className="text-md md:text-lg text-white/90 mb-4 leading-relaxed">
                Sistema de validación de certificaciones profesionales
              </p>
            </div>
          </div>
        </section>

        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <Card className="max-w-xl mx-auto shadow-xl border border-yellow-200 overflow-hidden">
              <CardHeader className="text-center bg-yellow-50">
                <CardTitle className="text-xl font-bold text-yellow-600">Información incompleta</CardTitle>
                <CardDescription className="text-gray-700">
                  No se pudo cargar toda la información necesaria para mostrar el certificado.
                </CardDescription>
              </CardHeader>
              <CardContent className="p-6">
                <Alert className="mb-6 bg-yellow-50 border-yellow-300 text-yellow-800">
                  <AlertDescription>
                    Verifique que el certificado existe y que tiene los permisos necesarios para acceder a él.
                  </AlertDescription>
                </Alert>
                <div className="flex justify-center">
                  {onBack ? (
                    <Button
                      onClick={onBack}
                      className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" /> Volver
                    </Button>
                  ) : (
                    <Button
                      asChild
                      className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                    >
                      <Link href={backUrl}>
                        <ArrowLeft className="mr-2 h-4 w-4" /> Volver
                      </Link>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  const instructorName = instructorData
    ? `${instructorData.first_name} ${instructorData.last_name}`
    : 'Instructor DOMUS';

  // Función para procesar la plantilla HTML con los datos del certificado
  const processTemplate = (html: string) => {
    try {
      if (!html) return "<div>No hay contenido HTML en esta plantilla</div>";

      // Definir los datos para las variables de la plantilla
      const templateVariables = {
        participantName: `${userData.first_name} ${userData.last_name}`,
        courseTitle: courseData.title,
        certificateNumber: certificateData.certificate_number,
        completionDate: formatDate(certificateData.issue_date),
        duration: courseData.duration || "8 Horas",
        instructorName: instructorName,
        issuingAuthority: "DOMUS OTEC",
        documentNumber: userData.identity_document || userData.document_number || userData.rut || '',
        logo: '<img src="/logo.svg" alt="DOMUS Logo" style="width: 100%; height: 100%; object-fit: contain;" />',
        qrCode: certificateData.qr_code ?
          `<img src="${certificateData.qr_code}" alt="Código QR de verificación" style="max-width: 100%; max-height: 100%; object-fit: contain; display: block;" />` : '',
        instructorSignature: instructorSignatureUrl ?
          `<img src="${instructorSignatureUrl}" alt="Firma del instructor" style="max-width: 150px; max-height: 60px; object-fit: contain; display: block; margin: 0 auto;" />` : ''
      };

      // Reemplazar todas las variables en la plantilla
      let processedHtml = html;
      Object.entries(templateVariables).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        processedHtml = processedHtml.replace(regex, value as string);
      });

      return processedHtml;
    } catch (error: any) {
      console.error("Error processing template:", error);
      return `<div class="error">Error al procesar la plantilla: ${error.message || 'Error desconocido'}</div>`;
    }
  };

  // Estilos adicionales para asegurar que el certificado se ajuste correctamente
  const additionalStyles = `
    .certificate-container {
      width: 100%;
      max-width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow-x: hidden;
      padding: 0;
      margin: 0;
    }
    .certificate-content {
      width: 100%;
      max-width: 100%;
      height: auto;
      box-sizing: border-box;
    }
    /* Estilos para el certificado */
    .certificate {
      width: 100% !important;
      max-width: 100% !important;
      height: auto !important;
      margin: 0 auto !important;
      box-sizing: border-box !important;
    }
    /* Asegurar que las imágenes se ajusten correctamente */
    img {
      max-width: 100%;
      height: auto;
    }
    /* Asegurar que las tablas se ajusten correctamente */
    table {
      width: 100%;
      max-width: 100%;
    }
    /* Ajustar el tamaño del título para que sea proporcional al logo */
    .title {
      font-size: 3.5em !important;
      margin-top: 150px !important;
      margin-bottom: 20px !important;
      letter-spacing: 1px !important;
    }
    /* Aumentar el tamaño del logo */
    .logo {
      width: 180px !important;
      height: 100px !important;
    }
    /* Asegurar que el QR se muestre correctamente */
    .qr-code {
      overflow: hidden !important;
      box-sizing: border-box !important;
    }
    .qr-code > div.qr-image {
      width: 120px !important;
      height: 120px !important;
      display: flex !important;
      justify-content: center !important;
      align-items: center !important;
      overflow: hidden !important;
      margin-bottom: 5px !important;
    }
    .qr-code img {
      max-width: 100% !important;
      max-height: 100% !important;
      object-fit: contain !important;
      display: block !important;
    }
    .qr-text {
      font-size: 11px !important;
      text-align: center !important;
      font-weight: bold !important;
      color: #219bf9 !important;
      background-color: white !important;
      padding: 2px 5px !important;
      border-radius: 3px !important;
      width: 100% !important;
      white-space: nowrap !important;
      overflow: hidden !important;
      text-overflow: ellipsis !important;
    }
    /* Mejorar el estilo del nombre del estudiante */
    .participant-name {
      font-size: 2.5em !important;
      color: #007bff !important;
      font-weight: bold !important;
      margin: 15px 0 !important;
      text-transform: uppercase !important;
      letter-spacing: 1px !important;
    }
    /* Mejorar el estilo del documento de identidad */
    .document-number {
      font-size: 1.4em !important;
      color: #444 !important;
      font-weight: bold !important;
      border: 1px solid #ddd !important;
      display: inline-block !important;
      padding: 5px 15px !important;
      border-radius: 5px !important;
      background-color: rgba(255, 255, 255, 0.7) !important;
      margin-bottom: 25px !important;
    }
    /* Mejorar el estilo del título del curso */
    .course-title {
      font-size: 1.8em !important;
      color: #333 !important;
      text-align: center !important;
      margin-bottom: 30px !important;
      font-weight: bold !important;
      padding: 0 20px !important;
    }
    /* Estilos para impresión */
    @media print {
      body {
        margin: 0;
        padding: 0;
      }
      .print-button, .print-hidden {
        display: none !important;
      }
      nav, header, footer {
        display: none !important;
      }
    }
  `;

  return (
    <div className="min-h-screen bg-white">
      <style jsx global>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
          }
          .container {
            margin: 0;
            padding: 0;
            max-width: none;
            width: 100%;
          }
          nav, header, footer, .print-hidden, .hero-gradient {
            display: none !important;
          }
        }
      `}</style>

      {showHeader && (
        <section className="hero-gradient w-full py-12">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">
                Certificado Verificado
              </h1>
              <p className="text-md md:text-lg text-white/90 mb-4 leading-relaxed">
                Certificación oficial emitida por DOMUS OTEC
              </p>
            </div>
          </div>
        </section>
      )}

      <section className="py-8 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {showHeader && (
              <>
                <div className="mb-6 flex justify-between items-center print-hidden">
                  {onBack ? (
                    <Button
                      onClick={onBack}
                      className="border-primary/50 text-primary hover:bg-primary/5"
                      variant="outline"
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" /> Volver
                    </Button>
                  ) : (
                    <Button variant="outline" asChild className="border-primary/50 text-primary hover:bg-primary/5">
                      <Link href={backUrl}>
                        <ArrowLeft className="mr-2 h-4 w-4" /> Volver
                      </Link>
                    </Button>
                  )}
                  <div className="flex gap-2">
                    <Button
                      className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                      onClick={() => {
                        // Ocultar elementos que no deben imprimirse
                        document.querySelectorAll('.print-hidden').forEach(el => {
                          (el as HTMLElement).style.display = 'none';
                        });
                        // Dar tiempo para que se apliquen los estilos
                        setTimeout(() => {
                          window.print();
                          // Restaurar después de imprimir
                          setTimeout(() => {
                            document.querySelectorAll('.print-hidden').forEach(el => {
                              (el as HTMLElement).style.display = '';
                            });
                          }, 500);
                        }, 300);
                      }}
                    >
                      <Printer className="mr-2 h-4 w-4" /> Imprimir
                    </Button>
                    {certificateData.qr_code && (
                      <Button variant="outline" asChild className="border-primary/50 text-primary hover:bg-primary/5">
                        <Link href={`/verificar-certificado/qr/${certificateData.id}`} target="_blank">
                          <QrCode className="mr-2 h-4 w-4" /> Ver QR
                        </Link>
                      </Button>
                    )}
                  </div>
                </div>

                <Card className="mb-8 print-hidden shadow-lg border border-border/50 overflow-hidden">
                  <CardHeader className="bg-primary/5 border-b border-border/50">
                    <CardTitle className="text-xl text-primary">Certificado Verificado</CardTitle>
                    <CardDescription>
                      Este certificado ha sido emitido por DOMUS OTEC para {userData.first_name} {userData.last_name}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <h3 className="text-sm font-medium text-primary mb-3">Información del Profesional</h3>
                        <div className="space-y-2">
                          <div>
                            <p className="text-sm font-medium text-gray-600">Nombre:</p>
                            <p className="text-sm text-gray-900 font-semibold">{userData.first_name} {userData.last_name}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-600">RUT:</p>
                            <p className="text-sm text-gray-900">{userData.identity_document || userData.document_number || userData.rut || 'No disponible'}</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100">
                        <h3 className="text-sm font-medium text-primary mb-3">Información del Certificado</h3>
                        <div className="space-y-2">
                          <div>
                            <p className="text-sm font-medium text-gray-600">Número:</p>
                            <p className="text-sm text-gray-900 font-semibold">{certificateData.certificate_number}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-600">Fecha de emisión:</p>
                            <p className="text-sm text-gray-900">{formatDate(certificateData.issue_date)}</p>
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-600">Estado:</p>
                            <p className="text-sm text-gray-900">{certificateData.status === 'ACTIVE' || certificateData.status === 'active' ? 'Activo' :
                              certificateData.status === 'REVOKED' || certificateData.status === 'revoked' ? 'Revocado' :
                              certificateData.status === 'EXPIRED' || certificateData.status === 'expired' ? 'Expirado' :
                              certificateData.status}</p>
                          </div>
                        </div>
                      </div>
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-100 md:col-span-2">
                        <h3 className="text-sm font-medium text-primary mb-3">Información del Curso</h3>
                        <div className="space-y-2">
                          <div>
                            <p className="text-sm font-medium text-gray-600">Curso:</p>
                            <p className="text-sm text-gray-900 font-semibold">{courseData.title}</p>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <p className="text-sm font-medium text-gray-600">Duración:</p>
                              <p className="text-sm text-gray-900">{courseData.duration || "8 Horas"}</p>
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-600">Instructor:</p>
                              <p className="text-sm text-gray-900">{instructorName}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}

            <div className="bg-white rounded-lg shadow-xl overflow-hidden">
              {/* Si tenemos una plantilla personalizada, la usamos */}
              {templateData && templateData.html_template ? (
                <div className="bg-white certificate-container" style={{ minHeight: 'auto' }}>
                  <style dangerouslySetInnerHTML={{ __html: templateData.css_styles + additionalStyles }} />
                  <div className="certificate-content" dangerouslySetInnerHTML={{ __html: processTemplate(templateData.html_template) }} />
                </div>
              ) : (
                <PrintableCertificate
                  logoSrc="/logo.svg"
                  courseTitle={courseData.title}
                  participantName={`${userData.first_name} ${userData.last_name}`}
                  documentNumber={userData.identity_document || userData.document_number || userData.rut}
                  duration={courseData.duration || "8 Horas"}
                  completionDate={formatDate(certificateData.issue_date)}
                  certificateNumber={certificateData.certificate_number}
                  instructorName={instructorName}
                  issuingAuthority="DOMUS OTEC"
                  backgroundImageSrc="/certificate-bg.png"
                  sealImageSrc="/certificate-seal.png"
                  qrCodeUrl={certificateData.qr_code || certificateData.qr_code_url}
                  signatureUrl={instructorSignatureUrl}
                />
              )}
            </div>

            {!showHeader && (
              <div className="flex justify-center mt-6 print-hidden">
                <Button
                  onClick={() => {
                    document.querySelectorAll('.print-hidden').forEach(el => {
                      (el as HTMLElement).style.display = 'none';
                    });
                    setTimeout(() => {
                      window.print();
                      setTimeout(() => {
                        document.querySelectorAll('.print-hidden').forEach(el => {
                          (el as HTMLElement).style.display = '';
                        });
                      }, 500);
                    }, 300);
                  }}
                  className="btn-gradient text-white px-6 py-2.5 rounded-lg font-medium shadow-lg whitespace-nowrap transition-all duration-300"
                >
                  <Printer className="mr-2 h-5 w-5" /> Imprimir Certificado
                </Button>
              </div>
            )}

            {showHeader && (
              <div className="mt-8 text-center print-hidden">
                <p className="text-sm text-gray-500 mb-4">
                  Este certificado valida las competencias y habilidades adquiridas en programas de formación profesional acreditados.
                </p>
                <Button
                  className="btn-gradient text-white px-6 py-2.5 rounded-lg font-medium shadow-lg"
                  onClick={() => {
                    document.querySelectorAll('.print-hidden').forEach(el => {
                      (el as HTMLElement).style.display = 'none';
                    });
                    setTimeout(() => {
                      window.print();
                      setTimeout(() => {
                        document.querySelectorAll('.print-hidden').forEach(el => {
                          (el as HTMLElement).style.display = '';
                        });
                      }, 500);
                    }, 300);
                  }}
                >
                  <Printer className="mr-2 h-5 w-5" /> Imprimir Certificado
                </Button>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}
