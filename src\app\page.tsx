"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function Home() {
  return (
    <main className="min-h-screen flex flex-col items-center justify-center bg-csi-white text-csi-dark">
      <section className="w-full max-w-2xl text-center py-16 px-4">
        <h1 className="text-4xl md:text-5xl font-heading font-bold mb-4 text-csi-blue">
          🚀 CSI Online Course & Certification Platform
        </h1>
        <p className="text-lg md:text-xl font-body mb-8 text-csi-dark">
          Launch, manage, and certify your workforce with a modern, robust, and AI-ready platform. Built for rapid deployment and easy rebranding.
        </p>
        <a href="#get-started" className="button-primary inline-block text-lg">
          Get Started
        </a>
      </section>
      <section className="w-full max-w-3xl text-center py-8 px-4 bg-csi-blue text-csi-white rounded-lg shadow-lg mb-8">
        <h2 className="text-2xl font-heading font-bold mb-2">About CSI Ltda.</h2>
        <p className="font-body mb-2">
          CSI Ltda. is a leader in fuel and lubricant services, committed to operational excellence, safety, and sustainability.
        </p>
        <a href="https://www.csiltda.cl/" target="_blank" rel="noopener" className="underline text-csi-yellow font-bold">Learn more</a>
      </section>
      <section className="w-full max-w-2xl text-center py-8 px-4">
        <h3 className="text-xl font-heading font-bold mb-2 text-csi-blue">Why this Scaffold?</h3>
        <ul className="list-disc list-inside text-left mx-auto font-body text-csi-dark mb-4">
          <li>⚡ Rapid setup for any company or institution</li>
          <li>🎨 Easy rebranding (colors, fonts, logos)</li>
          <li>🔐 Secure, scalable, and multi-tenant ready</li>
          <li>🤖 Designed for AI coding agents and automation</li>
          <li>🧩 Extensible with payments, notifications, analytics, and more</li>
        </ul>
        <p className="font-body text-csi-dark">Fork, brand, and deploy your own course platform in minutes!</p>
      </section>
    </main>
  );
}
