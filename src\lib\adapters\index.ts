/**
 * Database Adapter Factory
 * 
 * This file provides the main entry point for database adapters.
 * It creates and configures the appropriate adapter based on environment configuration.
 * 
 * This abstraction allows the application to switch between different database providers
 * (Supabase, PostgreSQL, etc.) without changing business logic.
 */

import { AppAdapter } from './database/types';
import { createSupabaseAdapter } from './database/supabase-adapter';

// ============================================================================
// Configuration
// ============================================================================

interface AdapterConfig {
  provider: 'supabase' | 'postgresql' | 'mysql';
  url: string;
  key?: string;
  options?: Record<string, any>;
}

// ============================================================================
// Adapter Factory
// ============================================================================

/**
 * Creates the appropriate database adapter based on configuration
 */
export function createDatabaseAdapter(config?: AdapterConfig): AppAdapter {
  // Default to Supabase if no config provided
  const provider = config?.provider || 'supabase';
  
  switch (provider) {
    case 'supabase':
      return createSupabaseAdapter(
        config?.url || process.env.NEXT_PUBLIC_SUPABASE_URL!,
        config?.key || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
    
    case 'postgresql':
      // Future implementation for direct PostgreSQL
      throw new Error('PostgreSQL adapter not yet implemented');
    
    case 'mysql':
      // Future implementation for MySQL
      throw new Error('MySQL adapter not yet implemented');
    
    default:
      throw new Error(`Unsupported database provider: ${provider}`);
  }
}

// ============================================================================
// Default Adapter Instance
// ============================================================================

/**
 * Default adapter instance using environment configuration
 * This is the main adapter used throughout the application
 */
export const db = createDatabaseAdapter();

// ============================================================================
// Convenience Exports
// ============================================================================

// Export the individual adapters for direct access
export const database = db.database;
export const auth = db.auth;
export const storage = db.storage;

// Export types for use in business logic
export * from './database/types';

// ============================================================================
// Migration Helpers
// ============================================================================

/**
 * Helper function to gradually migrate from direct Supabase usage
 * This allows for incremental adoption of the adapter pattern
 */
export function migrateFromSupabase() {
  console.warn(
    'Direct Supabase usage detected. Consider migrating to the adapter pattern for better maintainability.'
  );
}

/**
 * Validation helper to ensure adapter is properly configured
 */
export function validateAdapter(adapter: AppAdapter): boolean {
  try {
    // Basic validation - ensure all required methods exist
    const requiredMethods = {
      database: ['select', 'selectOne', 'insert', 'update', 'delete', 'rpc'],
      auth: ['signIn', 'signUp', 'signOut', 'getSession', 'getUser'],
      storage: ['upload', 'download', 'delete', 'getPublicUrl']
    };

    for (const [service, methods] of Object.entries(requiredMethods)) {
      const serviceAdapter = adapter[service as keyof AppAdapter];
      for (const method of methods) {
        if (typeof (serviceAdapter as any)[method] !== 'function') {
          console.error(`Missing method ${method} in ${service} adapter`);
          return false;
        }
      }
    }

    return true;
  } catch (error) {
    console.error('Adapter validation failed:', error);
    return false;
  }
}

// Validate the default adapter on initialization
if (process.env.NODE_ENV === 'development') {
  if (!validateAdapter(db)) {
    console.error('Default database adapter validation failed');
  }
}
