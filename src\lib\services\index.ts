/**
 * Service Factory and Exports
 *
 * This file provides a centralized way to create and access all services.
 * It implements the factory pattern to ensure consistent service creation
 * and provides a single point of access for all business logic services.
 */

import { RepositoryFactory, repositories } from '../repositories';

// Service imports
import { BaseService } from './base-service';
import { UserService } from './user-service';
import { CertificateService } from './certificate-service';
import { AuthService } from './auth-service';

// Additional service interfaces (to be implemented)
export interface CourseServiceInterface {
  createCourse(data: any): Promise<any>;
  getCourseById(id: string): Promise<any>;
  updateCourse(id: string, data: any): Promise<any>;
  deleteCourse(id: string): Promise<any>;
}

export interface GradeServiceInterface {
  createGrade(data: any): Promise<any>;
  getGradesByUser(userId: string): Promise<any>;
  updateGrade(id: string, data: any): Promise<any>;
}

export interface AttendanceServiceInterface {
  recordAttendance(data: any): Promise<any>;
  getAttendanceByUser(userId: string): Promise<any>;
  updateAttendance(id: string, data: any): Promise<any>;
}

// Placeholder service implementations (to be fully implemented later)
class CourseService extends BaseService implements CourseServiceInterface {
  async createCourse(data: any): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.courses.create(data);
      },
      'Failed to create course'
    );
  }

  async getCourseById(id: string): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        const course = await this.repositories.courses.findById(id);
        if (!course) {
          throw new Error('Course not found');
        }
        return course;
      },
      'Failed to get course'
    );
  }

  async updateCourse(id: string, data: any): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.courses.update(id, data);
      },
      'Failed to update course'
    );
  }

  async deleteCourse(id: string): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.courses.delete(id);
      },
      'Failed to delete course'
    );
  }
}

class GradeService extends BaseService implements GradeServiceInterface {
  async createGrade(data: any): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.grades.create(data);
      },
      'Failed to create grade'
    );
  }

  async getGradesByUser(userId: string): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.grades.findByUser(userId);
      },
      'Failed to get grades by user'
    );
  }

  async updateGrade(id: string, data: any): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.grades.update(id, data);
      },
      'Failed to update grade'
    );
  }
}

class AttendanceService extends BaseService implements AttendanceServiceInterface {
  async recordAttendance(data: any): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.attendance.create(data);
      },
      'Failed to record attendance'
    );
  }

  async getAttendanceByUser(userId: string): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.attendance.findByUser(userId);
      },
      'Failed to get attendance by user'
    );
  }

  async updateAttendance(id: string, data: any): Promise<any> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.attendance.update(id, data);
      },
      'Failed to update attendance'
    );
  }
}

// ============================================================================
// Service Factory
// ============================================================================

export class ServiceFactory {
  private authService?: AuthService;
  private userService?: UserService;
  private certificateService?: CertificateService;
  private courseService?: CourseService;
  private gradeService?: GradeService;
  private attendanceService?: AttendanceService;

  constructor(private repositories: RepositoryFactory) {}

  // ============================================================================
  // Service Getters (Singleton Pattern)
  // ============================================================================

  get auth(): AuthService {
    if (!this.authService) {
      this.authService = new AuthService(this.repositories);
    }
    return this.authService;
  }

  get users(): UserService {
    if (!this.userService) {
      this.userService = new UserService(this.repositories);
    }
    return this.userService;
  }

  get certificates(): CertificateService {
    if (!this.certificateService) {
      this.certificateService = new CertificateService(this.repositories);
    }
    return this.certificateService;
  }

  get courses(): CourseService {
    if (!this.courseService) {
      this.courseService = new CourseService(this.repositories);
    }
    return this.courseService;
  }

  get grades(): GradeService {
    if (!this.gradeService) {
      this.gradeService = new GradeService(this.repositories);
    }
    return this.gradeService;
  }

  get attendance(): AttendanceService {
    if (!this.attendanceService) {
      this.attendanceService = new AttendanceService(this.repositories);
    }
    return this.attendanceService;
  }

  // ============================================================================
  // Utility Methods
  // ============================================================================

  /**
   * Reset all service instances (useful for testing)
   */
  reset(): void {
    this.authService = undefined;
    this.userService = undefined;
    this.certificateService = undefined;
    this.courseService = undefined;
    this.gradeService = undefined;
    this.attendanceService = undefined;
  }

  /**
   * Get all service instances
   */
  getAllServices(): {
    auth: AuthService;
    users: UserService;
    certificates: CertificateService;
    courses: CourseService;
    grades: GradeService;
    attendance: AttendanceService;
  } {
    return {
      auth: this.auth,
      users: this.users,
      certificates: this.certificates,
      courses: this.courses,
      grades: this.grades,
      attendance: this.attendance
    };
  }
}

// ============================================================================
// Default Service Factory Instance
// ============================================================================

/**
 * Default service factory using the default repository factory
 */
export const services = new ServiceFactory(repositories);

// ============================================================================
// Convenience Exports
// ============================================================================

// Export individual services for direct access
export const authService = services.auth;
export const userService = services.users;
export const certificateService = services.certificates;
export const courseService = services.courses;
export const gradeService = services.grades;
export const attendanceService = services.attendance;

// Export service classes for custom instantiation
export {
  BaseService,
  AuthService,
  UserService,
  CertificateService,
  CourseService,
  GradeService,
  AttendanceService
};

// Export types
export * from './base-service';
export * from './auth-service';
export * from './user-service';
export * from './certificate-service';

// ============================================================================
// Migration Helpers
// ============================================================================

/**
 * Helper function to create a service factory with custom repositories
 * Useful for testing or when using different repository configurations
 */
export function createServiceFactory(repositories: RepositoryFactory): ServiceFactory {
  return new ServiceFactory(repositories);
}

/**
 * Helper function to validate all services are properly configured
 */
export function validateServices(factory: ServiceFactory): boolean {
  try {
    const services = factory.getAllServices();

    // Basic validation - ensure all services are instantiated
    const requiredServices = ['auth', 'users', 'certificates', 'courses', 'grades', 'attendance'];

    for (const serviceName of requiredServices) {
      const service = services[serviceName as keyof typeof services];
      if (!service) {
        console.error(`Service ${serviceName} is not properly instantiated`);
        return false;
      }
    }

    return true;
  } catch (error) {
    console.error('Service validation failed:', error);
    return false;
  }
}

// ============================================================================
// Service Health Check
// ============================================================================

/**
 * Perform a health check on all services
 */
export async function performServiceHealthCheck(): Promise<{
  healthy: boolean;
  services: Record<string, boolean>;
  errors: string[];
}> {
  const serviceHealth: Record<string, boolean> = {};
  const errors: string[] = [];

  try {
    // Test basic service instantiation
    const allServices = services.getAllServices();

    for (const [serviceName, service] of Object.entries(allServices)) {
      try {
        // Basic health check - ensure service has required methods
        if (typeof service.constructor === 'function') {
          serviceHealth[serviceName] = true;
        } else {
          serviceHealth[serviceName] = false;
          errors.push(`Service ${serviceName} is not properly constructed`);
        }
      } catch (error: any) {
        serviceHealth[serviceName] = false;
        errors.push(`Service ${serviceName} health check failed: ${error.message}`);
      }
    }

    const healthy = Object.values(serviceHealth).every(status => status);

    return {
      healthy,
      services: serviceHealth,
      errors
    };
  } catch (error: any) {
    return {
      healthy: false,
      services: serviceHealth,
      errors: [`Global service health check failed: ${error.message}`]
    };
  }
}

// Validate services on initialization in development
if (process.env.NODE_ENV === 'development') {
  if (!validateServices(services)) {
    console.error('Default services validation failed');
  }
}
