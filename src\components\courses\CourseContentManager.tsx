"use client";

import React, { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/use-toast";
import { PlusCircle, Trash2, ArrowUpCircle, ArrowDownCircle } from "lucide-react";
import { v4 as uuidv4 } from 'uuid';
import * as mcpClient from "@/lib/mcpClient";

interface ContentItem {
  id: string;
  content_id: string;
  description: string;
  order_num: number;
  created_at?: string;
  updated_at?: string;
  isNew?: boolean;
  isDeleted?: boolean;
}

interface CourseContent {
  id: string;
  course_id: string;
  content_type: 'theory' | 'practice' | 'certification';
  title: string;
  description?: string;
  content_items?: ContentItem[];
  isNew?: boolean;
}

interface CourseContentManagerProps {
  courseId: string;
}

const CourseContentManager = React.forwardRef(({ courseId }: CourseContentManagerProps, ref) => {
  const [courseContents, setCourseContents] = useState<CourseContent[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("theory");

  useEffect(() => {
    async function fetchCourseContents() {
      try {
        setLoading(true);
        setError(null);

        if (courseId === "new") {
          // Si es un curso nuevo, inicializar con contenidos vacíos
          setCourseContents([
            {
              id: uuidv4(),
              course_id: courseId,
              content_type: 'theory',
              title: 'Contenido Teórico',
              content_items: [
                {
                  id: uuidv4(),
                  content_id: '',
                  description: '',
                  order_num: 1,
                  isNew: true
                }
              ],
              isNew: true
            },
            {
              id: uuidv4(),
              course_id: courseId,
              content_type: 'practice',
              title: 'Práctica',
              content_items: [
                {
                  id: uuidv4(),
                  content_id: '',
                  description: '',
                  order_num: 1,
                  isNew: true
                }
              ],
              isNew: true
            },
            {
              id: uuidv4(),
              course_id: courseId,
              content_type: 'certification',
              title: 'Certificación',
              content_items: [
                {
                  id: uuidv4(),
                  content_id: '',
                  description: '',
                  order_num: 1,
                  isNew: true
                }
              ],
              isNew: true
            }
          ]);
          return;
        }

        // Usar el MCP Server para obtener los contenidos del curso
        const courseData = await mcpClient.fetchCourseContent(courseId);
        let contents = courseData.content || [];

        // Asegurarse de que existen los tres tipos de contenido
        const contentTypes: ('theory' | 'practice' | 'certification')[] = ['theory', 'practice', 'certification'];

        for (const type of contentTypes) {
          if (!contents.some((c: any) => c.content_type === type)) {
            contents.push({
              id: uuidv4(),
              course_id: courseId,
              content_type: type,
              title: type === 'theory' ? 'Contenido Teórico' : type === 'practice' ? 'Práctica' : 'Certificación',
              content_items: [
                {
                  id: uuidv4(),
                  content_id: '',
                  description: '',
                  order_num: 1,
                  isNew: true
                }
              ],
              isNew: true
            });
          }
        }

        setCourseContents(contents);
      } catch (err: any) {
        console.error("Error fetching course contents:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchCourseContents();
  }, [courseId]);

  const getContentByType = (type: 'theory' | 'practice' | 'certification') => {
    return courseContents.find(content => content.content_type === type) || null;
  };

  const handleAddContentItem = async (contentType: 'theory' | 'practice' | 'certification') => {
    const content = getContentByType(contentType);
    if (!content) return;

    const contentItems = content.content_items || [];
    const newOrder = contentItems.length > 0
      ? Math.max(...contentItems.filter(item => !item.isDeleted).map(item => item.order_num)) + 1
      : 1;

    const newItem: ContentItem = {
      id: uuidv4(),
      content_id: content.id,
      description: '',
      order_num: newOrder,
      isNew: true
    };

    const updatedContents = courseContents.map(c => {
      if (c.content_type === contentType) {
        return {
          ...c,
          content_items: [...(c.content_items || []), newItem]
        };
      }
      return c;
    });

    setCourseContents(updatedContents);

    // Auto-save after adding a new content item
    if (courseId !== "new") {
      try {
        setSaving(true);
        await mcpClient.saveCourseContent(
          courseId,
          updatedContents, // Guardamos los contenidos actualizados
          [], // No estamos guardando objetivos aquí
          [] // No estamos guardando marcos legales aquí
        );
      } catch (err: any) {
        console.error("Error auto-saving after adding content item:", err);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleRemoveContentItem = async (contentType: 'theory' | 'practice' | 'certification', index: number) => {
    const updatedContents = courseContents.map(content => {
      if (content.content_type === contentType && content.content_items) {
        const updatedItems = [...content.content_items];

        if (updatedItems[index].isNew) {
          // Si es un item nuevo, simplemente lo eliminamos del array
          updatedItems.splice(index, 1);
        } else {
          // Si es un item existente, lo marcamos como eliminado
          updatedItems[index] = {
            ...updatedItems[index],
            isDeleted: true
          };
        }

        return {
          ...content,
          content_items: updatedItems
        };
      }
      return content;
    });

    setCourseContents(updatedContents);

    // Auto-save after removing a content item
    if (courseId !== "new") {
      try {
        setSaving(true);
        await mcpClient.saveCourseContent(
          courseId,
          updatedContents, // Guardamos los contenidos actualizados
          [], // No estamos guardando objetivos aquí
          [] // No estamos guardando marcos legales aquí
        );
      } catch (err: any) {
        console.error("Error auto-saving after removing content item:", err);
      } finally {
        setSaving(false);
      }
    }
  };

  // Debounce timer for auto-saving after typing
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const handleContentItemChange = (contentType: 'theory' | 'practice' | 'certification', index: number, value: string) => {
    const updatedContents = courseContents.map(content => {
      if (content.content_type === contentType && content.content_items) {
        const updatedItems = [...content.content_items];
        updatedItems[index] = {
          ...updatedItems[index],
          description: value
        };

        return {
          ...content,
          content_items: updatedItems
        };
      }
      return content;
    });

    setCourseContents(updatedContents);

    // Auto-save after typing with debounce (1.5 seconds)
    if (courseId !== "new") {
      // Clear previous timer if it exists
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set new timer
      const timer = setTimeout(async () => {
        try {
          setSaving(true);
          await mcpClient.saveCourseContent(
            courseId,
            updatedContents, // Guardamos los contenidos actualizados
            [], // No estamos guardando objetivos aquí
            [] // No estamos guardando marcos legales aquí
          );
        } catch (err: any) {
          console.error("Error auto-saving after changing content item:", err);
        } finally {
          setSaving(false);
        }
      }, 1500);

      setDebounceTimer(timer);
    }
  };

  const handleMoveContentItem = (contentType: 'theory' | 'practice' | 'certification', index: number, direction: 'up' | 'down') => {
    const content = getContentByType(contentType);
    if (!content || !content.content_items) return;

    const visibleItems = content.content_items.filter(item => !item.isDeleted);

    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === visibleItems.length - 1)
    ) {
      return;
    }

    const updatedContents = courseContents.map(c => {
      if (c.content_type === contentType && c.content_items) {
        const updatedItems = [...c.content_items];
        const visibleItems = updatedItems.filter(item => !item.isDeleted);

        if (direction === 'up') {
          const prevIndex = updatedItems.indexOf(visibleItems[index - 1]);
          const currentIndex = updatedItems.indexOf(visibleItems[index]);

          // Intercambiar order_num
          const tempOrder = updatedItems[prevIndex].order_num;
          updatedItems[prevIndex].order_num = updatedItems[currentIndex].order_num;
          updatedItems[currentIndex].order_num = tempOrder;
        } else {
          const nextIndex = updatedItems.indexOf(visibleItems[index + 1]);
          const currentIndex = updatedItems.indexOf(visibleItems[index]);

          // Intercambiar order_num
          const tempOrder = updatedItems[nextIndex].order_num;
          updatedItems[nextIndex].order_num = updatedItems[currentIndex].order_num;
          updatedItems[currentIndex].order_num = tempOrder;
        }

        // Ordenar el array por order_num
        updatedItems.sort((a, b) => a.order_num - b.order_num);

        return {
          ...c,
          content_items: updatedItems
        };
      }
      return c;
    });

    setCourseContents(updatedContents);
  };

  const saveContents = async () => {
    if (courseId === "new") {
      // Si es un curso nuevo, no guardamos los contenidos todavía
      return true;
    }

    try {
      setSaving(true);

      // Usar el MCP Server para guardar los contenidos
      await mcpClient.saveCourseContent(
        courseId,
        courseContents, // Guardamos los contenidos
        [], // No estamos guardando objetivos aquí
        [] // No estamos guardando marcos legales aquí
      );

      toast({
        title: "Contenido guardado",
        description: "El contenido del curso se ha guardado correctamente.",
      });

      return true;
    } catch (err: any) {
      console.error("Error saving course content:", err);
      toast({
        title: "Error al guardar",
        description: err.message,
        variant: "destructive"
      });
      return false;
    } finally {
      setSaving(false);
    }
  };

  // Exponer el método saveContents a través de la referencia
  React.useImperativeHandle(ref, () => ({
    saveContents
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Contenido del Curso</CardTitle>
        <CardDescription>
          Define el contenido teórico, práctico y de certificación del curso
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="theory">Teoría</TabsTrigger>
            <TabsTrigger value="practice">Práctica</TabsTrigger>
            <TabsTrigger value="certification">Certificación</TabsTrigger>
          </TabsList>

          <TabsContent value="theory" className="space-y-4">
            {getContentByType('theory')?.content_items
              ?.filter(item => !item.isDeleted)
              .map((item, index) => (
                <div key={item.id} className="flex items-start gap-2">
                  <div className="flex flex-col items-center gap-1 mt-2">
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveContentItem('theory', index, 'up')}
                      disabled={index === 0 || saving}
                      className="h-7 w-7"
                    >
                      <ArrowUpCircle className="h-5 w-5" />
                    </Button>
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveContentItem('theory', index, 'down')}
                      disabled={
                        index === getContentByType('theory')?.content_items?.filter(item => !item.isDeleted).length - 1 ||
                        saving
                      }
                      className="h-7 w-7"
                    >
                      <ArrowDownCircle className="h-5 w-5" />
                    </Button>
                  </div>
                  <div className="flex-1">
                    <Input
                      value={item.description}
                      onChange={(e) => handleContentItemChange('theory', index, e.target.value)}
                      placeholder="Describe el contenido teórico"
                      disabled={saving}
                    />
                  </div>
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    onClick={() => handleRemoveContentItem('theory', index)}
                    disabled={saving}
                    className="h-10 w-10 text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-5 w-5" />
                  </Button>
                </div>
              ))}

            <Button
              type="button"
              variant="outline"
              onClick={() => handleAddContentItem('theory')}
              disabled={saving}
              className="w-full"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Añadir Contenido Teórico
            </Button>
          </TabsContent>

          <TabsContent value="practice" className="space-y-4">
            {getContentByType('practice')?.content_items
              ?.filter(item => !item.isDeleted)
              .map((item, index) => (
                <div key={item.id} className="flex items-start gap-2">
                  <div className="flex flex-col items-center gap-1 mt-2">
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveContentItem('practice', index, 'up')}
                      disabled={index === 0 || saving}
                      className="h-7 w-7"
                    >
                      <ArrowUpCircle className="h-5 w-5" />
                    </Button>
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveContentItem('practice', index, 'down')}
                      disabled={
                        index === getContentByType('practice')?.content_items?.filter(item => !item.isDeleted).length - 1 ||
                        saving
                      }
                      className="h-7 w-7"
                    >
                      <ArrowDownCircle className="h-5 w-5" />
                    </Button>
                  </div>
                  <div className="flex-1">
                    <Input
                      value={item.description}
                      onChange={(e) => handleContentItemChange('practice', index, e.target.value)}
                      placeholder="Describe el contenido práctico"
                      disabled={saving}
                    />
                  </div>
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    onClick={() => handleRemoveContentItem('practice', index)}
                    disabled={saving}
                    className="h-10 w-10 text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-5 w-5" />
                  </Button>
                </div>
              ))}

            <Button
              type="button"
              variant="outline"
              onClick={() => handleAddContentItem('practice')}
              disabled={saving}
              className="w-full"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Añadir Contenido Práctico
            </Button>
          </TabsContent>

          <TabsContent value="certification" className="space-y-4">
            {getContentByType('certification')?.content_items
              ?.filter(item => !item.isDeleted)
              .map((item, index) => (
                <div key={item.id} className="flex items-start gap-2">
                  <div className="flex flex-col items-center gap-1 mt-2">
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveContentItem('certification', index, 'up')}
                      disabled={index === 0 || saving}
                      className="h-7 w-7"
                    >
                      <ArrowUpCircle className="h-5 w-5" />
                    </Button>
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveContentItem('certification', index, 'down')}
                      disabled={
                        index === getContentByType('certification')?.content_items?.filter(item => !item.isDeleted).length - 1 ||
                        saving
                      }
                      className="h-7 w-7"
                    >
                      <ArrowDownCircle className="h-5 w-5" />
                    </Button>
                  </div>
                  <div className="flex-1">
                    <Input
                      value={item.description}
                      onChange={(e) => handleContentItemChange('certification', index, e.target.value)}
                      placeholder="Describe el proceso de certificación"
                      disabled={saving}
                    />
                  </div>
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    onClick={() => handleRemoveContentItem('certification', index)}
                    disabled={saving}
                    className="h-10 w-10 text-red-500 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-5 w-5" />
                  </Button>
                </div>
              ))}

            <Button
              type="button"
              variant="outline"
              onClick={() => handleAddContentItem('certification')}
              disabled={saving}
              className="w-full"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Añadir Contenido de Certificación
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
});

export default CourseContentManager;
