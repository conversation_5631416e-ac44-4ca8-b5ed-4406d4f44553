{"extends": "next/core-web-vitals", "ignorePatterns": ["**/*"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "react-hooks/exhaustive-deps": "off", "react/no-unescaped-entities": "off", "@next/next/no-img-element": "off", "react-hooks/rules-of-hooks": "off", "@typescript-eslint/no-empty-object-type": "off", "react/display-name": "off", "prefer-const": "off"}}