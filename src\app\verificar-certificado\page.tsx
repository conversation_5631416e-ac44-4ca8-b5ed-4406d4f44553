"use client";

import { FormEvent, useState, useEffect, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { supabase } from "@/lib/supabase";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { User, Certificate } from "@/lib/supabase"; // Importar User y Certificate base
import { AlertCircle, CheckCircle, Info, Printer, Search, ExternalLink, ShieldCheck, QrCode } from "lucide-react"; // Added ShieldCheck and QrCode

// Definir el tipo extendido localmente si es necesario, o ajustar el uso
type CertificateWithCourseDetails = Certificate & {
  courses: { title: string } | null;
};


// Componente que usa useSearchParams envuelto en Suspense
function VerificarCertificadoForm() {
  const searchParams = useSearchParams();
  const documentoFromUrl = searchParams.get("documento") || "";
  const certificateIdFromUrl = searchParams.get("id") || "";

  // Estados para búsqueda por documento de identidad (RUT)
  const [documentoIdentidad, setDocumentoIdentidad] = useState(documentoFromUrl);
  const [certificatesList, setCertificatesList] = useState<CertificateWithCourseDetails[]>([]); // Usar el tipo local
  const [student, setStudent] = useState<User | null>(null);
  const [loadingDocumento, setLoadingDocumento] = useState(false);
  const [errorDocumento, setErrorDocumento] = useState<string | null>(null);
  const [searchedDocumento, setSearchedDocumento] = useState(false);

  // Función para buscar certificados por documento de identidad (RUT)
  const searchCertificatesByRut = async (rut: string) => {
    if (!rut) return;

    setLoadingDocumento(true);
    setErrorDocumento(null);
    setCertificatesList([]);
    setStudent(null); // Reset student info on new search
    setSearchedDocumento(true);

    try {
      // Buscar usuario por documento de identidad (RUT)
      const { data: userData, error: userError } = await supabase
        .from("users")
        .select("*") // Seleccionar todos los campos del usuario
        .eq("identity_document", rut)
        .single();

      if (userError || !userData) {
        throw new Error("No se encontró ningún usuario con el RUT proporcionado.");
      }

      setStudent(userData as User);

      // Buscar todos los certificados del usuario, incluyendo la información del curso
      const { data: certificatesData, error: certificatesError } = await supabase
        .from("certificates")
        .select(`
          *,
          courses:course_id (title)
        `)
        .eq("user_id", userData.id)
        .order("issue_date", { ascending: false });

      if (certificatesError) {
        throw new Error("Error al buscar los certificados del usuario.");
      }

      if (!certificatesData || certificatesData.length === 0) {
        // No es un error, simplemente no hay certificados
        setCertificatesList([]);
      } else {
        // Asegurarse de que el casting coincide con la estructura esperada
        setCertificatesList(certificatesData as CertificateWithCourseDetails[]);
      }

    } catch (error: any) {
      console.error("Error buscando certificados por RUT:", error);
      setErrorDocumento(error.message);
      setCertificatesList([]); // Limpiar lista en caso de error
    } finally {
      setLoadingDocumento(false);
    }
  };

  // Función para buscar un certificado específico por ID
  const searchCertificateById = async (id: string) => {
    if (!id) return;

    // Redirigir a la página de detalles del certificado
    window.location.href = `/verificar-certificado/certificado/${id}`;
  };

  // Verificar automáticamente si hay un documento o ID en la URL
  useEffect(() => {
    if (certificateIdFromUrl) {
      searchCertificateById(certificateIdFromUrl);
    } else if (documentoFromUrl) {
      searchCertificatesByRut(documentoFromUrl);
    }
  }, [certificateIdFromUrl, documentoFromUrl]); // Dependencias

  const handleDocumentSubmit = async (e: FormEvent) => {
    e.preventDefault();
    await searchCertificatesByRut(documentoIdentidad);
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString('es-CL', { // Usar locale chileno
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
    switch (status?.toLowerCase()) {
      case 'active': return 'default'; // Verde (o el color por defecto)
      case 'expired': return 'secondary'; // Amarillo/Naranja
      case 'revoked': return 'destructive'; // Rojo
      default: return 'outline'; // Gris
    }
  };

  const getStatusText = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'active': return 'Activo';
      case 'expired': return 'Expirado';
      case 'revoked': return 'Revocado';
      default: return status || 'Desconocido';
    }
  };


  return (
    <div className="min-h-screen bg-white">
      {/* Hero section con gradiente como en la página de inicio */}
      <section className="hero-gradient w-full py-20 md:py-24">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            {/* Logo grande en la parte superior */}
            <div className="flex justify-center mb-6">
              <div className="bg-white rounded-full p-3 shadow-lg">
                <Image
                  src="/images/logo1.svg"
                  alt="DOMUS OTEC Logo"
                  width={200}
                  height={200}
                  className="w-36 h-auto"
                  style={{ filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.15))" }}
                  priority
                />
              </div>
            </div>
            <h1 className="text-3xl md:text-4xl font-bold text-white mb-4 leading-tight">
              Verificación de Certificados
            </h1>
            <p className="text-lg md:text-xl text-white/90 mb-8 leading-relaxed">
              Valide la autenticidad de certificaciones profesionales emitidas por DOMUS OTEC
            </p>

            {/* Card para el formulario de búsqueda */}
            <Card className="max-w-3xl mx-auto shadow-xl border border-white/20 bg-white/10 backdrop-blur-lg">
              <CardHeader className="text-center">
                <div className="flex justify-center items-center mb-2">
                  <ShieldCheck className="w-12 h-12 text-white drop-shadow-md" />
                </div>
                <CardTitle className="text-2xl font-bold text-white">Verificar Certificado</CardTitle>
                <CardDescription className="text-white/90 mt-1">
                  Ingrese el RUT del profesional para verificar sus certificaciones oficiales
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                <form onSubmit={handleDocumentSubmit} className="space-y-4">
                  <div className="flex flex-col sm:flex-row items-end gap-4">
                    <div className="flex-grow space-y-1.5">
                      <Label htmlFor="rut" className="text-sm font-medium text-white">Ingrese el RUT</Label>
                      <Input
                        id="rut"
                        type="text"
                        value={documentoIdentidad}
                        onChange={(e) => setDocumentoIdentidad(e.target.value)}
                        placeholder="Ej: 12345678-9"
                        required
                        className="text-base py-3 px-4 bg-white/20 border-white/30 text-white placeholder:text-white/60 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-transparent"
                      />
                    </div>
                    {/* Botón con estilo btn-gradient como en la página de inicio */}
                    <Button
                      type="submit"
                      disabled={loadingDocumento}
                      className="btn-gradient text-white px-6 py-3 rounded-lg font-medium shadow-lg whitespace-nowrap transition-all duration-300 sm:mb-[1px] hover:shadow-xl"
                    >
                      <Search className="mr-2 h-5 w-5" />
                      {loadingDocumento ? "Buscando..." : "Buscar"}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Sección de información sobre certificaciones */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Resultados de la búsqueda */}
            {searchedDocumento && (
              <div className="mb-12">
                {loadingDocumento ? (
                  <div className="flex justify-center items-center p-10">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
                  </div>
                ) : errorDocumento ? (
                  <Alert variant="destructive" className="shadow-lg border border-destructive/30 mb-8 bg-red-50/50">
                    <AlertCircle className="h-5 w-5" />
                    <AlertTitle className="font-semibold">Error en la Búsqueda</AlertTitle>
                    <AlertDescription>{errorDocumento}</AlertDescription>
                  </Alert>
                ) : student && certificatesList.length > 0 ? (
                  <>
                    <Alert variant="default" className="mb-8 bg-gradient-to-r from-green-50 to-blue-50 border-green-300 text-green-900 dark:bg-green-900/20 dark:border-green-700 dark:text-green-100 shadow-lg">
                      <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                      <AlertTitle className="font-semibold">Búsqueda Exitosa</AlertTitle>
                      <AlertDescription>
                        Se encontraron {certificatesList.length} certificado(s) para el profesional <span className="font-medium">{student.first_name} {student.last_name}</span> (RUT: {student.identity_document}).
                      </AlertDescription>
                    </Alert>

                    <div className="text-center mb-8">
                      <h2 className="text-3xl font-bold text-gray-800 mb-4">Certificados Encontrados</h2>
                      <p className="text-gray-600 max-w-2xl mx-auto">
                        Estos certificados validan las competencias y habilidades adquiridas en programas de formación profesional.
                      </p>
                    </div>

                    <Card className="overflow-hidden shadow-xl border border-border/50 rounded-xl">
                      <CardContent className="p-0">
                        <Table>
                          <TableHeader className="bg-gradient-to-r from-primary/10 to-secondary/10">
                            <TableRow>
                              <TableHead className="py-4 text-primary font-medium">Curso</TableHead>
                              <TableHead className="py-4 text-primary font-medium">N° Certificado</TableHead>
                              <TableHead className="py-4 text-primary font-medium">Fecha Emisión</TableHead>
                              <TableHead className="py-4 text-primary font-medium">Estado</TableHead>
                              <TableHead className="text-right py-4 pr-4 text-primary font-medium">Acciones</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {certificatesList.map((cert) => (
                              <TableRow key={cert.id} className="hover:bg-muted/30 dark:hover:bg-muted/10 transition-colors">
                                <TableCell className="font-medium py-4">{cert.courses?.title ?? 'Curso no especificado'}</TableCell>
                                <TableCell className="py-4">{cert.certificate_number}</TableCell>
                                <TableCell className="py-4">{formatDate(cert.issue_date)}</TableCell>
                                <TableCell className="py-4">
                                  <Badge variant={getStatusBadgeVariant(cert.status)} className="text-xs font-medium px-3 py-1">
                                    {getStatusText(cert.status)}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-right space-x-2 py-4 pr-4">
                                  <Button asChild className="btn-gradient text-white shadow-md hover:shadow-lg transition-all">
                                    <Link href={`/certificado/${cert.id}`} target="_blank" rel="noopener noreferrer" title="Ver/Imprimir Certificado">
                                      <Printer className="mr-1.5 h-4 w-4" /> Ver
                                    </Link>
                                  </Button>
                                  <Button asChild variant="outline" size="sm" className="ml-2 border-primary/30 text-primary hover:bg-primary/5">
                                    <Link href="#" onClick={(e) => {
                                      e.preventDefault();
                                      window.open(`/verificar-certificado/qr/${cert.id}`, '_blank', 'width=500,height=700');
                                    }} title="Ver QR">
                                      <QrCode className="h-4 w-4" />
                                    </Link>
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </CardContent>
                    </Card>
                  </>
                ) : searchedDocumento && !loadingDocumento ? (
                  <Alert variant="default" className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-300 text-blue-900 dark:bg-blue-900/20 dark:border-blue-700 dark:text-blue-100 shadow-lg">
                    <Info className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    <AlertTitle className="font-semibold">Sin Resultados</AlertTitle>
                    <AlertDescription>
                      No se encontraron certificados para el RUT ingresado. Verifique el RUT e intente nuevamente.
                    </AlertDescription>
                  </Alert>
                ) : null}
              </div>
            )}

            {/* Sección de información sobre certificaciones */}
            {!searchedDocumento && (
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div>
                  <h2 className="text-3xl font-bold text-gray-800 mb-6">Certificaciones para Profesionales y Empresas</h2>
                  <p className="text-gray-600 mb-6 leading-relaxed">
                    Nuestras certificaciones están diseñadas para validar las competencias y habilidades requeridas en el ámbito laboral actual, proporcionando un respaldo oficial a la formación recibida.
                  </p>
                  <ul className="space-y-4">
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">Certificados con validación oficial y verificables en línea</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">Cursos especializados para personal y empresas</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">Verificación mediante código QR para mayor seguridad</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-primary mt-1 mr-3 flex-shrink-0" />
                      <span className="text-gray-700">Acceso permanente a su historial de certificaciones</span>
                    </li>
                  </ul>
                </div>
                <div className="bg-gradient-to-br from-primary/10 to-secondary/10 p-8 rounded-xl shadow-lg">
                  <h3 className="text-xl font-semibold text-gray-800 mb-4">¿Por qué verificar un certificado?</h3>
                  <p className="text-gray-600 mb-6">
                    La verificación de certificados garantiza la autenticidad de las credenciales profesionales, asegurando que las habilidades y conocimientos han sido adquiridos a través de programas de formación acreditados.
                  </p>
                  <div className="flex items-center justify-center">
                    <ShieldCheck className="w-24 h-24 text-primary opacity-80" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </section>
    </div>
  );
}

// Componente principal que envuelve VerificarCertificadoForm en Suspense
export default function VerificarCertificado() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
         <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
      </div>
    }>
      <VerificarCertificadoForm />
    </Suspense>
  );
}
