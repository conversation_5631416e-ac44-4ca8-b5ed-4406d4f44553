"use client";

import React, { useState, useEffect } from "react";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { PlusCircle, Trash2, ArrowUpCircle, ArrowDownCircle, Link as LinkIcon } from "lucide-react";
import { v4 as uuidv4 } from 'uuid';
import * as mcpClient from "@/lib/mcpClient";

interface CourseLegalFramework {
  id: string;
  course_id: string;
  description: string;
  url?: string;
  order_num: number;
  created_at?: string;
  updated_at?: string;
  isNew?: boolean;
  isDeleted?: boolean;
}

interface CourseLegalFrameworkManagerProps {
  courseId: string;
}

const CourseLegalFrameworkManager = React.forwardRef(({ courseId }: CourseLegalFrameworkManagerProps, ref) => {
  const [legalFrameworks, setLegalFrameworks] = useState<CourseLegalFramework[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchLegalFrameworks() {
      try {
        setLoading(true);
        setError(null);

        if (courseId === "new") {
          // Si es un curso nuevo, inicializar con un marco legal vacío
          setLegalFrameworks([
            {
              id: uuidv4(),
              course_id: courseId,
              description: "",
              url: "",
              order_num: 1,
              isNew: true
            }
          ]);
          return;
        }

        // Usar el MCP Server para obtener los marcos legales
        const courseData = await mcpClient.fetchCourseContent(courseId);
        setLegalFrameworks(courseData.legalFramework || []);
      } catch (err: any) {
        console.error("Error fetching course legal frameworks:", err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }

    fetchLegalFrameworks();
  }, [courseId]);

  const handleAddLegalFramework = async () => {
    const newOrder = legalFrameworks.length > 0
      ? Math.max(...legalFrameworks.filter(f => !f.isDeleted).map(f => f.order_num)) + 1
      : 1;

    const newLegalFrameworks = [
      ...legalFrameworks,
      {
        id: uuidv4(),
        course_id: courseId,
        description: "",
        url: "",
        order_num: newOrder,
        isNew: true
      }
    ];

    setLegalFrameworks(newLegalFrameworks);

    // Auto-save after adding a new legal framework
    if (courseId !== "new") {
      try {
        setSaving(true);
        await mcpClient.saveCourseContent(
          courseId,
          [], // No estamos guardando contenido aquí
          [], // No estamos guardando objetivos aquí
          newLegalFrameworks // Guardamos los marcos legales actualizados
        );
      } catch (err: any) {
        console.error("Error auto-saving after adding legal framework:", err);
      } finally {
        setSaving(false);
      }
    }
  };

  const handleRemoveLegalFramework = async (index: number) => {
    const updatedLegalFrameworks = [...legalFrameworks];

    if (updatedLegalFrameworks[index].isNew) {
      // Si es un marco legal nuevo, simplemente lo eliminamos del array
      updatedLegalFrameworks.splice(index, 1);
    } else {
      // Si es un marco legal existente, lo marcamos como eliminado
      updatedLegalFrameworks[index] = {
        ...updatedLegalFrameworks[index],
        isDeleted: true
      };
    }

    setLegalFrameworks(updatedLegalFrameworks);

    // Auto-save after removing a legal framework
    if (courseId !== "new") {
      try {
        setSaving(true);
        await mcpClient.saveCourseContent(
          courseId,
          [], // No estamos guardando contenido aquí
          [], // No estamos guardando objetivos aquí
          updatedLegalFrameworks // Guardamos los marcos legales actualizados
        );
      } catch (err: any) {
        console.error("Error auto-saving after removing legal framework:", err);
      } finally {
        setSaving(false);
      }
    }
  };

  // Debounce timer for auto-saving after typing
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const handleLegalFrameworkChange = (index: number, field: 'description' | 'url', value: string) => {
    const updatedLegalFrameworks = [...legalFrameworks];
    updatedLegalFrameworks[index] = {
      ...updatedLegalFrameworks[index],
      [field]: value
    };
    setLegalFrameworks(updatedLegalFrameworks);

    // Auto-save after typing with debounce (1.5 seconds)
    if (courseId !== "new") {
      // Clear previous timer if it exists
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set new timer
      const timer = setTimeout(async () => {
        try {
          setSaving(true);
          await mcpClient.saveCourseContent(
            courseId,
            [], // No estamos guardando contenido aquí
            [], // No estamos guardando objetivos aquí
            updatedLegalFrameworks // Guardamos los marcos legales actualizados
          );
        } catch (err: any) {
          console.error("Error auto-saving after changing legal framework:", err);
        } finally {
          setSaving(false);
        }
      }, 1500);

      setDebounceTimer(timer);
    }
  };

  const handleMoveLegalFramework = (index: number, direction: 'up' | 'down') => {
    if (
      (direction === 'up' && index === 0) ||
      (direction === 'down' && index === legalFrameworks.filter(f => !f.isDeleted).length - 1)
    ) {
      return;
    }

    const updatedLegalFrameworks = [...legalFrameworks];
    const visibleFrameworks = updatedLegalFrameworks.filter(f => !f.isDeleted);

    if (direction === 'up') {
      const prevIndex = updatedLegalFrameworks.indexOf(visibleFrameworks[index - 1]);
      const currentIndex = updatedLegalFrameworks.indexOf(visibleFrameworks[index]);

      // Intercambiar order_num
      const tempOrder = updatedLegalFrameworks[prevIndex].order_num;
      updatedLegalFrameworks[prevIndex].order_num = updatedLegalFrameworks[currentIndex].order_num;
      updatedLegalFrameworks[currentIndex].order_num = tempOrder;
    } else {
      const nextIndex = updatedLegalFrameworks.indexOf(visibleFrameworks[index + 1]);
      const currentIndex = updatedLegalFrameworks.indexOf(visibleFrameworks[index]);

      // Intercambiar order_num
      const tempOrder = updatedLegalFrameworks[nextIndex].order_num;
      updatedLegalFrameworks[nextIndex].order_num = updatedLegalFrameworks[currentIndex].order_num;
      updatedLegalFrameworks[currentIndex].order_num = tempOrder;
    }

    // Ordenar el array por order_num
    updatedLegalFrameworks.sort((a, b) => a.order_num - b.order_num);

    setLegalFrameworks(updatedLegalFrameworks);
  };

  const saveLegalFrameworks = async () => {
    if (courseId === "new") {
      // Si es un curso nuevo, no guardamos los marcos legales todavía
      return true;
    }

    try {
      setSaving(true);

      // Usar el MCP Server para guardar los marcos legales
      await mcpClient.saveCourseContent(
        courseId,
        [], // No estamos guardando contenido aquí
        [], // No estamos guardando objetivos aquí
        legalFrameworks // Solo guardamos los marcos legales
      );

      toast({
        title: "Marco legal guardado",
        description: "El marco legal del curso se ha guardado correctamente.",
      });

      return true;
    } catch (err: any) {
      console.error("Error saving course legal framework:", err);
      toast({
        title: "Error al guardar",
        description: err.message,
        variant: "destructive"
      });
      return false;
    } finally {
      setSaving(false);
    }
  };

  // Exponer el método saveLegalFrameworks a través de la referencia
  React.useImperativeHandle(ref, () => ({
    saveLegalFrameworks
  }));

  return (
    <Card>
      <CardHeader>
        <CardTitle>Marco Legal</CardTitle>
        <CardDescription>
          Define las normativas y leyes aplicables a este curso
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {legalFrameworks
            .filter(framework => !framework.isDeleted)
            .map((framework, index) => (
              <div key={framework.id} className="space-y-2">
                <div className="flex items-start gap-2">
                  <div className="flex flex-col items-center gap-1 mt-2">
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveLegalFramework(index, 'up')}
                      disabled={index === 0 || saving}
                      className="h-7 w-7"
                    >
                      <ArrowUpCircle className="h-5 w-5" />
                    </Button>
                    <Button
                      type="button"
                      size="icon"
                      variant="ghost"
                      onClick={() => handleMoveLegalFramework(index, 'down')}
                      disabled={index === legalFrameworks.filter(f => !f.isDeleted).length - 1 || saving}
                      className="h-7 w-7"
                    >
                      <ArrowDownCircle className="h-5 w-5" />
                    </Button>
                  </div>
                  <div className="flex-1">
                    <Input
                      value={framework.description}
                      onChange={(e) => handleLegalFrameworkChange(index, 'description', e.target.value)}
                      placeholder="Describe la normativa o ley aplicable"
                      disabled={saving}
                      className="mb-2"
                    />
                    <div className="flex items-center gap-2">
                      <LinkIcon className="h-4 w-4 text-gray-400" />
                      <Input
                        value={framework.url || ''}
                        onChange={(e) => handleLegalFrameworkChange(index, 'url', e.target.value)}
                        placeholder="URL de la normativa (opcional)"
                        disabled={saving}
                      />
                    </div>
                  </div>
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    onClick={() => handleRemoveLegalFramework(index)}
                    disabled={saving}
                    className="h-10 w-10 text-red-500 hover:text-red-700 hover:bg-red-50 mt-2"
                  >
                    <Trash2 className="h-5 w-5" />
                  </Button>
                </div>
              </div>
            ))}

          <Button
            type="button"
            variant="outline"
            onClick={handleAddLegalFramework}
            disabled={saving}
            className="w-full"
          >
            <PlusCircle className="h-4 w-4 mr-2" />
            Añadir Marco Legal
          </Button>
        </div>
      </CardContent>
    </Card>
  );
});

export default CourseLegalFrameworkManager;
