"use client";

import { useParams, useRouter } from 'next/navigation';
import CertificateViewer from '@/components/certificates/CertificateViewer';

export default function VerificarCertificadoDetalle() {
  const params = useParams();
  const router = useRouter();
  const certificateId = params.id as string;

  return (
    <CertificateViewer
      certificateId={certificateId}
      showHeader={true}
      onBack={() => router.push('/verificar-certificado')}
      backUrl="/verificar-certificado"
    />
  );
}
