# 🚀 QR Course Platform Scaffold (CSI Ltda. Edition)

A modern, open-source, production-ready scaffold for building online course and certification platforms in record time. Built with Next.js, Supabase, and a robust, AI-friendly architecture inspired by [Context Engineering](https://github.com/iberi22/context-engineering-template).

---

## 🎨 Brand & Design (CSI Ltda.)

**Brand Colors (from [csiltda.cl](https://www.csiltda.cl/)):**
- ![#003366](https://via.placeholder.com/15/003366/000000?text=+) `#003366` (Primary Blue)
- ![#F9B233](https://via.placeholder.com/15/F9B233/000000?text=+) `#F9B233` (Accent Yellow)
- ![#FFFFFF](https://via.placeholder.com/15/FFFFFF/000000?text=+) `#FFFFFF` (White)
- ![#222222](https://via.placeholder.com/15/222222/000000?text=+) `#222222` (<PERSON> Gray)

**Suggested Free Fonts:**
- [Montserrat](https://fonts.google.com/specimen/Montserrat) (Headings)
- [Open Sans](https://fonts.google.com/specimen/Open+Sans) (Body)

---

## 🏗️ What is this Scaffold?

A plug-and-play template to launch a full-featured, scalable, and maintainable online course & certification system for any company or institution. Easily rebrand, migrate, and extend for new clients or verticals.

- **Multi-tenant ready**
- **Role-based access (Admin, Student, Instructor)**
- **Certificate generation & QR verification**
- **Modern UI/UX (Tailwind, Shadcn/ui, Responsive, Accessible)**
- **Supabase backend (Postgres, Auth, Storage, Edge Functions)**
- **Automated CI/CD with GitHub Actions**
- **Context Engineering for AI coding agents**

---

## 🧑‍💻 Tech Stack & Architecture

- **Frontend:** Next.js (App Router, SSR/SSG, RSC), React 19, TailwindCSS, Shadcn/ui, TypeScript
- **Backend:** Supabase (Postgres, Auth, Storage, Edge Functions, RLS)
- **Design System:** Customizable, atomic, accessible components (Shadcn/ui, Radix UI)
- **CI/CD:** Vercel, GitHub Actions (auto-update dependencies, test, deploy)
- **Testing:** Playwright (E2E), Vitest (unit)
- **DevOps:** Supabase CLI, versioned migrations, automated backups
- **AI-Ready:** Context Engineering docs, modular structure for AI agents

---

## ✨ Features

- 🔐 **Authentication & Authorization** (Supabase Auth, RLS)
- 🏢 **Admin Panel:** Manage users, courses, certificates, attendance, grades
- 🎓 **Student Panel:** View certificates, grades, attendance
- 📄 **Certificate Generation:** Downloadable, printable, QR-verifiable
- 📦 **Storage:** Upload and manage documents/certificates
- 🌐 **Public Verification:** QR code verification for certificates
- 🧩 **Extensible:** Add payment, notifications, analytics, etc.
- 🏷️ **Branding:** Easily swap colors, logos, and fonts
- 🏗️ **Multi-tenant:** Ready for multiple organizations
- 🧑‍🦽 **Accessibility:** WCAG 2.1, keyboard navigation, color contrast
- 🔄 **Automated Backups & Migrations**
- 🤖 **AI Coding Agent Friendly** (Context Engineering)

---

## 🛠️ Getting Started

### 1. **Clone the Scaffold**
```bash
git clone https://github.com/iberi22/context-engineering-template.git context-engineeringfor-qrcourse
cd context-engineeringfor-qrcourse
```

### 2. **Install Dependencies**
```bash
npm install
```

### 3. **Setup Environment**
```bash
cp .env.local.example .env.local
# Edit .env.local with your Supabase project credentials
```

### 4. **Restore Data (Optional)**
If migrating from another project, use the provided backups:
- `backup-supabase.sql` (SQL + data)
- `backup-supabase.json` (JSON)

See [Backup & Migration](#-backup--migration) for details.

### 5. **Run Locally**
```bash
npm run dev
```

### 6. **Deploy**
- Deploy to [Vercel](https://vercel.com/) for instant CI/CD
- Configure GitHub Actions for automated dependency updates and deployments

---

## 🧩 Design System

- **Component Library:** Shadcn/ui, Radix UI, custom atomic components
- **Branding:** Easily override colors, fonts, and logos in `tailwind.config.js` and global CSS
- **Accessibility:** All components are accessible by default
- **Responsive:** Mobile-first, desktop-optimized
- **Theme:** Light/Dark mode ready

---

## 🏛️ Architecture & Best Practices (2024)

- **Monorepo-friendly**: Modular structure for future Nx/Turborepo integration
- **Context Engineering**: [See docs/PLANNING.md](docs/PLANNING.md) for AI agent context
- **Supabase-first**: Auth, RLS, Storage, Edge Functions, versioned migrations
- **CI/CD**: GitHub Actions auto-updates all dependencies on push (see `.github/workflows/`)
- **Testing**: E2E (Playwright), Unit (Vitest)
- **OpenAPI/GraphQL**: Ready for API-first integrations
- **Internationalization (i18n)**: Easy to add new languages
- **Security**: RLS, HTTPS, secure cookies, audit logs
- **Performance**: SSR/SSG, CDN, image optimization
- **Observability**: Logging, error tracking, analytics hooks

---

## 🔄 Backup & Migration

### **Exporting Data**
- Use `backup-supabase.sql` and `backup-supabase.json` to export all tables and data
- Scripts provided for both SQL and JSON export

### **Restoring to a New Supabase Project**
1. Create a new Supabase project
2. Import `backup-supabase.sql` via Supabase SQL editor or CLI
3. Upload files from Supabase Storage if needed
4. Reapply RLS policies, triggers, and functions as needed
5. Update `.env.local` with new credentials

### **Automated Backups**
- Schedule regular exports using GitHub Actions or Supabase Edge Functions
- Store backups securely (e.g., S3, Google Drive)

---

## 🤖 Context Engineering & AI Agents

- **Context-rich docs**: All project docs are structured for AI agent consumption
- **Modular code**: Clear boundaries, explicit context, minimal hallucination risk
- **Prompt engineering**: See [docs/PLANNING.md](docs/PLANNING.md) and [context-engineering-template](https://github.com/iberi22/context-engineering-template)
- **AI-Ready**: Designed for Copilot, Cursor, GPT-4, and future agents

---

## ⚙️ CI/CD & Dependency Management

- **GitHub Actions**: Auto-update all dependencies on push/PR
- **Dependabot**: Optional for extra security
- **Test & Deploy**: All PRs run tests and deploy previews
- **Always Up-to-date**: Stay on the latest versions of all libraries in `package.json`

---

## 📝 Documentation

- **docs/PLANNING.md**: Vision, stack, principles
- **docs/RULES.md**: Coding standards, AI agent rules
- **docs/**: Epics, tasks, workflows, security, deployment
- **README.md**: Always up-to-date, AI-friendly

---

## 🪪 License

This project is **open source** under the MIT License. See [LICENSE](LICENSE) for details.

---

## 🏢 About CSI Ltda.

CSI Ltda. is a leader in fuel and lubricant services, committed to operational excellence, safety, and sustainability. [Learn more](https://www.csiltda.cl/)

---

## 💡 Credits & Inspiration

- [Context Engineering Template](https://github.com/iberi22/context-engineering-template)
- [Supabase](https://supabase.com/)
- [Next.js](https://nextjs.org/)
- [Shadcn/ui](https://ui.shadcn.com/)
- [Radix UI](https://www.radix-ui.com/)
- [Vercel](https://vercel.com/)

---

> **Ready to launch your next online course platform? Fork, brand, and deploy in minutes!**

---

# 🗺️ Modernization Roadmap & Phased Implementation

**This project follows a systematic modernization plan to ensure maintainability, automation, and AI-readiness.**

## Current Status: Phase 1 In Progress [/]

### Phase 1: Documentation Unification & Modularization [/]
**Goal:** Consolidate and modularize all documentation following context-engineering-template standards

- [x] Audit Current Documentation Structure
- [x] Standardize Documentation Format
- [x] Create Cross-Reference System
- [x] Validate Context-Engineering Compliance
- [/] Update README.md Integration

**Key Documents:** [docs/INDEX.md](docs/INDEX.md), [docs/PLANNING.md](docs/PLANNING.md), [docs/TASKS.md](docs/TASKS.md)

### Phase 2: Code Architecture Refactoring [ ]
**Goal:** Decouple business logic from Supabase dependencies using adapter pattern

- [ ] Create Database Adapter Layer
- [ ] Implement Business Logic Abstraction
- [ ] Decouple Supabase Dependencies
- [ ] Create Service Layer Architecture
- [ ] Implement Repository Pattern

**Key Documents:** [docs/ARCHITECTURE_SUSTAINABILITY.md](docs/ARCHITECTURE_SUSTAINABILITY.md)

### Phase 3: Code Quality Enhancement [ ]
**Goal:** Add comprehensive docstrings, practical code examples, and create/update unit tests

- [ ] Add Comprehensive Docstrings
- [ ] Create Unit Test Suite
- [ ] Add Integration Tests
- [ ] Implement Code Examples
- [ ] Create API Documentation

### Phase 4: Automation Implementation [ ]
**Goal:** Set up automated backup systems, code auditing processes, and AI context generation workflows

- [ ] Setup Automated Backup Systems
- [ ] Implement Code Auditing Processes
- [ ] Create AI Context Generation Workflows
- [ ] Setup CI/CD Pipeline Enhancements

**Key Documents:** [docs/BACKUP_MIGRATION.md](docs/BACKUP_MIGRATION.md), [docs/AI_CONTEXT.md](docs/AI_CONTEXT.md)

### Phase 5: Developer Experience Optimization [ ]
**Goal:** Enhance onboarding documentation and improve contribution guidelines

- [ ] Enhance Onboarding Documentation
- [ ] Improve Contribution Guidelines
- [ ] Create Developer-Friendly Documentation
- [ ] Setup Development Environment Automation

### Phase 6: LLM & CI/CD Readiness [ ]
**Goal:** Ensure project structure is optimized for LLM agent interaction and validate CI/CD compatibility

- [ ] Optimize Project Structure for LLM Agents
- [ ] Validate CI/CD Pipeline Compatibility
- [ ] Implement Context-Aware Documentation
- [ ] Create AI Agent Integration Workflows

> **Quick Reference:** Each phase is documented and cross-referenced in `/docs`. See [docs/TASKS.md](docs/TASKS.md) for detailed progress tracking.

---

## 🚦 Next Steps & Getting Involved

### For Developers

1. **Start Here:** Read [docs/PLANNING.md](docs/PLANNING.md) for project vision and architecture
2. **Development Rules:** Check [docs/RULES.md](docs/RULES.md) for coding standards and workflow
3. **Track Progress:** See [docs/TASKS.md](docs/TASKS.md) for current status and next tasks
4. **Full Documentation:** Browse [docs/INDEX.md](docs/INDEX.md) for complete navigation

### For AI Coding Agents

- **Context:** All documentation follows Context Engineering principles for optimal AI interaction
- **Structure:** Modular, cross-referenced documentation with clear boundaries and explicit context
- **Automation:** See [docs/AI_CONTEXT.md](docs/AI_CONTEXT.md) for bot orchestration and metadata generation
- **Prompt:** Use the systematic modernization approach outlined in the phase documentation above

### Contributing

- Follow the phased modernization approach
- Update documentation as you make changes
- Maintain cross-references between documents
- Test all changes thoroughly before committing

---
