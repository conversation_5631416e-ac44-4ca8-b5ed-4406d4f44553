"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

export interface UserOption {
  value: string
  label: string
  email?: string
  identity_document?: string
}

interface BasicUserSelectProps {
  options: UserOption[]
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
}

export function BasicUserSelect({
  options,
  value,
  onChange,
  placeholder = "Seleccionar usuario...",
  className,
  disabled = false,
}: BasicUserSelectProps) {
  // Ensure we have valid options
  const validOptions = React.useMemo(() => {
    return Array.isArray(options) ? options : [];
  }, [options]);

  // Handle selection
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value;
    console.log("BasicUserSelect selection changed to:", selectedValue);

    // Find the selected option for better debugging
    if (selectedValue) {
      const selectedOption = validOptions.find(option => option.value === selectedValue);
      console.log("Selected option:", selectedOption);
    }

    // Use setTimeout to ensure the UI updates correctly
    setTimeout(() => {
      onChange(selectedValue);
    }, 10);
  };

  return (
    <select
      value={value}
      onChange={handleChange}
      disabled={disabled}
      className={cn(
        "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
        "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
    >
      <option value="">{placeholder}</option>
      {validOptions.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label} {option.identity_document ? `- RUT: ${option.identity_document}` : ""}
        </option>
      ))}
    </select>
  )
}
