# ⚡ Quick Start Guide - DOMUS OTEC

Guía rápida para desarrolladores que quieren empezar a trabajar con DOMUS OTEC inmediatamente.

## 🚀 Setup en 5 Minutos

### 1. Prerequisitos Rápidos

```bash
# Verificar Node.js (requiere 18+)
node --version

# Si no tienes Node.js 18+, instalar desde https://nodejs.org
```

### 2. Clonar y Configurar

```bash
# Clonar repositorio
git clone https://github.com/iberi22/scaffolding-curses-nextjs-supabase.git
cd domus-otec

# Instalar dependencias
npm install

# Configurar entorno
cp .env.example .env.local
```

### 3. Variables de Entorno Mínimas

Edita `.env.local` con estos valores mínimos:

```env
# Para desarrollo local básico
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. Ejecutar

```bash
# Iniciar servidor de desarrollo
npm run dev

# Abrir http://localhost:3000
```

## 🎯 Tareas Comunes

### Agregar Nueva Página

```bash
# Crear archivo en app/
touch src/app/nueva-pagina/page.tsx
```

```tsx
// src/app/nueva-pagina/page.tsx
export default function NuevaPagina() {
  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold">Nueva Página</h1>
      <p>Contenido de la página...</p>
    </div>
  );
}
```

### Crear Componente

```bash
# Crear directorio y archivo
mkdir src/components/mi-componente
touch src/components/mi-componente/index.tsx
```

```tsx
// src/components/mi-componente/index.tsx
interface MiComponenteProps {
  title: string;
  children?: React.ReactNode;
}

export function MiComponente({ title, children }: MiComponenteProps) {
  return (
    <div className="p-4 border rounded-lg">
      <h2 className="text-lg font-semibold mb-2">{title}</h2>
      {children}
    </div>
  );
}
```

### Agregar Servicio

```bash
# Crear archivo de servicio
touch src/lib/services/mi-servicio.ts
```

```typescript
// src/lib/services/mi-servicio.ts
import { BaseService } from './base-service';
import { ServiceResponse } from '../types';

export class MiServicio extends BaseService {
  async obtenerDatos(): Promise<ServiceResponse<any[]>> {
    try {
      // Lógica del servicio
      const datos = await this.repositories.miEntidad.findAll();
      return this.success(datos);
    } catch (error) {
      return this.error('FETCH_ERROR', 'Error obteniendo datos', error);
    }
  }
}
```

### Agregar Test

```bash
# Crear archivo de test
touch src/components/mi-componente/__tests__/index.test.tsx
```

```tsx
// src/components/mi-componente/__tests__/index.test.tsx
import { render, screen } from '@testing-library/react';
import { MiComponente } from '../index';

describe('MiComponente', () => {
  it('should render title correctly', () => {
    render(<MiComponente title="Test Title" />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
});
```

## 🔧 Comandos Útiles

```bash
# Desarrollo
npm run dev              # Servidor de desarrollo
npm run build           # Build de producción
npm start              # Servidor de producción

# Testing
npm test               # Ejecutar tests
npm run test:watch     # Tests en modo watch
npm run test:coverage  # Tests con cobertura

# Calidad de código
npm run lint           # ESLint
npm run lint:fix       # ESLint con auto-fix
npm run type-check     # Verificar tipos TypeScript
npm run format         # Prettier
npm run format:check   # Verificar formato

# Utilidades
npm run analyze        # Analizar bundle
npm run clean          # Limpiar archivos generados
```

## 📁 Estructura Rápida

```
src/
├── app/                    # Páginas (Next.js App Router)
│   ├── (auth)/            # Grupo de rutas de auth
│   ├── (dashboard)/       # Grupo de rutas de dashboard
│   ├── api/               # API routes
│   └── globals.css        # Estilos globales
├── components/            # Componentes React
│   ├── ui/               # Componentes base (Shadcn/UI)
│   └── [feature]/        # Componentes por feature
├── lib/                  # Lógica de negocio
│   ├── services/         # Servicios de negocio
│   ├── repositories/     # Acceso a datos
│   ├── types/           # Tipos TypeScript
│   └── utils/           # Utilidades
└── __tests__/           # Tests y utilidades
```

## 🎨 Styling Rápido

### Tailwind CSS Classes Comunes

```tsx
// Layout
<div className="container mx-auto p-6">           // Container centrado
<div className="flex items-center justify-between"> // Flex horizontal
<div className="grid grid-cols-1 md:grid-cols-2">  // Grid responsivo

// Spacing
<div className="p-4">        // Padding
<div className="m-4">        // Margin
<div className="space-y-4">  // Espacio vertical entre hijos

// Typography
<h1 className="text-2xl font-bold">              // Título
<p className="text-muted-foreground">            // Texto secundario
<span className="text-sm">                       // Texto pequeño

// Colors
<div className="bg-background">                  // Fondo principal
<div className="bg-card">                        // Fondo de tarjeta
<div className="text-foreground">                // Texto principal
<div className="border">                         // Borde

// Interactive
<button className="hover:bg-accent">             // Hover
<div className="transition-colors">              // Transición
```

### Componentes UI Disponibles

```tsx
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';

// Uso básico
<Button variant="default" size="md">Click me</Button>
<Card>
  <CardHeader>
    <CardTitle>Title</CardTitle>
  </CardHeader>
  <CardContent>
    <p>Content</p>
  </CardContent>
</Card>
```

## 🔄 Flujo de Desarrollo

### 1. Crear Feature Branch

```bash
git checkout -b feature/mi-nueva-feature
```

### 2. Desarrollar

1. Escribir test (opcional pero recomendado)
2. Implementar funcionalidad
3. Verificar que funciona

### 3. Verificar Calidad

```bash
npm run lint
npm run type-check
npm test
npm run build
```

### 4. Commit y Push

```bash
git add .
git commit -m "feat: add nueva funcionalidad"
git push origin feature/mi-nueva-feature
```

### 5. Crear Pull Request

- Ir a GitHub
- Crear PR desde tu branch a `main`
- Completar template de PR
- Solicitar review

## 🐛 Debugging Rápido

### Logs de Desarrollo

```typescript
// En desarrollo, usar console.log está bien
console.log('Debug info:', data);

// Para producción, usar logger
import { logger } from '@/lib/utils/logger';
logger.info('Info message', { data });
logger.error('Error message', error);
```

### React DevTools

1. Instalar extensión de React DevTools
2. Abrir DevTools en navegador
3. Usar tabs "Components" y "Profiler"

### Network Debugging

```typescript
// Ver requests en Network tab del navegador
// Para debugging de API calls

// En servicios, agregar logs temporales
async function miServicio() {
  console.log('Calling API...');
  const response = await fetch('/api/endpoint');
  console.log('Response:', response);
  return response.json();
}
```

## 📚 Referencias Rápidas

### TypeScript

```typescript
// Tipos básicos
interface User {
  id: string;
  name: string;
  email: string;
}

// Tipos de props
interface ComponentProps {
  user: User;
  onEdit?: (user: User) => void;
  className?: string;
}

// Tipos de respuesta
type ServiceResponse<T> = {
  success: boolean;
  data?: T;
  error?: string;
};
```

### React Hooks Comunes

```tsx
import { useState, useEffect, useCallback } from 'react';

function MiComponente() {
  // Estado
  const [data, setData] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);

  // Efecto
  useEffect(() => {
    fetchData();
  }, []);

  // Callback memoizado
  const handleClick = useCallback(() => {
    // Handle click
  }, []);

  return <div>...</div>;
}
```

### Supabase Básico

```typescript
import { supabase } from '@/lib/supabase';

// Query básica
const { data, error } = await supabase
  .from('users')
  .select('*')
  .eq('active', true);

// Insert
const { data, error } = await supabase
  .from('users')
  .insert({ name: 'John', email: '<EMAIL>' });

// Update
const { data, error } = await supabase
  .from('users')
  .update({ name: 'Jane' })
  .eq('id', userId);
```

## 🆘 Ayuda Rápida

### Problemas Comunes

**Error de tipos TypeScript:**
```bash
npm run type-check
# Revisar errores y corregir tipos
```

**Tests fallan:**
```bash
npm test -- --verbose
# Ver detalles de tests que fallan
```

**Build falla:**
```bash
npm run build
# Revisar errores de build
```

**Linting errors:**
```bash
npm run lint:fix
# Auto-fix de errores de linting
```

### Contactos

- **GitHub Issues**: Para bugs y features
- **Team Lead**: [<EMAIL>]
- **Documentación**: [docs/](../README.md)

---

¡Listo para desarrollar! 🚀 Si necesitas más detalles, consulta la [documentación completa](../onboarding/DEVELOPER_ONBOARDING.md).
