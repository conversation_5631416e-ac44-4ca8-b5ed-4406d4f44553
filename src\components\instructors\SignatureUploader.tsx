"use client";

import { useState, useRef } from 'react';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "@/components/ui/use-toast";
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import Image from 'next/image';
import { Loader2, Upload, X } from 'lucide-react';

interface SignatureUploaderProps {
  instructorId: string;
  currentSignatureUrl?: string | null;
  onSignatureUploaded: (url: string) => void;
}

export default function SignatureUploader({
  instructorId,
  currentSignatureUrl,
  onSignatureUploaded
}: SignatureUploaderProps) {
  const supabase = createClientComponentClient();
  const [uploading, setUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentSignatureUrl || null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!['image/jpeg', 'image/png', 'image/svg+xml'].includes(file.type)) {
      toast({
        title: "Formato no válido",
        description: "Por favor, sube una imagen en formato JPG, PNG o SVG.",
        variant: "destructive",
      });
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "Archivo demasiado grande",
        description: "El tamaño máximo permitido es 5MB.",
        variant: "destructive",
      });
      return;
    }

    try {
      setUploading(true);

      // Create a unique file name
      const fileExt = file.name.split('.').pop();
      const fileName = `${instructorId}-signature-${Date.now()}.${fileExt}`;
      const filePath = `${fileName}`;

      // Upload file to Supabase Storage
      const { data, error } = await supabase.storage
        .from('signatures')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) throw error;

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('signatures')
        .getPublicUrl(filePath);

      // Update preview
      setPreviewUrl(publicUrl);

      // Call the callback with the new URL
      onSignatureUploaded(publicUrl);

      toast({
        title: "Firma subida",
        description: "La firma se ha subido correctamente.",
      });
    } catch (error: any) {
      console.error("Error uploading signature:", error);
      toast({
        title: "Error",
        description: "No se pudo subir la firma. " + error.message,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveSignature = async () => {
    if (!previewUrl || !confirm("¿Estás seguro de que deseas eliminar esta firma?")) {
      return;
    }

    try {
      setUploading(true);

      // Extract file path from URL
      const urlParts = previewUrl.split('/');
      const filePath = urlParts[urlParts.length - 1];

      // Delete file from storage
      const { error } = await supabase.storage
        .from('signatures')
        .remove([filePath]);

      if (error) throw error;

      // Clear preview and call callback with empty string
      setPreviewUrl(null);
      onSignatureUploaded('');

      toast({
        title: "Firma eliminada",
        description: "La firma se ha eliminado correctamente.",
      });
    } catch (error: any) {
      console.error("Error removing signature:", error);
      toast({
        title: "Error",
        description: "No se pudo eliminar la firma. " + error.message,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Firma del Instructor</CardTitle>
        <CardDescription>
          Sube una imagen de la firma para mostrar en los certificados (JPG, PNG o SVG)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {previewUrl ? (
            <div className="relative border rounded-md p-4 flex flex-col items-center">
              <div className="relative w-full h-32 mb-2">
                <Image
                  src={previewUrl}
                  alt="Firma del instructor"
                  fill
                  style={{ objectFit: "contain" }}
                />
              </div>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleRemoveSignature}
                disabled={uploading}
              >
                {uploading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <X className="h-4 w-4 mr-2" />}
                Eliminar firma
              </Button>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center border border-dashed rounded-md p-6">
              <Upload className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground mb-4">
                Haz clic para subir una imagen de firma (JPG, PNG o SVG)
              </p>
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploading}
              >
                {uploading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
                Seleccionar archivo
              </Button>
              <Input
                ref={fileInputRef}
                type="file"
                accept="image/jpeg,image/png,image/svg+xml"
                onChange={handleFileChange}
                className="hidden"
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
