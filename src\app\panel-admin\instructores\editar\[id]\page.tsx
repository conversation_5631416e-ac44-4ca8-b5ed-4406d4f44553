"use client";

import { useState, useEffect, FormEvent } from "react";
import { useRouter, useParams } from "next/navigation";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { Loader2 } from "lucide-react";
import SignatureUploader from "@/components/instructors/SignatureUploader";

// Define types for Instructor
type Instructor = {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  signature_url: string | null;
  created_at: string;
  updated_at: string;
};

export default function EditInstructor() {
  const supabase = createClientComponentClient();
  const router = useRouter();
  const params = useParams();
  const instructorId = params.id as string;

  // Form state
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [signatureUrl, setSignatureUrl] = useState<string | null>(null);

  // UI states
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formErrors, setFormErrors] = useState<{
    name?: string;
    email?: string;
    phone?: string;
  }>({});

  // Cargar datos del instructor
  useEffect(() => {
    async function loadInstructor() {
      try {
        setLoading(true);
        
        const { data, error } = await supabase
          .from("instructors")
          .select("*")
          .eq("id", instructorId)
          .single();
        
        if (error) throw error;
        
        if (!data) throw new Error("Instructor no encontrado");
        
        // Set state with fallbacks for null values
        setName(data.name || "");
        setEmail(data.email || "");
        setPhone(data.phone || "");
        setSignatureUrl(data.signature_url);
      } catch (error: any) {
        console.error("Error loading instructor:", error);
        setError(error.message || "Error al cargar los datos del instructor");
      } finally {
        setLoading(false);
      }
    }
    
    loadInstructor();
  }, [supabase, instructorId]);

  // Validar formulario
  const validateForm = () => {
    const errors: {
      name?: string;
      email?: string;
      phone?: string;
    } = {};
    let isValid = true;

    if (!name.trim()) {
      errors.name = "El nombre es obligatorio";
      isValid = false;
    }

    if (email && !/\S+@\S+\.\S+/.test(email)) {
      errors.email = "El correo electrónico no es válido";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Manejar envío del formulario
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!validateForm()) {
      return;
    }

    setSaving(true);

    try {
      const updates = {
        name,
        email: email || null,
        phone: phone || null,
        signature_url: signatureUrl,
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from("instructors")
        .update(updates)
        .eq("id", instructorId);

      if (error) throw error;

      toast({
        title: "Instructor actualizado",
        description: "Los datos del instructor se han actualizado correctamente.",
      });

      router.push("/panel-admin/instructores?updated=true");
    } catch (error: any) {
      console.error("Error updating instructor:", error);
      setError(error.message || "Error al actualizar el instructor");
      toast({
        title: "Error",
        description: "No se pudo actualizar el instructor. " + error.message,
        variant: "destructive",
      });
    } finally {
      setSaving(false);
    }
  };

  // Manejar actualización de firma
  const handleSignatureUploaded = (url: string) => {
    setSignatureUrl(url);
    toast({
      title: "Firma actualizada",
      description: "La firma se ha actualizado correctamente.",
    });
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Editar Instructor</h1>
        <Button
          variant="outline"
          onClick={() => router.push("/panel-admin/instructores")}
        >
          Cancelar
        </Button>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Información del Instructor</CardTitle>
              <CardDescription>
                Actualiza los datos básicos del instructor
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nombre completo *</Label>
                  <Input
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    required
                  />
                  {formErrors.name && (
                    <p className="text-sm text-red-500">{formErrors.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Correo electrónico</Label>
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                  />
                  {formErrors.email && (
                    <p className="text-sm text-red-500">{formErrors.email}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Teléfono</Label>
                  <Input
                    id="phone"
                    value={phone}
                    onChange={(e) => setPhone(e.target.value)}
                  />
                  {formErrors.phone && (
                    <p className="text-sm text-red-500">{formErrors.phone}</p>
                  )}
                </div>

                <Button type="submit" disabled={saving}>
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Guardando...
                    </>
                  ) : (
                    "Guardar cambios"
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>

          <SignatureUploader
            instructorId={instructorId}
            currentSignatureUrl={signatureUrl}
            onSignatureUploaded={handleSignatureUploaded}
          />
        </div>
      )}
    </div>
  );
}
