"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function NotasAsistenciaPage() {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Gestión de Notas y Asistencia</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Gestión de Notas</CardTitle>
            <CardDescription>
              Administre las calificaciones de los estudiantes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-gray-600">
              Registre y gestione las calificaciones de los estudiantes en los diferentes cursos y evaluaciones.
            </p>
            <Link href="/panel-admin/notas">
              <Button>Ir a Gestión de Notas</Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Control de Asistencia</CardTitle>
            <CardDescription>
              Administre la asistencia de los estudiantes
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="mb-4 text-gray-600">
              Registre y gestione la asistencia de los estudiantes a las clases y actividades.
            </p>
            <Link href="/panel-admin/asistencia">
              <Button>Ir a Control de Asistencia</Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
