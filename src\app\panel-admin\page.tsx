"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import DatabaseStatus from "@/app/components/DatabaseStatus";
import { ensureValidSession, supabase, tableExists } from "@/lib/supabase"; // Removed getDatabaseSchema

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCertificates: 0,
    totalCourses: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchDashboardData() {
      try {
        setLoading(true);
        setError(null);

        // Comprobar que tenemos los parámetros de conexión a Supabase
        if (
          !process.env.NEXT_PUBLIC_SUPABASE_URL ||
          !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        ) {
          console.error("Faltan variables de entorno para Supabase");
          throw new Error(
            "Error de configuración: credenciales de base de datos no configuradas. " +
              "Verifica que el archivo .env.local contiene NEXT_PUBLIC_SUPABASE_URL y NEXT_PUBLIC_SUPABASE_ANON_KEY.",
          );
        }

        // Verificar si la sesión está activa y válida
        const isSessionValid = await ensureValidSession();

        if (!isSessionValid) {
          throw new Error(
            "No hay sesión activa o ha expirado. Por favor, recarga la página o inicia sesión nuevamente.",
          );
        }

        // Removed database schema diagnosis as it's no longer used here

        // Variables para almacenar conteos
        let studentCount = 0;
        let certificateCount = 0;
        let courseCount = 0;

        // Verificar cada tabla antes de consultarla
        const hasUsers = await tableExists("users");
        // Removed check for students table
        const hasCertificates = await tableExists("certificates");
        const hasCourses = await tableExists("courses");

        console.log("Tablas disponibles:", {
          users: hasUsers,
          // Removed students count reference
          certificates: hasCertificates,
          courses: hasCourses,
        });

        // Si no encontramos ninguna tabla, mostrar un aviso claro
        // Adjusted condition to only check existing tables we care about
        // Condition now only checks for tables we expect to exist
        if (!hasUsers && !hasCertificates && !hasCourses) {
          console.error(
            "No se pudo detectar ninguna tabla en la base de datos",
          );
          throw new Error(
            "No se detectaron tablas en la base de datos. Posibles causas:\n" +
              "1. La conexión a Supabase no está correctamente configurada\n" +
              "2. No se han creado las tablas necesarias en la base de datos\n" +
              "3. El usuario no tiene permisos para acceder a estas tablas\n\n" +
              "Solución: Verifica la URL de Supabase, la clave anónima y las políticas RLS.",
          );
        }

        // Consultar usuarios o perfiles según disponibilidad
        let userTableUsed = null;

        if (hasUsers) {
          try {
            userTableUsed = "users";
            const { error: studentError, count: studCount } = await supabase
              .from("users")
              .select("id", { count: "exact", head: true })
              .eq("role", "student"); // Filter only by the valid lowercase role

            if (studentError) {
              console.error("Error consultando usuarios:", studentError);
              throw studentError;
            } else {
              studentCount = studCount || 0;
            }
          } catch (error) {
            console.error("Error inesperado al consultar usuarios:", error);
            throw new Error(
              `Error consultando usuarios: ${error instanceof Error ? error.message : JSON.stringify(error)}`,
            );
          } // End of try block for 'users' table check
        } else {
          // This else means 'users' table was not found or accessible
          console.warn(
            "No se encontró o no se pudo acceder a la tabla 'users' para contar estudiantes.",
          );
        }
        // --- Entire logic for checking profiles and students is now removed ---

        // Informar sobre la tabla utilizada
        if (userTableUsed) {
          // Log now confirms 'users' was the target
          console.log(
            `Se intentó usar la tabla '${userTableUsed}' para contar estudiantes: ${studentCount}`,
          );
        }

        // Consultar certificados
        if (hasCertificates) {
          try {
            const { count: certCount, error: certError } = await supabase
              .from("certificates")
              .select("id", { count: "exact", head: true });

            if (certError) {
              console.error("Error consultando certificados:", certError);
            } else {
              certificateCount = certCount || 0;
            }
          } catch (error) {
            console.error("Error inesperado al consultar certificados:", error);
          }
        } else {
          console.warn("No se encontró tabla de certificados");
        }

        // Consultar cursos
        if (hasCourses) {
          try {
            const { count: coursCount, error: courseError } = await supabase
              .from("courses")
              .select("id", { count: "exact", head: true });

            if (courseError) {
              console.error("Error consultando cursos:", courseError);
            } else {
              courseCount = coursCount || 0;
            }
          } catch (error) {
            console.error("Error inesperado al consultar cursos:", error);
          }
        } else {
          console.warn("No se encontró tabla de cursos");
        }

        // Actualizar estadísticas con valores obtenidos
        setStats({
          totalStudents: studentCount,
          totalCertificates: certificateCount,
          totalCourses: courseCount,
        });
      } catch (err) {
        console.error("Raw error caught in fetchDashboardData:", err);

        // Manejo mejorado de errores
        let errorMessage = "No se pudieron cargar los datos del dashboard";
        let errorDetails = "";

        if (err instanceof Error) {
          // Es un objeto Error estándar
          errorMessage = err.message || errorMessage;
          errorDetails = JSON.stringify(
            err,
            Object.getOwnPropertyNames(err),
            2,
          );
          console.error("Error details:", errorDetails);
        } else if (err && typeof err === "object") {
          // Es un objeto pero no de tipo Error
          try {
            // Intentar extraer mensaje y detalles completos
            const errObj = err as Record<string, unknown>;
            errorDetails = JSON.stringify(errObj, null, 2);

            // Comprobamos si es un error 400 específico
            if (errObj.status === 400 || errObj.statusCode === 400) {
              errorMessage =
                "Error de conexión con la base de datos. Por favor, intenta nuevamente.";
              if (errObj.message)
                errorMessage += ` Detalle: ${String(errObj.message)}`;
            } else {
              errorMessage =
                (errObj.message as string) ||
                (errObj.details as string) ||
                (errObj.error as string) ||
                "Error desconocido al obtener datos";
            }

            console.error("Error object details:", errorDetails);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (_parseError) {
            console.error("Could not extract error details from object:", err);
            errorDetails = "Error al analizar el objeto de error";
          }
        } else {
          // Otro tipo de error (primitivo, etc)
          errorMessage = String(err);
          errorDetails = `Tipo: ${typeof err}`;
          console.error("Unknown error type:", errorDetails, err);
        }

        // Guardar tanto el mensaje de error como los detalles completos
        setError(
          `${errorMessage}${errorDetails ? ` (Detalles: ${errorDetails})` : ""}`,
        );
      } finally {
        setLoading(false);
      }
    }

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-48">
        <p className="text-gray-500">Cargando estadísticas...</p>
      </div>
    );
  }

  if (error) {
    // Determinar si el error es un problema común y proporcionar orientación específica
    const isSchemaError =
      error.includes("information_schema") ||
      error.includes("tabla") ||
      error.includes("table");
    const isAuthError =
      error.includes("sesión") ||
      error.includes("session") ||
      error.includes("JWT");
    const isConnectionError =
      error.includes("404") ||
      error.includes("conexión") ||
      error.includes("network");

    return (
      <div className="space-y-4">
        <div className="bg-red-50 border-l-4 border-red-500 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error al cargar el panel
              </h3>
              <p className="text-sm text-red-700 mt-2">{error}</p>
            </div>
          </div>
        </div>

        {/* Ayuda específica basada en el tipo de error */}
        {isSchemaError && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <h4 className="text-sm font-medium text-yellow-800">
              Posible problema con la estructura de la base de datos
            </h4>
            <ul className="list-disc pl-5 mt-2 text-sm text-yellow-700">
              <li>
                Verifica que las tablas necesarias existen en tu proyecto
                Supabase
              </li>
              <li>
                Revisa las políticas RLS para asegurar que el usuario actual
                tiene permisos
              </li>
              <li>
                Si estás usando una base de datos nueva, ejecuta las migraciones
                necesarias
              </li>
            </ul>
          </div>
        )}

        {isAuthError && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <h4 className="text-sm font-medium text-yellow-800">
              Problema de autenticación detectado
            </h4>
            <ul className="list-disc pl-5 mt-2 text-sm text-yellow-700">
              <li>Tu sesión puede haber expirado</li>
              <li>Intenta cerrar sesión y volver a iniciar sesión</li>
              <li>Verifica que estás usando credenciales correctas</li>
            </ul>
          </div>
        )}

        {isConnectionError && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <h4 className="text-sm font-medium text-yellow-800">
              Problema de conexión detectado
            </h4>
            <ul className="list-disc pl-5 mt-2 text-sm text-yellow-700">
              <li>Verifica tu conexión a internet</li>
              <li>Confirma que la URL de Supabase en .env.local es correcta</li>
              <li>
                El servicio de Supabase podría estar experimentando problemas
                temporales
              </li>
            </ul>
          </div>
        )}

        {/* Link para regresar */}
        <div className="mt-4">
          <Link href="/" className="text-indigo-600 hover:text-indigo-800">
            ← Volver a la página de inicio
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold text-gray-900">
          Resumen General
        </h2>
        <p className="mt-1 text-sm text-gray-500">
          Vista rápida de las principales métricas de la plataforma.
        </p>
        <DatabaseStatus />
      </div>

      {/* Panel de {"message":""} */}
      {stats.totalStudents === 0 &&
        stats.totalCertificates === 0 &&
        stats.totalCourses === 0 && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg
                  className="h-5 w-5 text-yellow-400"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  Todavía no hay información disponible. Comienza registrando
                  alumnos, cursos y certificados.
                </p>
              </div>
            </div>
          </div>
        )}

      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {/* Students Stats Card */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-indigo-500 rounded-md p-3">
                <svg
                  className="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Total de Alumnos
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {stats.totalStudents}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                href="/panel-admin/alumnos"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Ver todos los alumnos
                <span aria-hidden="true"> &rarr;</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Certificates Stats Card */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <svg
                  className="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Certificados Emitidos
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {stats.totalCertificates}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <Link
                href="/panel-admin/certificados"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Gestionar certificados
                <span aria-hidden="true"> &rarr;</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Courses Stats Card */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                <svg
                  className="h-6 w-6 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                  />
                </svg>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    Cursos Activos
                  </dt>
                  <dd>
                    <div className="text-lg font-medium text-gray-900">
                      {stats.totalCourses}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div className="bg-gray-50 px-4 py-4 sm:px-6">
            <div className="text-sm">
              <a
                href="#"
                className="font-medium text-indigo-600 hover:text-indigo-500"
              >
                Ver todos los cursos
                <span aria-hidden="true"> &rarr;</span>
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Links Section */}
      <div className="mt-8">
        <h2 className="text-2xl font-semibold text-gray-900">
          Acciones Rápidas
        </h2>
        <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
          <div className="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-indigo-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <Link href="/panel-admin/alumnos" className="focus:outline-none">
                <span className="absolute inset-0" aria-hidden="true" />
                <p className="text-sm font-medium text-gray-900">
                  Registrar Nuevo Alumno
                </p>
                <p className="text-sm text-gray-500">
                  Añadir un nuevo alumno a la plataforma
                </p>
              </Link>
            </div>
          </div>

          <div className="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-indigo-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <Link
                href="/panel-admin/certificados"
                className="focus:outline-none"
              >
                <span className="absolute inset-0" aria-hidden="true" />
                <p className="text-sm font-medium text-gray-900">
                  Crear Nuevo Certificado
                </p>
                <p className="text-sm text-gray-500">
                  Generar un certificado para un alumno
                </p>
              </Link>
            </div>
          </div>

          <div className="relative rounded-lg border border-gray-300 bg-white px-6 py-5 shadow-sm flex items-center space-x-3 hover:border-gray-400 focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-indigo-600"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
            </div>
            <div className="flex-1 min-w-0">
              <Link
                href="/panel-admin/instructores"
                className="focus:outline-none"
              >
                <span className="absolute inset-0" aria-hidden="true" />
                <p className="text-sm font-medium text-gray-900">
                  Gestionar Instructores
                </p>
                <p className="text-sm text-gray-500 truncate">
                  Administrar instructores y sus firmas
                </p>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
