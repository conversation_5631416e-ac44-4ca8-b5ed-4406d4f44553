"use client";

import Link from "next/link";
import { useEffect, useState, useCallback } from "react";
import { Users, BookOpen, Award, TrendingUp, Plus, Settings, BarChart3, Calendar } from "lucide-react";
import { ModernCard, ModernCardHeader, ModernCardTitle, ModernCardContent } from "@/components/ui/modern-card";
import { StatCard } from "@/components/ui/modern-card";
import { ModernButton } from "@/components/ui/modern-button";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { ResponsiveGrid } from "@/components/ui/responsive";
import DatabaseStatus from "@/app/components/DatabaseStatus";
import { ensureValidSession, supabase, tableExists } from "@/lib/supabase";

export default function AdminDashboard() {
  const [stats, setStats] = useState({
    totalStudents: 0,
    totalCertificates: 0,
    totalCourses: 0,
    totalInstructors: 0,
    recentActivity: [],
    monthlyGrowth: {
      students: 0,
      certificates: 0,
      courses: 0,
    }
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Verificar si la sesión está activa y válida
      const isSessionValid = await ensureValidSession();
      if (!isSessionValid) {
        throw new Error("Sesión no válida. Por favor, inicia sesión nuevamente.");
      }

      // Obtener conteos de las tablas principales
      let studentCount = 0;
      let certificateCount = 0;
      let courseCount = 0;
      let instructorCount = 0;

      // Contar estudiantes
      try {
        const { count } = await supabase
          .from("users")
          .select("*", { count: "exact", head: true })
          .eq("role", "student");
        studentCount = count || 0;
      } catch (error) {
        console.warn("No se pudo obtener el conteo de estudiantes:", error);
      }

      // Contar certificados
      try {
        const { count } = await supabase
          .from("certificates")
          .select("*", { count: "exact", head: true });
        certificateCount = count || 0;
      } catch (error) {
        console.warn("No se pudo obtener el conteo de certificados:", error);
      }

      // Contar cursos
      try {
        const { count } = await supabase
          .from("courses")
          .select("*", { count: "exact", head: true });
        courseCount = count || 0;
      } catch (error) {
        console.warn("No se pudo obtener el conteo de cursos:", error);
      }

      // Contar instructores
      try {
        const { count } = await supabase
          .from("users")
          .select("*", { count: "exact", head: true })
          .in("role", ["admin", "instructor"]);
        instructorCount = count || 0;
      } catch (error) {
        console.warn("No se pudo obtener el conteo de instructores:", error);
      }

      // Actualizar el estado con los datos obtenidos
      setStats({
        totalStudents: studentCount,
        totalCertificates: certificateCount,
        totalCourses: courseCount,
        totalInstructors: instructorCount,
        recentActivity: [], // Por ahora vacío, se puede implementar después
        monthlyGrowth: {
          students: Math.floor(Math.random() * 20), // Datos simulados por ahora
          certificates: Math.floor(Math.random() * 15),
          courses: Math.floor(Math.random() * 5),
        }
      });

        // Consultar certificados
        if (hasCertificates) {
          try {
            const { count: certCount, error: certError } = await supabase
              .from("certificates")
              .select("id", { count: "exact", head: true });

            if (certError) {
              console.error("Error consultando certificados:", certError);
            } else {
              certificateCount = certCount || 0;
            }
          } catch (error) {
            console.error("Error inesperado al consultar certificados:", error);
          }
        } else {
          console.warn("No se encontró tabla de certificados");
        }

        // Consultar cursos
        if (hasCourses) {
          try {
            const { count: coursCount, error: courseError } = await supabase
              .from("courses")
              .select("id", { count: "exact", head: true });

            if (courseError) {
              console.error("Error consultando cursos:", courseError);
            } else {
              courseCount = coursCount || 0;
            }
          } catch (error) {
            console.error("Error inesperado al consultar cursos:", error);
          }
        } else {
          console.warn("No se encontró tabla de cursos");
        }

        // Actualizar estadísticas con valores obtenidos
        setStats({
          totalStudents: studentCount,
          totalCertificates: certificateCount,
          totalCourses: courseCount,
        });
      } catch (err) {
        console.error("Raw error caught in fetchDashboardData:", err);

        // Manejo mejorado de errores
        let errorMessage = "No se pudieron cargar los datos del dashboard";
        let errorDetails = "";

        if (err instanceof Error) {
          // Es un objeto Error estándar
          errorMessage = err.message || errorMessage;
          errorDetails = JSON.stringify(
            err,
            Object.getOwnPropertyNames(err),
            2,
          );
          console.error("Error details:", errorDetails);
        } else if (err && typeof err === "object") {
          // Es un objeto pero no de tipo Error
          try {
            // Intentar extraer mensaje y detalles completos
            const errObj = err as Record<string, unknown>;
            errorDetails = JSON.stringify(errObj, null, 2);

            // Comprobamos si es un error 400 específico
            if (errObj.status === 400 || errObj.statusCode === 400) {
              errorMessage =
                "Error de conexión con la base de datos. Por favor, intenta nuevamente.";
              if (errObj.message)
                errorMessage += ` Detalle: ${String(errObj.message)}`;
            } else {
              errorMessage =
                (errObj.message as string) ||
                (errObj.details as string) ||
                (errObj.error as string) ||
                "Error desconocido al obtener datos";
            }

            console.error("Error object details:", errorDetails);
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
          } catch (_parseError) {
            console.error("Could not extract error details from object:", err);
            errorDetails = "Error al analizar el objeto de error";
          }
        } else {
          // Otro tipo de error (primitivo, etc)
          errorMessage = String(err);
          errorDetails = `Tipo: ${typeof err}`;
          console.error("Unknown error type:", errorDetails, err);
        }

        // Guardar tanto el mensaje de error como los detalles completos
        setError(
          `${errorMessage}${errorDetails ? ` (Detalles: ${errorDetails})` : ""}`,
        );
      } finally {
        setLoading(false);
      }
    }, []);

    useEffect(() => {
      fetchDashboardData();
    }, [fetchDashboardData]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-48">
        <p className="text-gray-500">Cargando estadísticas...</p>
      </div>
    );
  }

  if (error) {
    // Determinar si el error es un problema común y proporcionar orientación específica
    const isSchemaError =
      error.includes("information_schema") ||
      error.includes("tabla") ||
      error.includes("table");
    const isAuthError =
      error.includes("sesión") ||
      error.includes("session") ||
      error.includes("JWT");
    const isConnectionError =
      error.includes("404") ||
      error.includes("conexión") ||
      error.includes("network");

    return (
      <div className="space-y-4">
        <div className="bg-red-50 border-l-4 border-red-500 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Error al cargar el panel
              </h3>
              <p className="text-sm text-red-700 mt-2">{error}</p>
            </div>
          </div>
        </div>

        {/* Ayuda específica basada en el tipo de error */}
        {isSchemaError && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <h4 className="text-sm font-medium text-yellow-800">
              Posible problema con la estructura de la base de datos
            </h4>
            <ul className="list-disc pl-5 mt-2 text-sm text-yellow-700">
              <li>
                Verifica que las tablas necesarias existen en tu proyecto
                Supabase
              </li>
              <li>
                Revisa las políticas RLS para asegurar que el usuario actual
                tiene permisos
              </li>
              <li>
                Si estás usando una base de datos nueva, ejecuta las migraciones
                necesarias
              </li>
            </ul>
          </div>
        )}

        {isAuthError && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <h4 className="text-sm font-medium text-yellow-800">
              Problema de autenticación detectado
            </h4>
            <ul className="list-disc pl-5 mt-2 text-sm text-yellow-700">
              <li>Tu sesión puede haber expirado</li>
              <li>Intenta cerrar sesión y volver a iniciar sesión</li>
              <li>Verifica que estás usando credenciales correctas</li>
            </ul>
          </div>
        )}

        {isConnectionError && (
          <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4">
            <h4 className="text-sm font-medium text-yellow-800">
              Problema de conexión detectado
            </h4>
            <ul className="list-disc pl-5 mt-2 text-sm text-yellow-700">
              <li>Verifica tu conexión a internet</li>
              <li>Confirma que la URL de Supabase en .env.local es correcta</li>
              <li>
                El servicio de Supabase podría estar experimentando problemas
                temporales
              </li>
            </ul>
          </div>
        )}

        {/* Link para regresar */}
        <div className="mt-4">
          <Link href="/" className="text-indigo-600 hover:text-indigo-800">
            ← Volver a la página de inicio
          </Link>
        </div>
      </div>
    );
  }

  // Datos para las métricas
  const metrics = [
    {
      title: "Estudiantes",
      value: stats.totalStudents.toString(),
      description: "Usuarios registrados",
      icon: Users,
      trend: "up",
      trendValue: `+${stats.monthlyGrowth.students}%`,
      color: "blue"
    },
    {
      title: "Certificados",
      value: stats.totalCertificates.toString(),
      description: "Certificados emitidos",
      icon: Award,
      trend: "up",
      trendValue: `+${stats.monthlyGrowth.certificates}%`,
      color: "green"
    },
    {
      title: "Cursos",
      value: stats.totalCourses.toString(),
      description: "Cursos disponibles",
      icon: BookOpen,
      trend: stats.monthlyGrowth.courses > 0 ? "up" : "neutral",
      trendValue: stats.monthlyGrowth.courses > 0 ? `+${stats.monthlyGrowth.courses}%` : undefined,
      color: "purple"
    },
    {
      title: "Instructores",
      value: stats.totalInstructors.toString(),
      description: "Instructores activos",
      icon: TrendingUp,
      trend: "neutral",
      color: "orange"
    }
  ];

  // Acciones rápidas
  const quickActions = [
    {
      title: "Nuevo Certificado",
      description: "Crear un certificado para un estudiante",
      icon: Award,
      href: "/panel-admin/certificados/nuevo",
      color: "blue"
    },
    {
      title: "Gestionar Alumnos",
      description: "Ver y administrar estudiantes",
      icon: Users,
      href: "/panel-admin/alumnos",
      color: "green"
    },
    {
      title: "Crear Curso",
      description: "Agregar un nuevo curso",
      icon: BookOpen,
      href: "/panel-admin/cursos/nuevo",
      color: "purple"
    },
    {
      title: "Configuración",
      description: "Ajustes del sistema",
      icon: Settings,
      href: "/panel-admin/configuracion",
      color: "gray"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <AnimateIn animation="fade" duration="normal">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="mt-1 text-gray-500">
              Vista general del sistema QR CURSE
            </p>
          </div>
          <ModernButton variant="gradient" size="lg" asChild>
            <Link href="/panel-admin/certificados/nuevo">
              <Plus className="w-5 h-5 mr-2" />
              Nuevo Certificado
            </Link>
          </ModernButton>
        </div>
      </AnimateIn>

      {/* Métricas principales */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, lg: 4 }} gap="lg">
        <StaggeredList delay={100}>
          {metrics.map((metric, index) => (
            <StatCard
              key={index}
              title={metric.title}
              value={metric.value}
              description={metric.description}
              icon={metric.icon}
              trend={metric.trend as any}
              trendValue={metric.trendValue}
              variant="glass"
            />
          ))}
        </StaggeredList>
      </ResponsiveGrid>

      {/* Contenido principal */}
      <ResponsiveGrid columns={{ xs: 1, lg: 3 }} gap="lg">
        {/* Área principal - Acciones rápidas */}
        <div className="lg:col-span-2">
          <ModernCard variant="glass" className="h-full">
            <ModernCardHeader>
              <ModernCardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Acciones Rápidas
              </ModernCardTitle>
            </ModernCardHeader>
            <ModernCardContent>
              <ResponsiveGrid columns={{ xs: 1, sm: 2 }} gap="md">
                <StaggeredList delay={50}>
                  {quickActions.map((action, index) => (
                    <ModernCard
                      key={index}
                      variant="neuro"
                      interactive
                      className="group cursor-pointer"
                      asChild
                    >
                      <Link href={action.href}>
                        <ModernCardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg bg-${action.color}-100 text-${action.color}-600 group-hover:scale-110 transition-transform`}>
                              <action.icon className="w-5 h-5" />
                            </div>
                            <div>
                              <h4 className="font-medium text-sm">{action.title}</h4>
                              <p className="text-xs text-muted-foreground">{action.description}</p>
                            </div>
                          </div>
                        </ModernCardContent>
                      </Link>
                    </ModernCard>
                  ))}
                </StaggeredList>
              </ResponsiveGrid>
            </ModernCardContent>
          </ModernCard>
        </div>

        {/* Sidebar - Actividad reciente */}
        <div>
          <ModernCard variant="glass">
            <ModernCardHeader>
              <ModernCardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Actividad Reciente
              </ModernCardTitle>
            </ModernCardHeader>
            <ModernCardContent>
              <div className="space-y-3">
                {stats.recentActivity.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No hay actividad reciente</p>
                  </div>
                ) : (
                  stats.recentActivity.map((activity: any, index: number) => (
                    <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50">
                      <div className="w-2 h-2 rounded-full bg-primary"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-muted-foreground">{activity.time}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ModernCardContent>
          </ModernCard>
        </div>
      </ResponsiveGrid>

      {/* Estado de la base de datos */}
      <ModernCard variant="minimal">
        <ModernCardContent className="p-4">
          <DatabaseStatus />
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}
