"use client";

import Link from "next/link";
import { useEffect, useState, useCallback } from "react";
import { Users, BookOpen, Award, TrendingUp, Plus, Settings, BarChart3, Calendar } from "lucide-react";
import { ModernCard, ModernCardHeader, ModernCardTitle, ModernCardContent } from "@/components/ui/modern-card";
import { StatCard } from "@/components/ui/modern-card";
import { ModernButton } from "@/components/ui/modern-button";
import { AnimateIn, StaggeredList } from "@/components/ui/animations";
import { ResponsiveGrid } from "@/components/ui/responsive";
import { LoadingPage, CardSkeleton } from "@/components/ui/loading";
import { PageError } from "@/components/ui/error-boundary";
import DatabaseStatus from "@/app/components/DatabaseStatus";
import { ensureValidSession, supabase } from "@/lib/supabase";
import { useAsyncState } from "@/hooks/useAsyncState";

export default function AdminDashboard() {
  // Usar el hook useAsyncState para manejar la carga de datos
  const [dashboardState, dashboardActions] = useAsyncState(
    async () => {
      // Verificar si la sesión está activa y válida
      const isSessionValid = await ensureValidSession();
      if (!isSessionValid) {
        throw new Error("Sesión no válida. Por favor, inicia sesión nuevamente.");
      }

      // Obtener conteos de las tablas principales
      const [studentsResult, certificatesResult, coursesResult, instructorsResult] = await Promise.allSettled([
        supabase.from("users").select("*", { count: "exact", head: true }).eq("role", "student"),
        supabase.from("certificates").select("*", { count: "exact", head: true }),
        supabase.from("courses").select("*", { count: "exact", head: true }),
        supabase.from("users").select("*", { count: "exact", head: true }).in("role", ["admin", "instructor"])
      ]);

      const studentCount = studentsResult.status === "fulfilled" ? studentsResult.value.count || 0 : 0;
      const certificateCount = certificatesResult.status === "fulfilled" ? certificatesResult.value.count || 0 : 0;
      const courseCount = coursesResult.status === "fulfilled" ? coursesResult.value.count || 0 : 0;
      const instructorCount = instructorsResult.status === "fulfilled" ? instructorsResult.value.count || 0 : 0;

      return {
        totalStudents: studentCount,
        totalCertificates: certificateCount,
        totalCourses: courseCount,
        totalInstructors: instructorCount,
        recentActivity: [], // Por ahora vacío, se puede implementar después
        monthlyGrowth: {
          students: Math.floor(Math.random() * 20), // Datos simulados por ahora
          certificates: Math.floor(Math.random() * 15),
          courses: Math.floor(Math.random() * 5),
        }
      };
    },
    {
      immediate: true,
      onError: (error) => {
        console.error("Error loading dashboard data:", error);
      }
    }
  );

  const { data: stats, loading, error } = dashboardState;

  // Mostrar loading mientras se cargan los datos
  if (loading) {
    return <LoadingPage message="Cargando dashboard..." />;
  }

  // Mostrar error si ocurre algún problema
  if (error) {
    return (
      <PageError
        title="Error al cargar el dashboard"
        message={error.message}
        onRetry={() => dashboardActions.execute()}
      />
    );
  }

  // Si no hay datos, mostrar mensaje
  if (!stats) {
    return <LoadingPage message="Inicializando dashboard..." />;
  }

  // Datos para las métricas (ahora usando stats del estado)
  const metrics = [
    {
      title: "Estudiantes",
      value: stats.totalStudents.toString(),
      description: "Usuarios registrados",
      icon: Users,
      trend: "up",
      trendValue: `+${stats.monthlyGrowth.students}%`,
      color: "blue"
    },
    {
      title: "Certificados",
      value: stats.totalCertificates.toString(),
      description: "Certificados emitidos",
      icon: Award,
      trend: "up",
      trendValue: `+${stats.monthlyGrowth.certificates}%`,
      color: "green"
    },
    {
      title: "Cursos",
      value: stats.totalCourses.toString(),
      description: "Cursos disponibles",
      icon: BookOpen,
      trend: stats.monthlyGrowth.courses > 0 ? "up" : "neutral",
      trendValue: stats.monthlyGrowth.courses > 0 ? `+${stats.monthlyGrowth.courses}%` : undefined,
      color: "purple"
    },
    {
      title: "Instructores",
      value: stats.totalInstructors.toString(),
      description: "Instructores activos",
      icon: TrendingUp,
      trend: "neutral",
      color: "orange"
    }
  ];

  // Acciones rápidas
  const quickActions = [
    {
      title: "Nuevo Certificado",
      description: "Crear un certificado para un estudiante",
      icon: Award,
      href: "/panel-admin/certificados/nuevo",
      color: "blue"
    },
    {
      title: "Gestionar Alumnos",
      description: "Ver y administrar estudiantes",
      icon: Users,
      href: "/panel-admin/alumnos",
      color: "green"
    },
    {
      title: "Crear Curso",
      description: "Agregar un nuevo curso",
      icon: BookOpen,
      href: "/panel-admin/cursos/nuevo",
      color: "purple"
    },
    {
      title: "Configuración",
      description: "Ajustes del sistema",
      icon: Settings,
      href: "/panel-admin/configuracion",
      color: "gray"
    }
  ];

  return (
    <div className="space-y-8">
      {/* Header */}
      <AnimateIn animation="fade" duration="normal">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="mt-1 text-gray-500">
              Vista general del sistema QR CURSE
            </p>
          </div>
          <ModernButton variant="gradient" size="lg" asChild>
            <Link href="/panel-admin/certificados/nuevo">
              <Plus className="w-5 h-5 mr-2" />
              Nuevo Certificado
            </Link>
          </ModernButton>
        </div>
      </AnimateIn>

      {/* Métricas principales */}
      <ResponsiveGrid columns={{ xs: 1, sm: 2, lg: 4 }} gap="lg">
        <StaggeredList delay={100}>
          {metrics.map((metric, index) => (
            <StatCard
              key={index}
              title={metric.title}
              value={metric.value}
              description={metric.description}
              icon={metric.icon}
              trend={metric.trend as any}
              trendValue={metric.trendValue}
              variant="glass"
            />
          ))}
        </StaggeredList>
      </ResponsiveGrid>

      {/* Contenido principal */}
      <ResponsiveGrid columns={{ xs: 1, lg: 3 }} gap="lg">
        {/* Área principal - Acciones rápidas */}
        <div className="lg:col-span-2">
          <ModernCard variant="glass" className="h-full">
            <ModernCardHeader>
              <ModernCardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Acciones Rápidas
              </ModernCardTitle>
            </ModernCardHeader>
            <ModernCardContent>
              <ResponsiveGrid columns={{ xs: 1, sm: 2 }} gap="md">
                <StaggeredList delay={50}>
                  {quickActions.map((action, index) => (
                    <ModernCard
                      key={index}
                      variant="neuro"
                      interactive
                      className="group cursor-pointer"
                      asChild
                    >
                      <Link href={action.href}>
                        <ModernCardContent className="p-4">
                          <div className="flex items-center gap-3">
                            <div className={`p-2 rounded-lg bg-${action.color}-100 text-${action.color}-600 group-hover:scale-110 transition-transform`}>
                              <action.icon className="w-5 h-5" />
                            </div>
                            <div>
                              <h4 className="font-medium text-sm">{action.title}</h4>
                              <p className="text-xs text-muted-foreground">{action.description}</p>
                            </div>
                          </div>
                        </ModernCardContent>
                      </Link>
                    </ModernCard>
                  ))}
                </StaggeredList>
              </ResponsiveGrid>
            </ModernCardContent>
          </ModernCard>
        </div>

        {/* Sidebar - Actividad reciente */}
        <div>
          <ModernCard variant="glass">
            <ModernCardHeader>
              <ModernCardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Actividad Reciente
              </ModernCardTitle>
            </ModernCardHeader>
            <ModernCardContent>
              <div className="space-y-3">
                {stats.recentActivity.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Calendar className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No hay actividad reciente</p>
                  </div>
                ) : (
                  stats.recentActivity.map((activity: any, index: number) => (
                    <div key={index} className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50">
                      <div className="w-2 h-2 rounded-full bg-primary"></div>
                      <div className="flex-1">
                        <p className="text-sm font-medium">{activity.title}</p>
                        <p className="text-xs text-muted-foreground">{activity.time}</p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </ModernCardContent>
          </ModernCard>
        </div>
      </ResponsiveGrid>

      {/* Estado de la base de datos */}
      <ModernCard variant="minimal">
        <ModernCardContent className="p-4">
          <DatabaseStatus />
        </ModernCardContent>
      </ModernCard>
    </div>
  );
}
