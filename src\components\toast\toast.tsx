"use client";

import { <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Info, X } from "lucide-react";
import React, { createContext, useCallback, useContext, useState } from "react";

type ToastType = "success" | "error" | "info" | "warning";

interface Toast {
  id: string;
  type: ToastType;
  title: string;
  message: string;
}

interface ToastContextType {
  toasts: Toast[];
  showToast: (type: ToastType, title: string, message: string) => void;
  hideToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const showToast = useCallback(
    (type: ToastType, title: string, message: string) => {
      const id = Math.random().toString(36).substr(2, 9);
      setToasts((prevToasts) => [...prevToasts, { id, type, title, message }]);

      // Auto-remove toast after 5 seconds
      setTimeout(() => {
        hideToast(id);
      }, 5000);
    },
    [hideToast],
  );

  const hideToast = useCallback((id: string) => {
    setToasts((prevToasts) => prevToasts.filter((toast) => toast.id !== id));
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, showToast, hideToast }}>
      {children}
      <div className="fixed bottom-4 right-4 z-50 flex flex-col gap-2">
        {toasts.map((toast) => (
          <div
            key={toast.id}
            className={`flex items-start gap-2 p-4 rounded-md shadow-md min-w-[300px] max-w-[450px] animate-slide-up
              ${
                toast.type === "success"
                  ? "bg-green-50 border-l-4 border-green-500"
                  : toast.type === "error"
                    ? "bg-red-50 border-l-4 border-red-500"
                    : toast.type === "warning"
                      ? "bg-yellow-50 border-l-4 border-yellow-500"
                      : "bg-blue-50 border-l-4 border-blue-500"
              }`}
          >
            <div className="flex-shrink-0 mt-0.5">
              {toast.type === "success" && (
                <CheckCircle className="h-5 w-5 text-green-500" />
              )}
              {toast.type === "error" && <X className="h-5 w-5 text-red-500" />}
              {toast.type === "warning" && (
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
              )}
              {toast.type === "info" && (
                <Info className="h-5 w-5 text-blue-500" />
              )}
            </div>
            <div className="flex-1">
              <p
                className={`font-medium text-sm
                ${
                  toast.type === "success"
                    ? "text-green-800"
                    : toast.type === "error"
                      ? "text-red-800"
                      : toast.type === "warning"
                        ? "text-yellow-800"
                        : "text-blue-800"
                }`}
              >
                {toast.title}
              </p>
              <p className="text-sm mt-1 text-gray-600">{toast.message}</p>
            </div>
            <button
              type="button"
              onClick={() => hideToast(toast.id)}
              className="flex-shrink-0 text-gray-400 hover:text-gray-500"
              aria-label="Cerrar notificación"
            >
              <X className="h-4 w-4" aria-hidden="true" />
            </button>
          </div>
        ))}
      </div>
    </ToastContext.Provider>
  );
};

// Convenience functions
export const toast = {
  success: (title: string, message: string) => {
    const context = useContext(ToastContext);
    if (context) {
      context.showToast("success", title, message);
    }
  },
  error: (title: string, message: string) => {
    const context = useContext(ToastContext);
    if (context) {
      context.showToast("error", title, message);
    }
  },
  warning: (title: string, message: string) => {
    const context = useContext(ToastContext);
    if (context) {
      context.showToast("warning", title, message);
    }
  },
  info: (title: string, message: string) => {
    const context = useContext(ToastContext);
    if (context) {
      context.showToast("info", title, message);
    }
  },
};
