openapi: 3.0.3
info:
  title: DOMUS OTEC API
  description: |
    Comprehensive API documentation for the DOMUS OTEC certificate management platform.
    
    This API provides endpoints for user management, authentication, certificate issuance,
    verification, and administrative operations.
    
    ## Authentication
    
    Most endpoints require authentication using JWT tokens obtained through the login endpoint.
    Include the token in the Authorization header: `Bearer <token>`
    
    ## Error Handling
    
    All endpoints return consistent error responses with the following structure:
    ```json
    {
      "success": false,
      "error": {
        "code": "ERROR_CODE",
        "message": "Human readable error message",
        "field": "field_name",
        "details": {}
      }
    }
    ```
    
    ## Rate Limiting
    
    API endpoints are rate limited to prevent abuse. Current limits:
    - Authentication endpoints: 5 requests per minute
    - General endpoints: 100 requests per minute
    - Public verification: 20 requests per minute
    
  version: 1.0.0
  contact:
    name: DOMUS OTEC Development Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: https://domus-otec.vercel.app/api
    description: Production server
  - url: http://localhost:3000/api
    description: Development server

paths:
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate a user with email and password
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  minLength: 6
                  example: "securePassword123"
                remember_me:
                  type: boolean
                  default: false
                  example: true
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      session:
                        $ref: '#/components/schemas/AuthSession'
                      user:
                        $ref: '#/components/schemas/AuthUser'
                      profile:
                        $ref: '#/components/schemas/UserProfile'
                      redirect_url:
                        type: string
                        example: "/panel-alumno"
        '400':
          $ref: '#/components/responses/ValidationError'
        '401':
          $ref: '#/components/responses/AuthenticationError'

  /auth/register:
    post:
      tags:
        - Authentication
      summary: User registration
      description: Register a new user account
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
                - first_name
                - last_name
                - role
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  minLength: 6
                  example: "securePassword123"
                first_name:
                  type: string
                  minLength: 2
                  maxLength: 50
                  example: "María"
                last_name:
                  type: string
                  minLength: 2
                  maxLength: 50
                  example: "González"
                identity_document:
                  type: string
                  example: "12345678-9"
                phone:
                  type: string
                  example: "+56912345678"
                role:
                  type: string
                  enum: [student, instructor]
                  example: "student"
                company_id:
                  type: string
                  format: uuid
                  example: "550e8400-e29b-41d4-a716-************"
      responses:
        '201':
          description: Registration successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      session:
                        $ref: '#/components/schemas/AuthSession'
                      user:
                        $ref: '#/components/schemas/AuthUser'
                      profile:
                        $ref: '#/components/schemas/UserProfile'
                      redirect_url:
                        type: string
                        example: "/panel-alumno"
        '400':
          $ref: '#/components/responses/ValidationError'
        '409':
          $ref: '#/components/responses/ConflictError'

  /auth/logout:
    post:
      tags:
        - Authentication
      summary: User logout
      description: Logout the current user and invalidate session
      security:
        - bearerAuth: []
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  message:
                    type: string
                    example: "Logged out successfully"

  /users:
    get:
      tags:
        - Users
      summary: Search users
      description: Search and filter users with pagination
      security:
        - bearerAuth: []
      parameters:
        - name: role
          in: query
          schema:
            type: string
            enum: [admin, student, instructor]
          example: "student"
        - name: company_id
          in: query
          schema:
            type: string
            format: uuid
        - name: is_active
          in: query
          schema:
            type: boolean
          example: true
        - name: search_query
          in: query
          schema:
            type: string
          example: "john doe"
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
          example: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
          example: 20
        - name: orderBy
          in: query
          schema:
            type: string
            enum: [created_at, first_name, last_name, email]
            default: created_at
        - name: orderDirection
          in: query
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/User'
                      pagination:
                        $ref: '#/components/schemas/Pagination'
        '400':
          $ref: '#/components/responses/ValidationError'
        '401':
          $ref: '#/components/responses/AuthenticationError'

    post:
      tags:
        - Users
      summary: Create user
      description: Create a new user account
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: User created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/ValidationError'
        '401':
          $ref: '#/components/responses/AuthenticationError'
        '409':
          $ref: '#/components/responses/ConflictError'

  /users/{id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      description: Retrieve a specific user by their ID
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
          example: "550e8400-e29b-41d4-a716-************"
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/User'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '401':
          $ref: '#/components/responses/AuthenticationError'

    put:
      tags:
        - Users
      summary: Update user
      description: Update an existing user's information
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: User updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/ValidationError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '401':
          $ref: '#/components/responses/AuthenticationError'

    delete:
      tags:
        - Users
      summary: Delete user
      description: Delete a user account
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: User deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                    example: true
                  data:
                    type: boolean
                    example: true
        '404':
          $ref: '#/components/responses/NotFoundError'
        '401':
          $ref: '#/components/responses/AuthenticationError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  schemas:
    AuthSession:
      type: object
      properties:
        access_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        refresh_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
        expires_at:
          type: integer
          example: 1640995200

    AuthUser:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        email_confirmed_at:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"

    UserProfile:
      type: object
      properties:
        id:
          type: string
          format: uuid
          example: "550e8400-e29b-41d4-a716-************"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        first_name:
          type: string
          example: "María"
        last_name:
          type: string
          example: "González"
        identity_document:
          type: string
          example: "12345678-9"
        phone:
          type: string
          example: "+56912345678"
        role:
          type: string
          enum: [admin, student, instructor]
          example: "student"
        company_id:
          type: string
          format: uuid
          nullable: true
        is_active:
          type: boolean
          example: true
        created_at:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"

    User:
      allOf:
        - $ref: '#/components/schemas/UserProfile'

    CreateUserRequest:
      type: object
      required:
        - email
        - first_name
        - last_name
        - role
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        first_name:
          type: string
          minLength: 2
          maxLength: 50
          example: "Carlos"
        last_name:
          type: string
          minLength: 2
          maxLength: 50
          example: "Rodríguez"
        identity_document:
          type: string
          example: "87654321-0"
        phone:
          type: string
          example: "+56987654321"
        role:
          type: string
          enum: [admin, student, instructor]
          example: "instructor"
        company_id:
          type: string
          format: uuid
          nullable: true

    UpdateUserRequest:
      type: object
      properties:
        first_name:
          type: string
          minLength: 2
          maxLength: 50
        last_name:
          type: string
          minLength: 2
          maxLength: 50
        identity_document:
          type: string
        phone:
          type: string
        role:
          type: string
          enum: [admin, student, instructor]
        company_id:
          type: string
          format: uuid
          nullable: true
        is_active:
          type: boolean

    Pagination:
      type: object
      properties:
        page:
          type: integer
          example: 1
        limit:
          type: integer
          example: 10
        total:
          type: integer
          example: 25
        totalPages:
          type: integer
          example: 3
        hasNext:
          type: boolean
          example: true
        hasPrev:
          type: boolean
          example: false

    Error:
      type: object
      properties:
        success:
          type: boolean
          example: false
        error:
          type: object
          properties:
            code:
              type: string
              example: "VALIDATION_ERROR"
            message:
              type: string
              example: "Invalid input data"
            field:
              type: string
              example: "email"
            details:
              type: object

  responses:
    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            error:
              code: "VALIDATION_ERROR"
              message: "Invalid email format"
              field: "email"

    AuthenticationError:
      description: Authentication error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            error:
              code: "UNAUTHORIZED"
              message: "Authentication required"

    NotFoundError:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            error:
              code: "NOT_FOUND"
              message: "User not found"

    ConflictError:
      description: Conflict error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            success: false
            error:
              code: "DUPLICATE_EMAIL"
              message: "Email already exists"

tags:
  - name: Authentication
    description: User authentication and session management
  - name: Users
    description: User management operations
  - name: Certificates
    description: Certificate issuance and verification
  - name: Courses
    description: Course management
  - name: Companies
    description: Company and organization management
