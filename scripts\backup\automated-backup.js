#!/usr/bin/env node

/**
 * @fileoverview Automated Backup System for DOMUS OTEC
 * 
 * Sistema automatizado de respaldo que incluye:
 * - Respaldo de base de datos Supabase
 * - Respaldo de configuraciones de entorno
 * - Respaldo de archivos de configuración
 * - Respaldo de documentación crítica
 * - Notificaciones de estado
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const crypto = require('crypto');

// Configuración del sistema de respaldo
const BACKUP_CONFIG = {
  // Directorios de respaldo
  backupDir: path.join(process.cwd(), 'backups'),
  tempDir: path.join(process.cwd(), 'temp-backup'),
  
  // Retención de respaldos (días)
  retentionDays: 30,
  
  // Archivos y directorios críticos
  criticalPaths: [
    'package.json',
    'package-lock.json',
    'next.config.js',
    'tailwind.config.js',
    'tsconfig.json',
    'jest.config.js',
    '.env.example',
    'vercel.json',
    'docs/',
    'src/lib/database/schema/',
    'src/lib/types/',
    'scripts/',
  ],
  
  // Configuraciones de entorno a respaldar (sin valores sensibles)
  envKeys: [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_APP_NAME',
    'NODE_ENV',
  ],
};

/**
 * Clase principal del sistema de respaldo
 */
class AutomatedBackupSystem {
  constructor() {
    this.timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.backupId = crypto.randomBytes(8).toString('hex');
    this.backupPath = path.join(BACKUP_CONFIG.backupDir, `backup-${this.timestamp}-${this.backupId}`);
  }

  /**
   * Ejecuta el proceso completo de respaldo
   */
  async run() {
    try {
      console.log('🚀 Iniciando sistema de respaldo automatizado...');
      console.log(`📅 Timestamp: ${this.timestamp}`);
      console.log(`🆔 Backup ID: ${this.backupId}`);
      
      // Crear directorios necesarios
      await this.createDirectories();
      
      // Ejecutar respaldos
      await this.backupCriticalFiles();
      await this.backupEnvironmentConfig();
      await this.backupDatabaseSchema();
      await this.backupDocumentation();
      await this.createBackupManifest();
      
      // Limpiar respaldos antiguos
      await this.cleanupOldBackups();
      
      // Comprimir respaldo
      await this.compressBackup();
      
      console.log('✅ Respaldo completado exitosamente');
      console.log(`📁 Ubicación: ${this.backupPath}.tar.gz`);
      
      return {
        success: true,
        backupId: this.backupId,
        timestamp: this.timestamp,
        path: `${this.backupPath}.tar.gz`,
      };
      
    } catch (error) {
      console.error('❌ Error durante el respaldo:', error.message);
      await this.cleanup();
      throw error;
    }
  }

  /**
   * Crear directorios necesarios para el respaldo
   */
  async createDirectories() {
    console.log('📁 Creando directorios de respaldo...');
    
    await fs.mkdir(BACKUP_CONFIG.backupDir, { recursive: true });
    await fs.mkdir(this.backupPath, { recursive: true });
    await fs.mkdir(path.join(this.backupPath, 'files'), { recursive: true });
    await fs.mkdir(path.join(this.backupPath, 'config'), { recursive: true });
    await fs.mkdir(path.join(this.backupPath, 'docs'), { recursive: true });
    await fs.mkdir(path.join(this.backupPath, 'schema'), { recursive: true });
  }

  /**
   * Respaldar archivos críticos del proyecto
   */
  async backupCriticalFiles() {
    console.log('📄 Respaldando archivos críticos...');
    
    for (const criticalPath of BACKUP_CONFIG.criticalPaths) {
      try {
        const sourcePath = path.join(process.cwd(), criticalPath);
        const targetPath = path.join(this.backupPath, 'files', criticalPath);
        
        // Verificar si el archivo/directorio existe
        try {
          await fs.access(sourcePath);
        } catch {
          console.log(`⚠️  Archivo no encontrado: ${criticalPath}`);
          continue;
        }
        
        // Crear directorio padre si es necesario
        await fs.mkdir(path.dirname(targetPath), { recursive: true });
        
        // Copiar archivo o directorio
        const stats = await fs.stat(sourcePath);
        if (stats.isDirectory()) {
          await this.copyDirectory(sourcePath, targetPath);
        } else {
          await fs.copyFile(sourcePath, targetPath);
        }
        
        console.log(`✅ Respaldado: ${criticalPath}`);
      } catch (error) {
        console.error(`❌ Error respaldando ${criticalPath}:`, error.message);
      }
    }
  }

  /**
   * Respaldar configuración de entorno (sin valores sensibles)
   */
  async backupEnvironmentConfig() {
    console.log('⚙️  Respaldando configuración de entorno...');
    
    const envConfig = {
      timestamp: this.timestamp,
      backupId: this.backupId,
      nodeVersion: process.version,
      platform: process.platform,
      environment: {},
    };
    
    // Respaldar solo variables de entorno no sensibles
    for (const key of BACKUP_CONFIG.envKeys) {
      if (process.env[key]) {
        envConfig.environment[key] = process.env[key];
      }
    }
    
    // Obtener información de dependencias
    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      envConfig.dependencies = {
        production: packageJson.dependencies || {},
        development: packageJson.devDependencies || {},
      };
    } catch (error) {
      console.error('⚠️  No se pudo leer package.json:', error.message);
    }
    
    const configPath = path.join(this.backupPath, 'config', 'environment.json');
    await fs.writeFile(configPath, JSON.stringify(envConfig, null, 2));
    
    console.log('✅ Configuración de entorno respaldada');
  }

  /**
   * Respaldar esquema de base de datos
   */
  async backupDatabaseSchema() {
    console.log('🗄️  Respaldando esquema de base de datos...');
    
    try {
      // Intentar usar Supabase CLI si está disponible
      try {
        const schemaOutput = execSync('supabase db dump --schema-only', { 
          encoding: 'utf8',
          timeout: 30000 
        });
        
        const schemaPath = path.join(this.backupPath, 'schema', 'database-schema.sql');
        await fs.writeFile(schemaPath, schemaOutput);
        
        console.log('✅ Esquema de base de datos respaldado via Supabase CLI');
      } catch (cliError) {
        console.log('⚠️  Supabase CLI no disponible, respaldando archivos de esquema locales...');
        
        // Respaldar archivos de esquema locales si existen
        const schemaDir = path.join(process.cwd(), 'src/lib/database/schema');
        try {
          await fs.access(schemaDir);
          await this.copyDirectory(schemaDir, path.join(this.backupPath, 'schema', 'local'));
          console.log('✅ Archivos de esquema locales respaldados');
        } catch {
          console.log('⚠️  No se encontraron archivos de esquema locales');
        }
      }
    } catch (error) {
      console.error('❌ Error respaldando esquema:', error.message);
    }
  }

  /**
   * Respaldar documentación crítica
   */
  async backupDocumentation() {
    console.log('📚 Respaldando documentación...');
    
    const docsToBackup = [
      'README.md',
      'PLANNING.md',
      'PROJECT_SUMMARY.md',
      'docs/',
    ];
    
    for (const docPath of docsToBackup) {
      try {
        const sourcePath = path.join(process.cwd(), docPath);
        const targetPath = path.join(this.backupPath, 'docs', docPath);
        
        try {
          await fs.access(sourcePath);
        } catch {
          continue;
        }
        
        await fs.mkdir(path.dirname(targetPath), { recursive: true });
        
        const stats = await fs.stat(sourcePath);
        if (stats.isDirectory()) {
          await this.copyDirectory(sourcePath, targetPath);
        } else {
          await fs.copyFile(sourcePath, targetPath);
        }
        
        console.log(`✅ Documentación respaldada: ${docPath}`);
      } catch (error) {
        console.error(`❌ Error respaldando documentación ${docPath}:`, error.message);
      }
    }
  }

  /**
   * Crear manifiesto del respaldo
   */
  async createBackupManifest() {
    console.log('📋 Creando manifiesto del respaldo...');
    
    const manifest = {
      backupId: this.backupId,
      timestamp: this.timestamp,
      version: '1.0.0',
      type: 'automated-backup',
      contents: {
        criticalFiles: BACKUP_CONFIG.criticalPaths,
        environmentConfig: true,
        databaseSchema: true,
        documentation: true,
      },
      metadata: {
        nodeVersion: process.version,
        platform: process.platform,
        cwd: process.cwd(),
        retentionDays: BACKUP_CONFIG.retentionDays,
      },
      checksum: await this.calculateChecksum(),
    };
    
    const manifestPath = path.join(this.backupPath, 'MANIFEST.json');
    await fs.writeFile(manifestPath, JSON.stringify(manifest, null, 2));
    
    console.log('✅ Manifiesto creado');
  }

  /**
   * Limpiar respaldos antiguos
   */
  async cleanupOldBackups() {
    console.log('🧹 Limpiando respaldos antiguos...');
    
    try {
      const backupFiles = await fs.readdir(BACKUP_CONFIG.backupDir);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - BACKUP_CONFIG.retentionDays);
      
      let deletedCount = 0;
      
      for (const file of backupFiles) {
        if (file.startsWith('backup-') && (file.endsWith('.tar.gz') || !file.includes('.'))) {
          const filePath = path.join(BACKUP_CONFIG.backupDir, file);
          const stats = await fs.stat(filePath);
          
          if (stats.mtime < cutoffDate) {
            if (stats.isDirectory()) {
              await fs.rm(filePath, { recursive: true, force: true });
            } else {
              await fs.unlink(filePath);
            }
            deletedCount++;
            console.log(`🗑️  Eliminado respaldo antiguo: ${file}`);
          }
        }
      }
      
      console.log(`✅ Limpieza completada: ${deletedCount} respaldos antiguos eliminados`);
    } catch (error) {
      console.error('⚠️  Error durante la limpieza:', error.message);
    }
  }

  /**
   * Comprimir el respaldo
   */
  async compressBackup() {
    console.log('🗜️  Comprimiendo respaldo...');
    
    try {
      const tarCommand = `tar -czf "${this.backupPath}.tar.gz" -C "${BACKUP_CONFIG.backupDir}" "${path.basename(this.backupPath)}"`;
      execSync(tarCommand, { timeout: 60000 });
      
      // Eliminar directorio sin comprimir
      await fs.rm(this.backupPath, { recursive: true, force: true });
      
      console.log('✅ Respaldo comprimido exitosamente');
    } catch (error) {
      console.error('⚠️  Error comprimiendo respaldo:', error.message);
    }
  }

  /**
   * Calcular checksum del respaldo
   */
  async calculateChecksum() {
    const hash = crypto.createHash('sha256');
    hash.update(this.backupId + this.timestamp);
    return hash.digest('hex');
  }

  /**
   * Copiar directorio recursivamente
   */
  async copyDirectory(source, target) {
    await fs.mkdir(target, { recursive: true });
    const entries = await fs.readdir(source, { withFileTypes: true });
    
    for (const entry of entries) {
      const sourcePath = path.join(source, entry.name);
      const targetPath = path.join(target, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(sourcePath, targetPath);
      } else {
        await fs.copyFile(sourcePath, targetPath);
      }
    }
  }

  /**
   * Limpiar archivos temporales en caso de error
   */
  async cleanup() {
    try {
      await fs.rm(this.backupPath, { recursive: true, force: true });
    } catch {
      // Ignorar errores de limpieza
    }
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  const backup = new AutomatedBackupSystem();
  backup.run()
    .then(result => {
      console.log('🎉 Respaldo completado:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

module.exports = { AutomatedBackupSystem, BACKUP_CONFIG };
