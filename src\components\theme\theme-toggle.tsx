/**
 * @fileoverview Theme Toggle Component
 * 
 * This component provides an accessible theme toggle button with support for
 * light, dark, and system preference modes. Includes proper ARIA labels,
 * keyboard navigation, and visual feedback.
 * 
 * @example Basic Theme Toggle
 * ```tsx
 * import { ThemeToggle } from '@/components/theme/theme-toggle';
 * 
 * function Header() {
 *   return (
 *     <header>
 *       <nav>
 *         <ThemeToggle />
 *       </nav>
 *     </header>
 *   );
 * }
 * ```
 * 
 * @example Theme Toggle with Custom Styling
 * ```tsx
 * <ThemeToggle 
 *   variant="outline" 
 *   size="sm"
 *   className="ml-auto"
 * />
 * ```
 * 
 * <AUTHOR> OTEC Development Team
 * @version 1.0.0
 * @since 2025-01-12
 */

'use client';

import * as React from 'react';
import { Moon, Sun, Monitor } from 'lucide-react';
import { useThemeContext, getEffectiveTheme } from './theme-provider';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

/**
 * Props for the ThemeToggle component
 */
interface ThemeToggleProps {
  /** Button variant */
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  /** Button size */
  size?: 'default' | 'sm' | 'lg' | 'icon';
  /** Additional CSS classes */
  className?: string;
  /** Whether to show labels in dropdown */
  showLabels?: boolean;
  /** Whether to use simple toggle (light/dark only) */
  simple?: boolean;
}

/**
 * Theme toggle button component with dropdown menu
 * 
 * Features:
 * - Three-way toggle: light, dark, system
 * - Accessible with proper ARIA labels
 * - Keyboard navigation support
 * - Visual feedback for current theme
 * - Customizable appearance
 * 
 * @param props - Component configuration
 * @returns JSX element for theme toggle
 */
export function ThemeToggle({
  variant = 'ghost',
  size = 'icon',
  className,
  showLabels = true,
  simple = false,
}: ThemeToggleProps) {
  const { theme, setTheme, systemTheme } = useThemeContext();
  const effectiveTheme = getEffectiveTheme(theme, systemTheme);

  // Simple toggle between light and dark (no system option)
  if (simple) {
    return (
      <Button
        variant={variant}
        size={size}
        className={cn('relative', className)}
        onClick={() => setTheme(effectiveTheme === 'dark' ? 'light' : 'dark')}
        aria-label={`Switch to ${effectiveTheme === 'dark' ? 'light' : 'dark'} mode`}
      >
        <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
        <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
        <span className="sr-only">Toggle theme</span>
      </Button>
    );
  }

  // Full dropdown with light, dark, and system options
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={cn('relative', className)}
          aria-label="Toggle theme menu"
        >
          <Sun className="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[140px]">
        <DropdownMenuItem
          onClick={() => setTheme('light')}
          className={cn(
            'flex items-center gap-2 cursor-pointer',
            theme === 'light' && 'bg-accent'
          )}
        >
          <Sun className="h-4 w-4" />
          {showLabels && <span>Light</span>}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('dark')}
          className={cn(
            'flex items-center gap-2 cursor-pointer',
            theme === 'dark' && 'bg-accent'
          )}
        >
          <Moon className="h-4 w-4" />
          {showLabels && <span>Dark</span>}
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => setTheme('system')}
          className={cn(
            'flex items-center gap-2 cursor-pointer',
            theme === 'system' && 'bg-accent'
          )}
        >
          <Monitor className="h-4 w-4" />
          {showLabels && <span>System</span>}
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

/**
 * Compact theme toggle for mobile or space-constrained layouts
 * 
 * @param props - Component configuration
 * @returns Compact theme toggle component
 */
export function CompactThemeToggle({ className }: { className?: string }) {
  return (
    <ThemeToggle
      variant="ghost"
      size="sm"
      className={cn('h-8 w-8', className)}
      showLabels={false}
      simple={true}
    />
  );
}

/**
 * Theme status indicator component
 * Shows current theme with icon and label
 * 
 * @example
 * ```tsx
 * <ThemeStatus className="text-sm text-muted-foreground" />
 * ```
 */
export function ThemeStatus({ className }: { className?: string }) {
  const { theme, systemTheme } = useThemeContext();
  const effectiveTheme = getEffectiveTheme(theme, systemTheme);

  const getThemeInfo = () => {
    switch (theme) {
      case 'light':
        return { icon: Sun, label: 'Light mode' };
      case 'dark':
        return { icon: Moon, label: 'Dark mode' };
      case 'system':
        return { 
          icon: Monitor, 
          label: `System (${effectiveTheme === 'dark' ? 'Dark' : 'Light'})` 
        };
      default:
        return { icon: Monitor, label: 'System' };
    }
  };

  const { icon: Icon, label } = getThemeInfo();

  return (
    <div className={cn('flex items-center gap-2', className)}>
      <Icon className="h-4 w-4" />
      <span>{label}</span>
    </div>
  );
}

/**
 * Hook for theme-aware styling
 * 
 * @returns Object with theme utilities
 * 
 * @example
 * ```tsx
 * function MyComponent() {
 *   const { isDark, isLight, effectiveTheme } = useThemeAware();
 *   
 *   return (
 *     <div className={isDark ? 'border-white' : 'border-black'}>
 *       Current theme: {effectiveTheme}
 *     </div>
 *   );
 * }
 * ```
 */
export function useThemeAware() {
  const { theme, systemTheme } = useThemeContext();
  const effectiveTheme = getEffectiveTheme(theme, systemTheme);

  return {
    theme,
    systemTheme,
    effectiveTheme,
    isDark: effectiveTheme === 'dark',
    isLight: effectiveTheme === 'light',
    isSystem: theme === 'system',
  };
}

/**
 * Theme-aware conditional rendering component
 * 
 * @example
 * ```tsx
 * <ThemeConditional
 *   light={<LightModeComponent />}
 *   dark={<DarkModeComponent />}
 * />
 * ```
 */
interface ThemeConditionalProps {
  light?: React.ReactNode;
  dark?: React.ReactNode;
  system?: React.ReactNode;
  fallback?: React.ReactNode;
}

export function ThemeConditional({
  light,
  dark,
  system,
  fallback,
}: ThemeConditionalProps) {
  const { theme, effectiveTheme } = useThemeAware();

  if (theme === 'system' && system) {
    return <>{system}</>;
  }

  if (effectiveTheme === 'dark' && dark) {
    return <>{dark}</>;
  }

  if (effectiveTheme === 'light' && light) {
    return <>{light}</>;
  }

  return <>{fallback}</>;
}
