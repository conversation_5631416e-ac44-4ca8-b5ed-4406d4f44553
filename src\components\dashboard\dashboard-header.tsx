/**
 * @fileoverview Dashboard Header Component
 *
 * Componente de cabecera principal para todos los dashboards de DOMUS OTEC.
 * Incluye navegación, búsqueda global, notificaciones y menú de usuario.
 */

"use client";

import { Bell, Menu, Search, User } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ThemeToggle } from "@/components/theme/theme-toggle";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";

interface DashboardHeaderProps {
  onMenuClick?: () => void;
  user?: {
    name: string;
    email: string;
    role: string;
  };
}

/**
 * Componente de cabecera del dashboard con navegación y controles de usuario
 */
export function DashboardHeader({ onMenuClick, user }: DashboardHeaderProps) {
  const pathname = usePathname();

  const navigationItems = [
    { href: "/dashboard", label: "Dashboard" },
    { href: "/certificates", label: "Certificados" },
    { href: "/users", label: "Usuarios" },
    { href: "/courses", label: "Cursos" },
  ];

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        {/* Mobile menu button */}
        <Button
          variant="ghost"
          size="sm"
          className="mr-2 lg:hidden"
          onClick={onMenuClick}
          aria-label="Abrir menú de navegación"
        >
          <Menu className="h-4 w-4" />
        </Button>

        {/* Logo y título */}
        <div className="mr-4 flex">
          <Link
            href="/dashboard"
            className="mr-6 flex items-center space-x-2 hover:opacity-80 transition-opacity"
          >
            <span className="font-bold text-lg">DOMUS OTEC</span>
          </Link>
        </div>

        {/* Navegación principal - oculta en móvil */}
        <nav className="hidden lg:flex items-center space-x-6 text-sm font-medium">
          {navigationItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "transition-colors hover:text-foreground/80",
                pathname === item.href
                  ? "text-foreground"
                  : "text-foreground/60",
              )}
            >
              {item.label}
            </Link>
          ))}
        </nav>

        <div className="flex flex-1 items-center justify-end space-x-2">
          {/* Búsqueda global */}
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <Button
              variant="outline"
              className="relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64"
              aria-label="Búsqueda global"
            >
              <Search className="mr-2 h-4 w-4" />
              <span className="hidden lg:inline-flex">Buscar...</span>
              <span className="lg:hidden">Buscar</span>
              <kbd className="pointer-events-none absolute right-1.5 top-1.5 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
                <span className="text-xs">⌘</span>K
              </kbd>
            </Button>
          </div>

          {/* Notificaciones */}
          <Button
            variant="ghost"
            size="sm"
            aria-label="Notificaciones"
            className="relative"
          >
            <Bell className="h-4 w-4" />
            {/* Indicador de notificaciones */}
            <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full" />
          </Button>

          {/* Toggle de tema */}
          <ThemeToggle />

          {/* Menú de usuario */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="relative h-8 w-8 rounded-full"
                aria-label="Menú de usuario"
              >
                <User className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              {user && (
                <>
                  <div className="flex items-center justify-start gap-2 p-2">
                    <div className="flex flex-col space-y-1 leading-none">
                      <p className="font-medium">{user.name}</p>
                      <p className="w-[200px] truncate text-sm text-muted-foreground">
                        {user.email}
                      </p>
                      <p className="text-xs text-muted-foreground capitalize">
                        {user.role}
                      </p>
                    </div>
                  </div>
                  <DropdownMenuSeparator />
                </>
              )}
              <DropdownMenuItem asChild>
                <Link href="/profile">Perfil</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings">Configuración</Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/help">Ayuda</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-red-600 focus:text-red-600">
                Cerrar Sesión
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}

/**
 * Componente simplificado de cabecera para páginas públicas
 */
export function PublicHeader() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <Link
            href="/"
            className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
          >
            <span className="font-bold text-lg">DOMUS OTEC</span>
          </Link>
        </div>

        <nav className="flex items-center space-x-6 text-sm font-medium ml-6">
          <Link
            href="/verify"
            className="transition-colors hover:text-foreground/80 text-foreground/60"
          >
            Verificar Certificado
          </Link>
          <Link
            href="/about"
            className="transition-colors hover:text-foreground/80 text-foreground/60"
          >
            Acerca de
          </Link>
        </nav>

        <div className="flex flex-1 items-center justify-end space-x-2">
          <ThemeToggle />
          <Button asChild variant="outline" size="sm">
            <Link href="/login">Iniciar Sesión</Link>
          </Button>
        </div>
      </div>
    </header>
  );
}

/**
 * Hook para obtener información del usuario actual
 * TODO: Implementar con el contexto de autenticación real
 */
export function useCurrentUser() {
  // Placeholder - reemplazar con lógica real de autenticación
  return {
    name: "Usuario Demo",
    email: "<EMAIL>",
    role: "admin",
  };
}
