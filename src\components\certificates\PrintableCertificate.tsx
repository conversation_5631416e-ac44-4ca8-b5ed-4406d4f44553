"use client";

import Image from "next/image";
import React, { useRef } from "react";
import { useReactToPrint } from "react-to-print";
import styled from "styled-components";

// Styled components for the certificate layout
const CertificateContainer = styled.div`
  width: 11in; /* Standard US Letter width */
  height: 8.5in; /* Standard US Letter height */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  padding: 50px;
  box-sizing: border-box;
  font-family: 'Arial, sans-serif';
  background: linear-gradient(135deg, #e6f2ff, #ffffff 50%, #e6f2ff);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  page-break-after: always;

  @media print {
    border: none;
    background: linear-gradient(135deg, #e6f2ff, #ffffff 50%, #e6f2ff);
    box-shadow: none;
    margin: 0;
    padding: 0.5in;
    height: 100%;
    width: 100%;
    page-break-after: always;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
`;

const _BackgroundLogo = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 85%;
  height: 85%;
  z-index: 0;
  opacity: 0.05;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
`;

const Title = styled.h1`
  font-size: 3.8em;
  color: #219bf9;
  margin-top: 20px;
  margin-bottom: 10px;
  text-align: center;
  font-weight: 700;
  letter-spacing: 2px;
  position: relative;
  z-index: 2;
  text-transform: uppercase;
`;

const SubTitle = styled.h2`
  font-size: 1.8em;
  color: #5d8df9;
  margin-top: 0;
  margin-bottom: 40px;
  text-align: center;
  font-weight: 500;
  position: relative;
  z-index: 2;
`;

const Statement = styled.p`
  font-size: 1.2em;
  color: #333;
  line-height: 1.5;
  text-align: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 2;
  font-weight: 500;
  max-width: 90%;
  margin-left: auto;
  margin-right: auto;
`;

const Details = styled.div`
  width: 70%;
  margin-bottom: 70px;
  position: relative;
  z-index: 2;
  border: 1px solid #e0e0ff;
  padding: 20px 30px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.5);
`;

const DetailRow = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding: 0 10px;
`;

const Label = styled.span`
  font-weight: bold;
  color: #219bf9;
`;

const Value = styled.span`
  color: #444;
  font-weight: 500;
`;

const Footer = styled.div`
  display: flex;
  justify-content: space-around;
  width: 80%;
  padding-top: 30px;
  border-top: 1px solid #219bf9;
  position: relative;
  z-index: 2;
  margin-top: 30px;
`;

const Signature = styled.div`
  text-align: center;
  margin: 0 30px;
`;

const SignatureLine = styled.div`
  width: 180px;
  height: 1px;
  background-color: #219bf9;
  margin: 10px auto;
`;

const PrintButton = styled.button`
  padding: 10px 20px;
  font-size: 1em;
  background: linear-gradient(90deg, #1DA1F2 0%, #7B3FE4 100%);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(90deg, #0C8BD9 0%, #6232C5 100%);
    box-shadow: 0 4px 12px rgba(29, 161, 242, 0.3);
  }

  @media print {
    display: none;
  }
`;

const BackgroundImage = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  opacity: 0.05;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
`;

const CertificateSeal = styled.div`
  position: absolute;
  bottom: 50px;
  right: 50px;
  width: 140px;
  height: 140px;
  z-index: 1;
  opacity: 0.9;
`;

const QRCodeBox = styled.div`
  position: absolute;
  top: 50px;
  left: 50px;
  width: 150px;
  height: 150px;
  z-index: 999;
  background-color: white;
  border: 1px solid #1DA1F2;
  padding: 5px;
  box-shadow: 0 2px 4px rgba(29, 161, 242, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  border-radius: 8px;

  @media print {
    top: 30px;
    left: 30px;
    box-shadow: none;
    border: 1px solid #1DA1F2;
    background-color: white !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
`;

interface CertificateProps {
  logoSrc: string;
  courseTitle: string;
  participantName: string;
  documentNumber?: string; // RUT del estudiante
  duration: string;
  completionDate: string;
  certificateNumber: string;
  instructorName: string;
  issuingAuthority: string;
  backgroundImageSrc?: string;
  sealImageSrc?: string;
  qrCodeUrl?: string;
  signatureUrl?: string; // URL de la imagen de la firma
}

const PrintableCertificate: React.FC<CertificateProps> = ({
  logoSrc,
  courseTitle,
  participantName,
  documentNumber,
  duration,
  completionDate,
  certificateNumber,
  instructorName,
  issuingAuthority,
  backgroundImageSrc,
  sealImageSrc,
  qrCodeUrl,
  signatureUrl,
}) => {
  const componentRef = useRef<HTMLDivElement>(null);

  const handlePrint = useReactToPrint({
    content: () => componentRef.current,
    documentTitle: `Certificado-${certificateNumber}`,
    onBeforeGetContent: () => {
      return new Promise<void>((resolve) => {
        console.log("Preparando para imprimir...");
        // Dar más tiempo para que se carguen todos los elementos
        setTimeout(() => {
          resolve();
        }, 1000);
      });
    },
    onAfterPrint: () => {
      console.log("Impresión completada");
    },
    removeAfterPrint: true,
    pageStyle: `
      @page {
        size: landscape;
        margin: 0;
      }
      @media print {
        html, body {
          height: 100%;
          margin: 0 !important;
          padding: 0 !important;
          overflow: hidden;
          -webkit-print-color-adjust: exact;
          print-color-adjust: exact;
          color-adjust: exact;
        }
      }
    `,
  });

  return (
    <div>
      <CertificateContainer ref={componentRef}>
        {/* Background elements */}
        {backgroundImageSrc && (
          <BackgroundImage
            style={{ backgroundImage: `url(${backgroundImageSrc})` }}
          />
        )}

        {/* Logo en la parte superior derecha */}
        {logoSrc && (
          <div
            style={{
              position: "absolute",
              top: "50px",
              right: "50px",
              width: "150px",
              height: "150px",
              zIndex: 2,
            }}
          >
            <Image
              src={logoSrc}
              alt="DOMUS Logo"
              fill
              style={{ objectFit: "contain" }}
            />
          </div>
        )}

        {/* QR Code on the left side */}
        {qrCodeUrl && (
          <QRCodeBox>
            <div style={{ position: "relative", width: "90%", height: "90%" }}>
              <Image
                src={qrCodeUrl}
                alt="Código QR de verificación"
                fill
                style={{ objectFit: "contain" }}
              />
            </div>
            <div
              style={{
                fontSize: "12px",
                marginTop: "5px",
                textAlign: "center",
                fontWeight: "bold",
                color: "#1DA1F2",
                backgroundColor: "white",
                padding: "2px 5px",
                borderRadius: "3px",
              }}
            >
              Verificar Certificado
            </div>
          </QRCodeBox>
        )}

        <Title>CERTIFICADO DE FINALIZACIÓN</Title>
        <SubTitle>Curso Profesional</SubTitle>

        <Statement>
          DOMUS OTEC, institución fundada para la formación en capacitaciones
          técnicas, con resolución No. 12345-AB del 2023, certifica que
        </Statement>

        <h2
          style={{
            fontSize: "2.8em",
            color: "#1DA1F2",
            textAlign: "center",
            marginBottom: "5px",
            fontWeight: "bold",
            position: "relative",
            zIndex: 2,
            textTransform: "uppercase",
          }}
        >
          {participantName}
        </h2>

        {documentNumber && (
          <p
            style={{
              fontSize: "1.6em",
              color: "#444",
              textAlign: "center",
              marginBottom: "25px",
              position: "relative",
              zIndex: 2,
              fontWeight: "bold",
              border: "1px solid #1DA1F2",
              display: "inline-block",
              padding: "5px 15px",
              borderRadius: "5px",
              backgroundColor: "rgba(255, 255, 255, 0.7)",
              boxShadow: "0 2px 4px rgba(29, 161, 242, 0.1)",
            }}
          >
            RUT: {documentNumber}
          </p>
        )}

        <Statement>TOMÓ Y TERMINÓ CON ÉXITO EL CURSO:</Statement>

        <h3
          style={{
            fontSize: "2.2em",
            color: "#333",
            textAlign: "center",
            marginBottom: "45px",
            fontWeight: "bold",
            position: "relative",
            zIndex: 2,
            maxWidth: "90%",
            margin: "0 auto 45px",
          }}
        >
          {courseTitle}
        </h3>

        <Details>
          <DetailRow>
            <Label>Intensidad horaria:</Label>
            <Value>{duration}</Value>
          </DetailRow>
          <DetailRow>
            <Label>Fecha de Finalización:</Label>
            <Value>{completionDate}</Value>
          </DetailRow>
          <DetailRow>
            <Label>Número de Certificado:</Label>
            <Value>{certificateNumber}</Value>
          </DetailRow>
          <DetailRow>
            <Label>Ciudad:</Label>
            <Value>Santiago, Chile</Value>
          </DetailRow>
          {/* RUT is now displayed prominently below the participant name, so we can remove it from details */}
        </Details>

        {sealImageSrc && (
          <CertificateSeal>
            <Image
              src={sealImageSrc}
              alt="Sello de certificación"
              fill
              style={{ objectFit: "contain" }}
            />
          </CertificateSeal>
        )}

        <Footer>
          <Signature>
            {signatureUrl ? (
              <div
                style={{
                  position: "relative",
                  width: "180px",
                  height: "60px",
                  marginBottom: "10px",
                }}
              >
                <Image
                  src={signatureUrl}
                  alt="Firma del instructor"
                  fill
                  style={{ objectFit: "contain" }}
                  onError={(e) => {
                    console.error("Error loading signature in certificate");
                    // Mostrar línea de firma en caso de error
                    e.currentTarget.style.display = "none";
                    // Crear y mostrar una línea de firma como fallback
                    const parent = e.currentTarget.parentElement;
                    if (parent) {
                      const line = document.createElement("div");
                      line.style.width = "150px";
                      line.style.height = "1px";
                      line.style.backgroundColor = "#000";
                      line.style.margin = "30px auto 10px";
                      parent.appendChild(line);
                    }
                  }}
                />
              </div>
            ) : (
              <SignatureLine />
            )}
            <p style={{ fontWeight: "bold", marginBottom: "3px" }}>
              {instructorName}
            </p>
            <p style={{ fontSize: "0.9em", color: "#555" }}>Instructor</p>
          </Signature>
          <Signature>
            <SignatureLine />
            <p style={{ fontWeight: "bold", marginBottom: "3px" }}>
              {issuingAuthority}
            </p>
            <p style={{ fontSize: "0.9em", color: "#555" }}>DOMUS</p>
          </Signature>
        </Footer>
      </CertificateContainer>

      <div className="flex flex-col items-center mt-4 print:hidden">
        <PrintButton onClick={handlePrint}>Imprimir Certificado</PrintButton>
        <div className="text-center text-sm text-gray-500 mt-2">
          Haga clic en el botón para imprimir o guardar como PDF
        </div>
        {qrCodeUrl && (
          <div className="mt-4 text-center text-sm text-gray-600">
            <p>
              El certificado incluye un código QR para verificación en la
              esquina superior izquierda.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default PrintableCertificate;
