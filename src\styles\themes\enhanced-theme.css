/**
 * Enhanced Theme System for DOMUS OTEC
 * 
 * This file provides an enhanced theme system with comprehensive CSS variables
 * for light and dark modes, semantic colors, and improved accessibility.
 * 
 * Features:
 * - Comprehensive color system with semantic meanings
 * - Improved contrast ratios for accessibility
 * - Consistent spacing and typography scales
 * - Enhanced component-specific variables
 * - Smooth transitions between themes
 */

/* ============================================================================
 * Root Theme Variables (Light Mode)
 * ============================================================================ */
:root {
    /* Color System - Light Mode */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    /* Surface Colors */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    
    /* Brand Colors (DOMUS OTEC) */
    --primary: 210 100% 20%; /* CSI Blue #003366 */
    --primary-foreground: 0 0% 100%;
    --secondary: 38 92% 58%; /* CSI Yellow #F9B233 */
    --secondary-foreground: 210 100% 20%;
    
    /* Semantic Colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 222.2 84% 4.9%;
    --error: 0 84% 60%;
    --error-foreground: 0 0% 100%;
    --info: 199 89% 48%;
    --info-foreground: 0 0% 100%;
    
    /* Interactive States */
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* Borders and Inputs */
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 20%;
    
    /* Destructive Actions */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    
    /* Chart Colors */
    --chart-1: 210 100% 20%;
    --chart-2: 38 92% 58%;
    --chart-3: 199 89% 48%;
    --chart-4: 142 76% 36%;
    --chart-5: 0 84% 60%;
    
    /* Sidebar/Navigation */
    --sidebar: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 210 100% 20%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96%;
    --sidebar-accent-foreground: 222.2 84% 4.9%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 210 100% 20%;
    
    /* Enhanced Component Variables */
    --header-background: 0 0% 100%;
    --header-foreground: 222.2 84% 4.9%;
    --header-border: 214.3 31.8% 91.4%;
    
    --footer-background: 210 40% 96%;
    --footer-foreground: 215.4 16.3% 46.9%;
    
    --table-header: 210 40% 96%;
    --table-row-even: 0 0% 100%;
    --table-row-odd: 210 40% 98%;
    --table-border: 214.3 31.8% 91.4%;
    
    /* Form Elements */
    --form-background: 0 0% 100%;
    --form-border: 214.3 31.8% 91.4%;
    --form-border-focus: 210 100% 20%;
    --form-placeholder: 215.4 16.3% 46.9%;
    
    /* Status Indicators */
    --status-online: 142 76% 36%;
    --status-offline: 215.4 16.3% 46.9%;
    --status-busy: 38 92% 50%;
    --status-away: 0 84% 60%;
    
    /* Design System */
    --radius: 0.5rem;
    --radius-sm: calc(var(--radius) - 0.125rem);
    --radius-md: var(--radius);
    --radius-lg: calc(var(--radius) + 0.125rem);
    --radius-xl: calc(var(--radius) + 0.25rem);
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* Typography Scale */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    /* Spacing Scale */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Animation Durations */
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    
    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* ============================================================================
 * Dark Theme Variables
 * ============================================================================ */
.dark {
    /* Color System - Dark Mode */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    /* Surface Colors */
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    
    /* Brand Colors (Enhanced for Dark Mode) */
    --primary: 199 89% 48%; /* Lighter blue for better contrast */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 38 92% 58%; /* Keep yellow bright */
    --secondary-foreground: 222.2 84% 4.9%;
    
    /* Semantic Colors (Dark Mode Optimized) */
    --success: 142 76% 45%;
    --success-foreground: 222.2 84% 4.9%;
    --warning: 38 92% 58%;
    --warning-foreground: 222.2 84% 4.9%;
    --error: 0 84% 70%;
    --error-foreground: 222.2 84% 4.9%;
    --info: 199 89% 58%;
    --info-foreground: 222.2 84% 4.9%;
    
    /* Interactive States */
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    
    /* Borders and Inputs */
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 199 89% 48%;
    
    /* Destructive Actions */
    --destructive: 0 84% 70%;
    --destructive-foreground: 222.2 84% 4.9%;
    
    /* Chart Colors (Dark Mode) */
    --chart-1: 199 89% 48%;
    --chart-2: 38 92% 58%;
    --chart-3: 142 76% 45%;
    --chart-4: 0 84% 70%;
    --chart-5: 280 100% 70%;
    
    /* Sidebar/Navigation */
    --sidebar: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 199 89% 48%;
    --sidebar-primary-foreground: 222.2 84% 4.9%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 199 89% 48%;
    
    /* Enhanced Component Variables */
    --header-background: 222.2 84% 4.9%;
    --header-foreground: 210 40% 98%;
    --header-border: 217.2 32.6% 17.5%;
    
    --footer-background: 217.2 32.6% 17.5%;
    --footer-foreground: 215 20.2% 65.1%;
    
    --table-header: 217.2 32.6% 17.5%;
    --table-row-even: 222.2 84% 4.9%;
    --table-row-odd: 217.2 32.6% 15%;
    --table-border: 217.2 32.6% 17.5%;
    
    /* Form Elements */
    --form-background: 222.2 84% 4.9%;
    --form-border: 217.2 32.6% 17.5%;
    --form-border-focus: 199 89% 48%;
    --form-placeholder: 215 20.2% 65.1%;
    
    /* Status Indicators */
    --status-online: 142 76% 45%;
    --status-offline: 215 20.2% 65.1%;
    --status-busy: 38 92% 58%;
    --status-away: 0 84% 70%;
    
    /* Shadows (Dark Mode) */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.3);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.4), 0 2px 4px -2px rgb(0 0 0 / 0.4);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.5), 0 4px 6px -4px rgb(0 0 0 / 0.5);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.6), 0 8px 10px -6px rgb(0 0 0 / 0.6);
}

/* ============================================================================
 * Base Styles with Theme Variables
 * ============================================================================ */

/* Smooth theme transitions */
*,
*::before,
*::after {
  transition-property: background-color, border-color, color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: var(--duration-fast);
}

/* Disable transitions during theme change to prevent flash */
.theme-transitioning *,
.theme-transitioning *::before,
.theme-transitioning *::after {
  transition: none !important;
}
