"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { supabase } from "@/lib/supabase";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { ThemeToggle } from "@/components/theme/theme-toggle";

export default function StudentLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    await supabase.auth.signOut();
    router.push("/login");
  };

  return (
    <div className="min-h-screen bg-background">
      <div className="bg-muted pb-32">
        <nav className="bg-muted border-b border-border lg:border-none">
          <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
            <div className="relative h-16 flex items-center justify-between lg:border-b lg:border-border">
              <div className="px-2 flex items-center lg:px-0">
                <div className="flex-shrink-0">
                  <Link href="/panel-alumno" className="text-foreground font-bold text-xl">
                    DOMUS OTEC | Alumno
                  </Link>
                </div>
                <div className="hidden lg:block lg:ml-10">
                  <div className="flex space-x-4">
                    <Link
                      href="/panel-alumno"
                      className={`${
                        pathname === "/panel-alumno"
                          ? "bg-accent text-accent-foreground"
                          : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"
                      } rounded-md py-2 px-3 text-sm font-medium transition-colors`}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/panel-alumno/certificados"
                      className={`${
                        pathname.startsWith("/panel-alumno/certificados")
                          ? "bg-accent text-accent-foreground"
                          : "text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"
                      } rounded-md py-2 px-3 text-sm font-medium transition-colors`}
                    >
                      Mis Certificados
                    </Link>
                    {/* Notas y Asistencia temporalmente oculto
                    <Link
                      href="/panel-alumno/mis-notas"
                      className={`${
                        pathname === "/panel-alumno/mis-notas"
                          ? "bg-gray-900 text-white"
                          : "text-white hover:bg-gray-700 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Notas y Asistencia
                    </Link>
                    */}
                  </div>
                </div>
              </div>
              <div className="flex lg:hidden">
                {/* Mobile menu button */}
                <button
                  type="button"
                  className="bg-gray-800 p-2 rounded-md inline-flex items-center justify-center text-gray-400 hover:text-white hover:bg-gray-700 hover:bg-opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                  aria-expanded="false"
                  aria-controls="mobile-menu"
                  aria-label="Abrir menú principal"
                >
                  <svg
                    className="block h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </button>
              </div>
              <div className="hidden lg:block lg:ml-4">
                <div className="flex items-center">
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className="flex-shrink-0 bg-gray-800 p-1 text-gray-400 rounded-full hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-white"
                  >
                    <span className="sr-only">Cerrar sesión</span>
                    <svg
                      className="h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </nav>
        <header className="py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-3xl font-bold text-white">
              {pathname === "/panel-alumno" && "Mi Panel de Alumno"}
              {pathname === "/panel-alumno/certificados" && "Mis Certificados"}
              {pathname.startsWith("/panel-alumno/certificados/") && "Detalle de Certificado"}
              {/* Notas y Asistencia temporalmente oculto */}
              {/* {pathname === "/panel-alumno/mis-notas" && "Mis Notas y Asistencia"} */}
            </h1>
          </div>
        </header>
      </div>

      <main className="-mt-32">
        <div className="max-w-7xl mx-auto pb-12 px-4 sm:px-6 lg:px-8">
          <div className="bg-gray-50 rounded-lg shadow px-5 py-6 sm:px-6">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}