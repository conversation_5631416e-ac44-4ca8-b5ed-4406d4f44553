#!/usr/bin/env node

/**
 * @fileoverview AI Context Generation System for DOMUS OTEC
 * 
 * Sistema automatizado para generar contexto optimizado para agentes AI que incluye:
 * - Extracción de estructura del proyecto
 * - Generación de mapas de dependencias
 * - Documentación de APIs y componentes
 * - Contexto de arquitectura y patrones
 * - Guías de desarrollo para AI
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

// Configuración del generador de contexto
const CONTEXT_CONFIG = {
  // Directorios a analizar
  sourcePaths: [
    'src/',
    'docs/',
    'scripts/',
  ],
  
  // Archivos de configuración importantes
  configFiles: [
    'package.json',
    'tsconfig.json',
    'next.config.js',
    'tailwind.config.js',
    'jest.config.js',
    'README.md',
    'PLANNING.md',
  ],
  
  // Extensiones de archivo a incluir
  includeExtensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.md', '.yml', '.yaml'],
  
  // Patrones a excluir
  excludePatterns: [
    'node_modules',
    '.next',
    '.git',
    'dist',
    'build',
    'coverage',
    '*.log',
    '.env*',
  ],
  
  // Límites de contenido
  maxFileSize: 50000, // caracteres
  maxTotalSize: 500000, // caracteres
};

/**
 * Clase principal del generador de contexto AI
 */
class AIContextGenerator {
  constructor() {
    this.timestamp = new Date().toISOString();
    this.contextId = crypto.randomBytes(8).toString('hex');
    this.context = {
      metadata: {},
      projectStructure: {},
      codebase: {},
      documentation: {},
      architecture: {},
      guidelines: {},
    };
    this.totalSize = 0;
  }

  /**
   * Ejecuta el proceso completo de generación de contexto
   */
  async run() {
    try {
      console.log('🤖 Generando contexto para agentes AI...');
      console.log(`📅 Timestamp: ${this.timestamp}`);
      console.log(`🆔 Context ID: ${this.contextId}`);
      
      // Generar contexto
      await this.generateMetadata();
      await this.analyzeProjectStructure();
      await this.extractCodebaseContext();
      await this.processDocumentation();
      await this.analyzeArchitecture();
      await this.generateDevelopmentGuidelines();
      
      // Guardar contexto
      await this.saveContext();
      
      console.log('✅ Contexto AI generado exitosamente');
      console.log(`📊 Tamaño total: ${this.totalSize} caracteres`);
      
      return {
        success: true,
        contextId: this.contextId,
        timestamp: this.timestamp,
        size: this.totalSize,
        path: path.join(process.cwd(), 'ai-context', `context-${this.contextId}.json`),
      };
      
    } catch (error) {
      console.error('❌ Error generando contexto AI:', error.message);
      throw error;
    }
  }

  /**
   * Generar metadata del proyecto
   */
  async generateMetadata() {
    console.log('📋 Generando metadata del proyecto...');
    
    try {
      const packageJson = JSON.parse(await fs.readFile('package.json', 'utf8'));
      
      this.context.metadata = {
        name: packageJson.name || 'domus-otec',
        version: packageJson.version || '1.0.0',
        description: packageJson.description || 'Sistema de gestión de certificados',
        timestamp: this.timestamp,
        contextId: this.contextId,
        framework: 'Next.js',
        language: 'TypeScript',
        database: 'Supabase (PostgreSQL)',
        ui: 'Shadcn/UI + Tailwind CSS',
        testing: 'Jest + React Testing Library',
        deployment: 'Vercel',
        dependencies: {
          production: Object.keys(packageJson.dependencies || {}),
          development: Object.keys(packageJson.devDependencies || {}),
        },
      };
      
      console.log('✅ Metadata generada');
    } catch (error) {
      console.error('⚠️  Error generando metadata:', error.message);
    }
  }

  /**
   * Analizar estructura del proyecto
   */
  async analyzeProjectStructure() {
    console.log('🏗️  Analizando estructura del proyecto...');
    
    const structure = {};
    
    for (const sourcePath of CONTEXT_CONFIG.sourcePaths) {
      try {
        const fullPath = path.join(process.cwd(), sourcePath);
        structure[sourcePath] = await this.buildDirectoryTree(fullPath);
      } catch (error) {
        console.warn(`⚠️  No se pudo analizar: ${sourcePath}`);
      }
    }
    
    this.context.projectStructure = {
      overview: this.generateStructureOverview(structure),
      detailed: structure,
      keyDirectories: this.identifyKeyDirectories(structure),
    };
    
    console.log('✅ Estructura del proyecto analizada');
  }

  /**
   * Extraer contexto del código fuente
   */
  async extractCodebaseContext() {
    console.log('💻 Extrayendo contexto del código...');
    
    const codeContext = {
      services: {},
      components: {},
      types: {},
      utilities: {},
      apis: {},
    };
    
    // Analizar servicios
    await this.extractServicesContext(codeContext.services);
    
    // Analizar componentes
    await this.extractComponentsContext(codeContext.components);
    
    // Analizar tipos
    await this.extractTypesContext(codeContext.types);
    
    // Analizar APIs
    await this.extractAPIContext(codeContext.apis);
    
    this.context.codebase = codeContext;
    console.log('✅ Contexto del código extraído');
  }

  /**
   * Procesar documentación
   */
  async processDocumentation() {
    console.log('📚 Procesando documentación...');
    
    const docs = {};
    
    // Procesar archivos de documentación principales
    const docFiles = ['README.md', 'PLANNING.md', 'PROJECT_SUMMARY.md'];
    
    for (const docFile of docFiles) {
      try {
        const content = await fs.readFile(docFile, 'utf8');
        docs[docFile] = {
          content: this.truncateContent(content),
          summary: this.extractDocumentSummary(content),
        };
      } catch {
        // Archivo no existe
      }
    }
    
    // Procesar documentación en directorio docs/
    try {
      const docsDir = path.join(process.cwd(), 'docs');
      docs.structured = await this.processDocsDirectory(docsDir);
    } catch {
      // Directorio docs no existe
    }
    
    this.context.documentation = docs;
    console.log('✅ Documentación procesada');
  }

  /**
   * Analizar arquitectura del proyecto
   */
  async analyzeArchitecture() {
    console.log('🏛️  Analizando arquitectura...');
    
    const architecture = {
      patterns: this.identifyArchitecturalPatterns(),
      layers: {
        presentation: 'Next.js App Router + React Components',
        business: 'Service Layer Pattern',
        data: 'Repository Pattern + Supabase',
        infrastructure: 'Vercel + Supabase + GitHub Actions',
      },
      principles: [
        'Clean Architecture',
        'Separation of Concerns',
        'Dependency Injection',
        'Type Safety',
        'Responsive Design',
        'Accessibility First',
      ],
      technologies: {
        frontend: ['Next.js 14', 'React 18', 'TypeScript', 'Tailwind CSS', 'Shadcn/UI'],
        backend: ['Next.js API Routes', 'Supabase', 'PostgreSQL'],
        testing: ['Jest', 'React Testing Library', 'MSW'],
        deployment: ['Vercel', 'GitHub Actions'],
      },
    };
    
    this.context.architecture = architecture;
    console.log('✅ Arquitectura analizada');
  }

  /**
   * Generar guías de desarrollo para AI
   */
  async generateDevelopmentGuidelines() {
    console.log('📖 Generando guías de desarrollo...');
    
    const guidelines = {
      codeStyle: {
        language: 'TypeScript',
        formatting: 'Prettier + ESLint',
        naming: 'camelCase para variables, PascalCase para componentes',
        imports: 'Relative imports dentro de packages',
        comments: 'JSDoc para funciones públicas',
      },
      
      componentPatterns: {
        structure: 'Functional components con hooks',
        props: 'TypeScript interfaces para props',
        styling: 'Tailwind CSS classes',
        accessibility: 'ARIA labels y semantic HTML',
      },
      
      servicePatterns: {
        structure: 'Service classes con dependency injection',
        errorHandling: 'ServiceResponse<T> pattern',
        validation: 'Zod schemas',
        testing: 'Jest unit tests con mocks',
      },
      
      fileOrganization: {
        components: 'src/components/ con co-location',
        services: 'src/lib/services/ por dominio',
        types: 'src/lib/types/ compartidos',
        utils: 'src/lib/utils/ funciones auxiliares',
      },
      
      bestPractices: [
        'Usar TypeScript estricto',
        'Implementar error boundaries',
        'Optimizar para performance',
        'Seguir principios de accesibilidad',
        'Escribir tests comprehensivos',
        'Documentar APIs públicas',
      ],
      
      commonTasks: {
        addComponent: 'Crear en src/components/ con props tipadas',
        addService: 'Extender BaseService con métodos específicos',
        addAPI: 'Crear route handler en app/api/',
        addTest: 'Crear archivo .test.ts con casos comprehensivos',
      },
    };
    
    this.context.guidelines = guidelines;
    console.log('✅ Guías de desarrollo generadas');
  }

  /**
   * Guardar contexto generado
   */
  async saveContext() {
    console.log('💾 Guardando contexto...');
    
    const contextDir = path.join(process.cwd(), 'ai-context');
    await fs.mkdir(contextDir, { recursive: true });
    
    // Guardar contexto completo en JSON
    const contextPath = path.join(contextDir, `context-${this.contextId}.json`);
    await fs.writeFile(contextPath, JSON.stringify(this.context, null, 2));
    
    // Generar versión optimizada para AI
    const optimizedContext = this.generateOptimizedContext();
    const optimizedPath = path.join(contextDir, `context-optimized-${this.contextId}.md`);
    await fs.writeFile(optimizedPath, optimizedContext);
    
    // Generar contexto actual (latest)
    const latestPath = path.join(contextDir, 'latest-context.json');
    await fs.writeFile(latestPath, JSON.stringify(this.context, null, 2));
    
    console.log(`✅ Contexto guardado: ${contextPath}`);
  }

  /**
   * Generar contexto optimizado para AI en formato Markdown
   */
  generateOptimizedContext() {
    return `# 🤖 AI Context: DOMUS OTEC Platform

## 📋 Project Overview

**Name:** ${this.context.metadata.name}
**Version:** ${this.context.metadata.version}
**Framework:** ${this.context.metadata.framework}
**Language:** ${this.context.metadata.language}

${this.context.metadata.description}

## 🏗️ Architecture

**Pattern:** Clean Architecture with Service Layer
**Frontend:** Next.js 14 + React 18 + TypeScript
**Backend:** Next.js API Routes + Supabase
**Database:** PostgreSQL (via Supabase)
**UI:** Shadcn/UI + Tailwind CSS
**Testing:** Jest + React Testing Library

## 📁 Project Structure

\`\`\`
${this.generateStructureText()}
\`\`\`

## 🔧 Development Guidelines

### Code Style
- Language: TypeScript (strict mode)
- Components: Functional components with hooks
- Styling: Tailwind CSS utility classes
- State: React hooks + Context API
- Forms: React Hook Form + Zod validation

### File Organization
- Components: \`src/components/\` with co-location
- Services: \`src/lib/services/\` by domain
- Types: \`src/lib/types/\` for shared interfaces
- Utils: \`src/lib/utils/\` for helper functions

### Common Patterns
- Service Layer: Extend BaseService for business logic
- Repository Pattern: Database abstraction layer
- Error Handling: ServiceResponse<T> pattern
- Validation: Zod schemas for type-safe validation
- Testing: Jest unit tests with comprehensive mocks

## 🎯 Key Components

${this.generateKeyComponentsText()}

## 📚 Important Notes

- Always use TypeScript interfaces for props and data
- Follow accessibility guidelines (WCAG 2.1 AA)
- Implement proper error handling and loading states
- Write comprehensive tests for new features
- Use semantic HTML and ARIA labels
- Optimize for performance and SEO

---

*Context generated on ${this.timestamp}*
*Context ID: ${this.contextId}*
`;
  }

  // Métodos auxiliares
  async buildDirectoryTree(dirPath, depth = 0, maxDepth = 3) {
    if (depth > maxDepth) return null;
    
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      const tree = {};
      
      for (const entry of entries) {
        if (this.shouldExclude(entry.name)) continue;
        
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          tree[entry.name] = await this.buildDirectoryTree(fullPath, depth + 1, maxDepth);
        } else if (this.shouldIncludeFile(entry.name)) {
          tree[entry.name] = 'file';
        }
      }
      
      return tree;
    } catch {
      return null;
    }
  }

  shouldExclude(name) {
    return CONTEXT_CONFIG.excludePatterns.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace('*', '.*'));
        return regex.test(name);
      }
      return name === pattern || name.startsWith(pattern);
    });
  }

  shouldIncludeFile(filename) {
    const ext = path.extname(filename);
    return CONTEXT_CONFIG.includeExtensions.includes(ext);
  }

  truncateContent(content) {
    if (content.length <= CONTEXT_CONFIG.maxFileSize) {
      this.totalSize += content.length;
      return content;
    }
    
    const truncated = content.substring(0, CONTEXT_CONFIG.maxFileSize) + '\n\n[... content truncated ...]';
    this.totalSize += truncated.length;
    return truncated;
  }

  generateStructureOverview(structure) {
    return {
      'src/': 'Main source code directory',
      'src/app/': 'Next.js App Router pages and layouts',
      'src/components/': 'Reusable React components',
      'src/lib/': 'Core business logic and utilities',
      'docs/': 'Project documentation',
      'scripts/': 'Automation and utility scripts',
    };
  }

  identifyKeyDirectories(structure) {
    return [
      'src/app/',
      'src/components/',
      'src/lib/services/',
      'src/lib/repositories/',
      'src/lib/types/',
      'docs/',
    ];
  }

  identifyArchitecturalPatterns() {
    return [
      'Service Layer Pattern',
      'Repository Pattern',
      'Adapter Pattern',
      'Component Composition',
      'Custom Hooks Pattern',
      'Provider Pattern',
    ];
  }

  extractDocumentSummary(content) {
    const lines = content.split('\n');
    const firstParagraph = lines.find(line => line.trim().length > 50);
    return firstParagraph ? firstParagraph.substring(0, 200) + '...' : '';
  }

  generateStructureText() {
    return `src/
├── app/                 # Next.js App Router
├── components/          # React components
├── lib/
│   ├── services/       # Business logic
│   ├── repositories/   # Data access
│   └── types/         # TypeScript definitions
docs/                   # Documentation
scripts/               # Automation scripts`;
  }

  generateKeyComponentsText() {
    return `- **AuthService**: User authentication and session management
- **UserService**: User CRUD operations and management
- **CertificateService**: Certificate issuance and verification
- **BaseService**: Common service functionality
- **DashboardLayout**: Main layout component
- **ThemeProvider**: Dark/light theme management`;
  }

  // Métodos de extracción específicos (simplificados para el ejemplo)
  async extractServicesContext(services) {
    // Implementar extracción de contexto de servicios
    services.overview = 'Service layer with BaseService pattern';
  }

  async extractComponentsContext(components) {
    // Implementar extracción de contexto de componentes
    components.overview = 'React components with TypeScript and Tailwind';
  }

  async extractTypesContext(types) {
    // Implementar extracción de contexto de tipos
    types.overview = 'TypeScript interfaces and types';
  }

  async extractAPIContext(apis) {
    // Implementar extracción de contexto de APIs
    apis.overview = 'Next.js API routes with type safety';
  }

  async processDocsDirectory(docsDir) {
    // Implementar procesamiento de directorio de documentación
    return { overview: 'Comprehensive project documentation' };
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  const generator = new AIContextGenerator();
  generator.run()
    .then(result => {
      console.log('🎉 Contexto AI generado:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

module.exports = { AIContextGenerator, CONTEXT_CONFIG };
