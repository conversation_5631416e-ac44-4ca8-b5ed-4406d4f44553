# Supabase Database Documentation

This directory contains comprehensive documentation for the Domus OTEC Supabase database.

## Overview

The Domus OTEC application uses Supabase as its backend database and authentication system. The database is structured around several key entities:

- **Users**: Students, instructors, administrators, and company representatives
- **Courses**: Educational courses offered by the institution
- **Certificates**: Diplomas and certificates issued to students
- **Companies**: Organizations that may enroll employees in courses
- **Modules & Lessons**: Course content structure
- **Attendance & Grades**: Student performance tracking

## Documentation Structure

- [Schema Overview](./schema.md) - High-level overview of the database schema
- [Tables](./tables.md) - Detailed information about each table
- [Relationships](./relationships.md) - Entity relationships and foreign keys
- [Policies](./policies.md) - Row Level Security (RLS) policies
- [Functions](./functions.md) - Database functions
- [Views](./views.md) - Database views
- [Types](./types.md) - Custom types and enums

## Key Features

1. **Row Level Security (RLS)**: The database implements comprehensive RLS policies to ensure data security and proper access control based on user roles.

2. **Role-Based Access**: Different user roles (admin, instructor, student, company_rep) have different permissions.

3. **Certificate Verification**: Public verification of certificates through QR codes and certificate numbers.

4. **Relationship Management**: Proper relationships between users, courses, enrollments, and other entities.

## Database Schemas

The database uses multiple schemas:

- **public**: Main application data
- **auth**: Authentication and user management (managed by Supabase)
- **storage**: File storage (managed by Supabase)
- **realtime**: Real-time subscriptions (managed by Supabase)

## Important Notes

- The database uses UUID as the primary key type for most tables
- Timestamps are stored with timezone information
- Custom types are used for enumerated values
- Row Level Security is implemented for all tables
