"use client";

import Link from "next/link";
import { useEffect, useState } from "react";
import { supabase } from "@/lib/supabase";

// Define types
type Certificate = {
  id: string;
  certificate_number?: string;
  issue_date: string;
  expiry_date: string | null;
  status: string;
  qr_code_url: string | null;
  course_id?: string;
  course?: {
    title: string;
    description: string | null;
  };
};

export default function StudentCertificates() {
  // State
  const [certificates, setCertificates] = useState<Certificate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [_userData, setUserData] = useState<{
    id: string;
    first_name: string;
    last_name: string;
  } | null>(null);

  // Status colors and labels
  const statusColors = {
    ACTIVE: "bg-gray-300 text-gray-800",
    REVOKED: "bg-gray-400 text-gray-800",
    EXPIRED: "bg-gray-200 text-gray-800",
    active: "bg-gray-300 text-gray-800",
    revoked: "bg-gray-400 text-gray-800",
    expired: "bg-gray-200 text-gray-800",
  };

  const statusLabels = {
    ACTIVE: "Activo",
    REVOKED: "Revocado",
    EXPIRED: "Expirado",
    active: "Activo",
    revoked: "Revocado",
    expired: "Expirado",
  };

  // Fetch user data and certificates
  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        setError(null);

        // Get current user
        const {
          data: { user },
        } = await supabase.auth.getUser();

        if (!user) {
          throw new Error("No se pudo obtener información del usuario");
        }

        // Get user details
        const { data: userData, error: userError } = await supabase
          .from("users")
          .select("id, first_name, last_name")
          .eq("id", user.id)
          .single();

        if (userError) throw userError;
        setUserData(userData);

        // Fetch certificates for this student
        const { data: certificatesData, error: certificatesError } =
          await supabase
            .from("certificates")
            .select(`
            id,
            issue_date,
            expiry_date,
            status,
            qr_code_url,
            course_id
          `)
            .eq("user_id", user.id)
            .order("issue_date", { ascending: false });

        if (certificatesError) throw certificatesError;

        // Fetch course data separately if we have certificates
        let certificatesWithCourses = [];
        if (certificatesData && certificatesData.length > 0) {
          // Get all course IDs from certificates
          const courseIds = certificatesData
            .map((cert) => cert.course_id)
            .filter(Boolean);

          if (courseIds.length > 0) {
            // Fetch courses data
            const { data: coursesData } = await supabase
              .from("courses")
              .select("id, title, description")
              .in("id", courseIds);

            // Map courses to certificates
            certificatesWithCourses = certificatesData.map((cert) => {
              const course =
                coursesData?.find((c) => c.id === cert.course_id) || null;
              return {
                ...cert,
                course,
              };
            });
          } else {
            certificatesWithCourses = certificatesData;
          }
        }

        setCertificates(certificatesWithCourses || []);
      } catch (error: any) {
        console.error("Error fetching certificates:", error);
        setError(error.message || "Error al cargar los certificados");
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  return (
    <div>
      {/* Header */}
      <div className="mb-8">
        <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
          Mis Certificados
        </h2>
        <p className="mt-1 text-sm text-gray-500">
          Visualiza y descarga tus certificados emitidos.
        </p>
      </div>

      {/* Error message */}
      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Certificates */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <p className="text-gray-500">Cargando certificados...</p>
          </div>
        ) : certificates.length === 0 ? (
          <div className="text-center py-12 px-6">
            <svg
              className="mx-auto h-12 w-12 text-gray-400"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
            <h3 className="mt-2 text-lg font-medium text-gray-900">
              No tienes certificados
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              Aún no se han emitido certificados a tu nombre.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
            {certificates.map((certificate) => (
              <div
                key={certificate.id}
                className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200"
              >
                <div className="p-5">
                  <div className="flex justify-between items-start">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      {certificate.course?.title || "Curso sin nombre"}
                    </h3>
                    <span
                      className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        statusColors[
                          certificate.status as keyof typeof statusColors
                        ] || "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {statusLabels[
                        certificate.status as keyof typeof statusLabels
                      ] || certificate.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mb-4">
                    {certificate.certificate_number
                      ? `Certificado N° ${certificate.certificate_number}`
                      : `Certificado ID: ${certificate.id.substring(0, 8)}...`}
                  </p>
                  <div className="text-sm text-gray-500 mb-4">
                    <p>
                      Emitido el:{" "}
                      {new Date(certificate.issue_date).toLocaleDateString(
                        "es-ES",
                      )}
                    </p>
                    {certificate.expiry_date && (
                      <p>
                        Válido hasta:{" "}
                        {new Date(certificate.expiry_date).toLocaleDateString(
                          "es-ES",
                        )}
                      </p>
                    )}
                  </div>

                  {/* QR Code section */}
                  {certificate.qr_code_url && (
                    <div className="flex justify-center mb-4">
                      <img
                        src={certificate.qr_code_url}
                        alt="QR Code"
                        className="h-32 w-32 border border-gray-200 p-1 rounded"
                      />
                    </div>
                  )}

                  <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                    <Link
                      href={`/verificar-certificado?id=${certificate.id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-700 hover:text-gray-900 text-sm font-medium"
                    >
                      Verificar
                    </Link>
                    <a
                      href={certificate.qr_code_url || "#"}
                      download={`certificado-${certificate.id.substring(0, 8)}.png`}
                      className={`inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded shadow-sm text-white ${
                        certificate.status === "ACTIVE" ||
                        certificate.status === "active"
                          ? "bg-gray-700 hover:bg-gray-800"
                          : "bg-gray-400 cursor-not-allowed"
                      }`}
                      onClick={(e) => {
                        if (
                          certificate.status !== "ACTIVE" &&
                          certificate.status !== "active"
                        ) {
                          e.preventDefault();
                        }
                      }}
                    >
                      {certificate.status === "ACTIVE" ||
                      certificate.status === "active"
                        ? "Descargar QR"
                        : "No disponible"}
                    </a>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
