# Script para ayudar a seguir el flujo de trabajo Git Flow

param (
    [Parameter(Mandatory=$true)]
    [ValidateSet("feature", "release", "hotfix", "finish-feature", "finish-release", "finish-hotfix")]
    [string]$Action,
    
    [Parameter(Mandatory=$false)]
    [string]$Name
)

# Colores para mensajes
function Write-ColorOutput($ForegroundColor) {
    $fc = $host.UI.RawUI.ForegroundColor
    $host.UI.RawUI.ForegroundColor = $ForegroundColor
    if ($args) {
        Write-Output $args
    }
    else {
        $input | Write-Output
    }
    $host.UI.RawUI.ForegroundColor = $fc
}

function Write-Success($message) {
    Write-ColorOutput Green $message
}

function Write-Info($message) {
    Write-ColorOutput Cyan $message
}

function Write-Warning($message) {
    Write-ColorOutput Yellow $message
}

function Write-Error($message) {
    Write-ColorOutput Red $message
}

# Verificar si hay cambios sin confirmar
function Check-GitStatus {
    $gitStatus = git status --porcelain
    if ($gitStatus) {
        Write-Warning "Hay cambios sin confirmar en el repositorio."
        $confirm = Read-Host "¿Deseas continuar de todos modos? (s/n)"
        if ($confirm -ne "s") {
            Write-Info "Operación cancelada. Confirma o descarta tus cambios antes de continuar."
            exit 1
        }
    }
}

# Iniciar una nueva característica
function Start-Feature {
    param (
        [string]$FeatureName
    )
    
    if (-not $FeatureName) {
        $FeatureName = Read-Host "Ingresa el nombre de la característica (sin el prefijo 'feature/')"
    }
    
    Write-Info "Iniciando nueva característica: $FeatureName"
    Check-GitStatus
    
    # Asegurarse de que estamos en la rama develop
    git checkout develop
    git pull
    
    # Crear la rama feature
    git checkout -b "feature/$FeatureName"
    
    Write-Success "Rama feature/$FeatureName creada. Puedes comenzar a trabajar en tu característica."
}

# Finalizar una característica
function Finish-Feature {
    param (
        [string]$FeatureName
    )
    
    if (-not $FeatureName) {
        # Obtener el nombre de la rama actual
        $currentBranch = git rev-parse --abbrev-ref HEAD
        if ($currentBranch -match "^feature/(.+)$") {
            $FeatureName = $matches[1]
        } else {
            Write-Error "No estás en una rama de característica. Cambia a la rama feature/* que deseas finalizar."
            exit 1
        }
    } else {
        # Si se proporcionó un nombre, asegurarse de que estamos en esa rama
        git checkout "feature/$FeatureName"
    }
    
    Write-Info "Finalizando característica: $FeatureName"
    Check-GitStatus
    
    # Asegurarse de que todos los cambios están confirmados
    $gitStatus = git status --porcelain
    if ($gitStatus) {
        Write-Warning "Hay cambios sin confirmar en la rama feature/$FeatureName."
        $confirm = Read-Host "¿Deseas confirmar estos cambios automáticamente? (s/n)"
        if ($confirm -eq "s") {
            git add .
            git commit -m "Finalizar característica $FeatureName"
        } else {
            Write-Error "Por favor, confirma tus cambios antes de finalizar la característica."
            exit 1
        }
    }
    
    # Fusionar la rama feature en develop
    git checkout develop
    git pull
    git merge --no-ff "feature/$FeatureName" -m "Merge feature/$FeatureName into develop"
    
    # Preguntar si se debe eliminar la rama feature
    $deleteFeature = Read-Host "¿Deseas eliminar la rama feature/$FeatureName? (s/n)"
    if ($deleteFeature -eq "s") {
        git branch -d "feature/$FeatureName"
        
        # Preguntar si se debe eliminar la rama remota
        $pushFeature = Read-Host "¿La rama feature/$FeatureName existe en el repositorio remoto y deseas eliminarla? (s/n)"
        if ($pushFeature -eq "s") {
            git push origin --delete "feature/$FeatureName"
        }
    }
    
    # Preguntar si se debe publicar develop
    $pushDevelop = Read-Host "¿Deseas publicar los cambios en la rama develop? (s/n)"
    if ($pushDevelop -eq "s") {
        git push origin develop
    }
    
    Write-Success "Característica $FeatureName finalizada y fusionada en develop."
}

# Iniciar una nueva versión
function Start-Release {
    param (
        [string]$ReleaseName
    )
    
    if (-not $ReleaseName) {
        $ReleaseName = Read-Host "Ingresa el número de versión (ej. v1.0.0)"
    }
    
    Write-Info "Iniciando nueva versión: $ReleaseName"
    Check-GitStatus
    
    # Asegurarse de que estamos en la rama develop
    git checkout develop
    git pull
    
    # Crear la rama release
    git checkout -b "release/$ReleaseName"
    
    Write-Success "Rama release/$ReleaseName creada. Puedes comenzar a preparar la versión."
}

# Finalizar una versión
function Finish-Release {
    param (
        [string]$ReleaseName
    )
    
    if (-not $ReleaseName) {
        # Obtener el nombre de la rama actual
        $currentBranch = git rev-parse --abbrev-ref HEAD
        if ($currentBranch -match "^release/(.+)$") {
            $ReleaseName = $matches[1]
        } else {
            Write-Error "No estás en una rama de versión. Cambia a la rama release/* que deseas finalizar."
            exit 1
        }
    } else {
        # Si se proporcionó un nombre, asegurarse de que estamos en esa rama
        git checkout "release/$ReleaseName"
    }
    
    Write-Info "Finalizando versión: $ReleaseName"
    Check-GitStatus
    
    # Asegurarse de que todos los cambios están confirmados
    $gitStatus = git status --porcelain
    if ($gitStatus) {
        Write-Warning "Hay cambios sin confirmar en la rama release/$ReleaseName."
        $confirm = Read-Host "¿Deseas confirmar estos cambios automáticamente? (s/n)"
        if ($confirm -eq "s") {
            git add .
            git commit -m "Finalizar versión $ReleaseName"
        } else {
            Write-Error "Por favor, confirma tus cambios antes de finalizar la versión."
            exit 1
        }
    }
    
    # Fusionar la rama release en main
    git checkout main
    git pull
    git merge --no-ff "release/$ReleaseName" -m "Merge release/$ReleaseName into main"
    
    # Etiquetar la versión
    git tag -a $ReleaseName -m "Versión $ReleaseName"
    
    # Fusionar la rama release en develop
    git checkout develop
    git pull
    git merge --no-ff "release/$ReleaseName" -m "Merge release/$ReleaseName into develop"
    
    # Preguntar si se debe eliminar la rama release
    $deleteRelease = Read-Host "¿Deseas eliminar la rama release/$ReleaseName? (s/n)"
    if ($deleteRelease -eq "s") {
        git branch -d "release/$ReleaseName"
        
        # Preguntar si se debe eliminar la rama remota
        $pushRelease = Read-Host "¿La rama release/$ReleaseName existe en el repositorio remoto y deseas eliminarla? (s/n)"
        if ($pushRelease -eq "s") {
            git push origin --delete "release/$ReleaseName"
        }
    }
    
    # Preguntar si se deben publicar los cambios
    $pushChanges = Read-Host "¿Deseas publicar los cambios y etiquetas? (s/n)"
    if ($pushChanges -eq "s") {
        git push origin main
        git push origin develop
        git push origin --tags
    }
    
    Write-Success "Versión $ReleaseName finalizada, etiquetada y fusionada en main y develop."
}

# Iniciar un hotfix
function Start-Hotfix {
    param (
        [string]$HotfixName
    )
    
    if (-not $HotfixName) {
        $HotfixName = Read-Host "Ingresa el nombre del hotfix (ej. v1.0.1 o error-critico)"
    }
    
    Write-Info "Iniciando nuevo hotfix: $HotfixName"
    Check-GitStatus
    
    # Asegurarse de que estamos en la rama main
    git checkout main
    git pull
    
    # Crear la rama hotfix
    git checkout -b "hotfix/$HotfixName"
    
    Write-Success "Rama hotfix/$HotfixName creada. Puedes comenzar a corregir el error."
}

# Finalizar un hotfix
function Finish-Hotfix {
    param (
        [string]$HotfixName
    )
    
    if (-not $HotfixName) {
        # Obtener el nombre de la rama actual
        $currentBranch = git rev-parse --abbrev-ref HEAD
        if ($currentBranch -match "^hotfix/(.+)$") {
            $HotfixName = $matches[1]
        } else {
            Write-Error "No estás en una rama de hotfix. Cambia a la rama hotfix/* que deseas finalizar."
            exit 1
        }
    } else {
        # Si se proporcionó un nombre, asegurarse de que estamos en esa rama
        git checkout "hotfix/$HotfixName"
    }
    
    Write-Info "Finalizando hotfix: $HotfixName"
    Check-GitStatus
    
    # Asegurarse de que todos los cambios están confirmados
    $gitStatus = git status --porcelain
    if ($gitStatus) {
        Write-Warning "Hay cambios sin confirmar en la rama hotfix/$HotfixName."
        $confirm = Read-Host "¿Deseas confirmar estos cambios automáticamente? (s/n)"
        if ($confirm -eq "s") {
            git add .
            git commit -m "Finalizar hotfix $HotfixName"
        } else {
            Write-Error "Por favor, confirma tus cambios antes de finalizar el hotfix."
            exit 1
        }
    }
    
    # Preguntar si se debe crear una etiqueta
    $createTag = Read-Host "¿Deseas crear una etiqueta para este hotfix? (s/n)"
    $tagName = ""
    if ($createTag -eq "s") {
        $tagName = Read-Host "Ingresa el nombre de la etiqueta (ej. v1.0.1)"
    }
    
    # Fusionar la rama hotfix en main
    git checkout main
    git pull
    git merge --no-ff "hotfix/$HotfixName" -m "Merge hotfix/$HotfixName into main"
    
    # Crear etiqueta si se solicitó
    if ($createTag -eq "s" -and $tagName) {
        git tag -a $tagName -m "Hotfix $HotfixName"
    }
    
    # Fusionar la rama hotfix en develop
    git checkout develop
    git pull
    git merge --no-ff "hotfix/$HotfixName" -m "Merge hotfix/$HotfixName into develop"
    
    # Preguntar si se debe eliminar la rama hotfix
    $deleteHotfix = Read-Host "¿Deseas eliminar la rama hotfix/$HotfixName? (s/n)"
    if ($deleteHotfix -eq "s") {
        git branch -d "hotfix/$HotfixName"
        
        # Preguntar si se debe eliminar la rama remota
        $pushHotfix = Read-Host "¿La rama hotfix/$HotfixName existe en el repositorio remoto y deseas eliminarla? (s/n)"
        if ($pushHotfix -eq "s") {
            git push origin --delete "hotfix/$HotfixName"
        }
    }
    
    # Preguntar si se deben publicar los cambios
    $pushChanges = Read-Host "¿Deseas publicar los cambios y etiquetas? (s/n)"
    if ($pushChanges -eq "s") {
        git push origin main
        git push origin develop
        if ($createTag -eq "s" -and $tagName) {
            git push origin --tags
        }
    }
    
    Write-Success "Hotfix $HotfixName finalizado y fusionado en main y develop."
}

# Ejecutar la acción solicitada
switch ($Action) {
    "feature" { Start-Feature -FeatureName $Name }
    "release" { Start-Release -ReleaseName $Name }
    "hotfix" { Start-Hotfix -HotfixName $Name }
    "finish-feature" { Finish-Feature -FeatureName $Name }
    "finish-release" { Finish-Release -ReleaseName $Name }
    "finish-hotfix" { Finish-Hotfix -HotfixName $Name }
}
