"use client";

import React, { useState, useEffect, forwardRef, useImperativeHandle, useCallback, useMemo } from "react";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { PlusCircle, Trash2, ArrowUpCircle, ArrowDownCircle, Loader2 } from "lucide-react";
import { v4 as uuidv4 } from 'uuid';
import * as mcpClient from "@/lib/mcpClient";

interface CourseObjective {
  id: string;
  course_id: string;
  description: string;
  order_num: number;
  created_at?: string;
  updated_at?: string;
  isNew?: boolean;
  isDeleted?: boolean;
}

interface CourseObjectivesManagerProps {
  courseId: string;
}

const CourseObjectivesManager = forwardRef(({ courseId }: CourseObjectivesManagerProps, ref) => {
  const [objectives, setObjectives] = useState<CourseObjective[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Memoizar la función fetchObjectives para evitar recreaciones innecesarias
  const fetchObjectives = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (courseId === "new") {
        // Si es un curso nuevo, inicializar con un objetivo vacío
        setObjectives([
          {
            id: uuidv4(),
            course_id: courseId,
            description: "",
            order_num: 1,
            isNew: true
          }
        ]);
        return;
      }

      // Usar el MCP Server para obtener los objetivos con caché
      const cacheKey = `course_objectives_${courseId}`;
      const cachedData = sessionStorage.getItem(cacheKey);

      if (cachedData) {
        // Usar datos en caché si están disponibles
        setObjectives(JSON.parse(cachedData));
        setLoading(false);

        // Actualizar en segundo plano, usando la sección específica para mejorar el rendimiento
        mcpClient.fetchCourseContent(courseId, 'objectives')
          .then(courseData => {
            const objectives = courseData.objectives || [];
            setObjectives(objectives);
            sessionStorage.setItem(cacheKey, JSON.stringify(objectives));
          })
          .catch(err => {
            console.error("Error actualizando objetivos en segundo plano:", err);
          });
      } else {
        // Obtener datos frescos si no hay caché, usando la sección específica para mejorar el rendimiento
        const courseData = await mcpClient.fetchCourseContent(courseId, 'objectives');
        const objectives = courseData.objectives || [];
        setObjectives(objectives);
        sessionStorage.setItem(cacheKey, JSON.stringify(objectives));
      }
    } catch (err: any) {
      console.error("Error fetching course objectives:", err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [courseId]);

  // Efecto para cargar los objetivos al montar el componente
  useEffect(() => {
    fetchObjectives();
  }, [fetchObjectives]);

  // Optimizar la función handleAddObjective con useCallback para evitar recreaciones
  const handleAddObjective = useCallback(async () => {
    // Usar una función de actualización de estado para garantizar el estado más reciente
    setObjectives(currentObjectives => {
      const newOrder = currentObjectives.length > 0
        ? Math.max(...currentObjectives.filter(o => !o.isDeleted).map(o => o.order_num)) + 1
        : 1;

      return [
        ...currentObjectives,
        {
          id: uuidv4(),
          course_id: courseId,
          description: "",
          order_num: newOrder,
          isNew: true
        }
      ];
    });

    // Retrasar el auto-guardado para permitir que la UI se actualice primero
    if (courseId !== "new") {
      setTimeout(async () => {
        try {
          setSaving(true);
          // Obtener el estado más reciente para guardar
          const currentObjectives = objectives;
          await mcpClient.saveCourseContent(
            courseId,
            [], // No estamos guardando contenido aquí
            currentObjectives, // Guardamos los objetivos actualizados
            [] // No estamos guardando marcos legales aquí
          );

          // Actualizar la caché después de guardar
          const cacheKey = `course_objectives_${courseId}`;
          sessionStorage.setItem(cacheKey, JSON.stringify(currentObjectives));
        } catch (err: any) {
          console.error("Error auto-saving after adding objective:", err);
        } finally {
          setSaving(false);
        }
      }, 0);
    }
  }, [courseId, objectives]);

  // Optimizar la función handleRemoveObjective con useCallback
  const handleRemoveObjective = useCallback(async (index: number) => {
    setObjectives(currentObjectives => {
      const updatedObjectives = [...currentObjectives];

      if (updatedObjectives[index].isNew) {
        // Si es un objetivo nuevo, simplemente lo eliminamos del array
        updatedObjectives.splice(index, 1);
      } else {
        // Si es un objetivo existente, lo marcamos como eliminado
        updatedObjectives[index] = {
          ...updatedObjectives[index],
          isDeleted: true
        };
      }

      return updatedObjectives;
    });

    // Auto-save after removing an objective con retraso para mejorar la UI
    if (courseId !== "new") {
      setTimeout(async () => {
        try {
          setSaving(true);
          // Obtener el estado más reciente
          const currentObjectives = objectives;
          await mcpClient.saveCourseContent(
            courseId,
            [], // No estamos guardando contenido aquí
            currentObjectives, // Guardamos los objetivos actualizados
            [] // No estamos guardando marcos legales aquí
          );

          // Actualizar la caché
          const cacheKey = `course_objectives_${courseId}`;
          sessionStorage.setItem(cacheKey, JSON.stringify(currentObjectives));
        } catch (err: any) {
          console.error("Error auto-saving after removing objective:", err);
        } finally {
          setSaving(false);
        }
      }, 0);
    }
  }, [courseId, objectives]);

  // Debounce timer for auto-saving after typing
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  // Optimizar la función handleObjectiveChange con useCallback
  const handleObjectiveChange = useCallback((index: number, value: string) => {
    // Usar función de actualización para garantizar el estado más reciente
    setObjectives(currentObjectives => {
      const updatedObjectives = [...currentObjectives];
      updatedObjectives[index] = {
        ...updatedObjectives[index],
        description: value
      };
      return updatedObjectives;
    });

    // Auto-save after typing with debounce (1.5 seconds)
    if (courseId !== "new") {
      // Clear previous timer if it exists
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      // Set new timer con tiempo reducido para mejor experiencia
      const timer = setTimeout(async () => {
        try {
          setSaving(true);
          // Obtener el estado más reciente
          const currentObjectives = objectives;
          await mcpClient.saveCourseContent(
            courseId,
            [], // No estamos guardando contenido aquí
            currentObjectives, // Guardamos los objetivos actualizados
            [] // No estamos guardando marcos legales aquí
          );

          // Actualizar la caché
          const cacheKey = `course_objectives_${courseId}`;
          sessionStorage.setItem(cacheKey, JSON.stringify(currentObjectives));
        } catch (err: any) {
          console.error("Error auto-saving after changing objective:", err);
        } finally {
          setSaving(false);
        }
      }, 1000); // Reducido de 1500ms a 1000ms para mejor respuesta

      setDebounceTimer(timer);
    }
  }, [courseId, objectives, debounceTimer]);

  // Optimizar la función handleMoveObjective con useCallback
  const handleMoveObjective = useCallback((index: number, direction: 'up' | 'down') => {
    setObjectives(currentObjectives => {
      if (
        (direction === 'up' && index === 0) ||
        (direction === 'down' && index === currentObjectives.filter(o => !o.isDeleted).length - 1)
      ) {
        return currentObjectives; // No se puede mover más arriba/abajo
      }

      const updatedObjectives = [...currentObjectives];
      const visibleObjectives = updatedObjectives.filter(o => !o.isDeleted);

      if (direction === 'up') {
        const prevIndex = updatedObjectives.indexOf(visibleObjectives[index - 1]);
        const currentIndex = updatedObjectives.indexOf(visibleObjectives[index]);

        // Intercambiar order_num
        const tempOrder = updatedObjectives[prevIndex].order_num;
        updatedObjectives[prevIndex].order_num = updatedObjectives[currentIndex].order_num;
        updatedObjectives[currentIndex].order_num = tempOrder;
      } else {
        const nextIndex = updatedObjectives.indexOf(visibleObjectives[index + 1]);
        const currentIndex = updatedObjectives.indexOf(visibleObjectives[index]);

        // Intercambiar order_num
        const tempOrder = updatedObjectives[nextIndex].order_num;
        updatedObjectives[nextIndex].order_num = updatedObjectives[currentIndex].order_num;
        updatedObjectives[currentIndex].order_num = tempOrder;
      }

      // Ordenar el array por order_num
      updatedObjectives.sort((a, b) => a.order_num - b.order_num);

      return updatedObjectives;
    });

    // Auto-guardar después de mover (con retraso para mejorar la UI)
    if (courseId !== "new") {
      setTimeout(async () => {
        try {
          setSaving(true);
          await mcpClient.saveCourseContent(
            courseId,
            [], // No estamos guardando contenido aquí
            objectives, // Guardamos los objetivos actualizados
            [] // No estamos guardando marcos legales aquí
          );

          // Actualizar la caché
          const cacheKey = `course_objectives_${courseId}`;
          sessionStorage.setItem(cacheKey, JSON.stringify(objectives));
        } catch (err: any) {
          console.error("Error auto-saving after moving objective:", err);
        } finally {
          setSaving(false);
        }
      }, 0);
    }
  }, [courseId, objectives]);

  // Optimizar la función saveObjectives con useCallback
  const saveObjectives = useCallback(async () => {
    if (courseId === "new") {
      // Si es un curso nuevo, no guardamos los objetivos todavía
      return true;
    }

    try {
      setSaving(true);

      // Usar el MCP Server para guardar los objetivos
      await mcpClient.saveCourseContent(
        courseId,
        [], // No estamos guardando contenido aquí
        objectives, // Solo guardamos los objetivos
        [] // No estamos guardando marcos legales aquí
      );

      // Actualizar la caché después de guardar
      const cacheKey = `course_objectives_${courseId}`;
      sessionStorage.setItem(cacheKey, JSON.stringify(objectives));

      toast({
        title: "Objetivos guardados",
        description: "Los objetivos del curso se han guardado correctamente.",
      });

      return true;
    } catch (err: any) {
      console.error("Error saving course objectives:", err);
      toast({
        title: "Error al guardar",
        description: err.message,
        variant: "destructive"
      });
      return false;
    } finally {
      setSaving(false);
    }
  }, [courseId, objectives]);

  // Exponer el método saveObjectives a través de la referencia
  useImperativeHandle(ref, () => ({
    saveObjectives
  }));

  // Memoizar la lista de objetivos filtrados para evitar recálculos innecesarios
  const filteredObjectives = useMemo(() => {
    return objectives.filter(objective => !objective.isDeleted);
  }, [objectives]);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Objetivos del Curso</CardTitle>
        <CardDescription>
          Define los objetivos que los participantes alcanzarán al completar este curso
        </CardDescription>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <div className="space-y-4">
            {filteredObjectives.map((objective, index) => (
              <div key={objective.id} className="flex items-start gap-2">
                <div className="flex flex-col items-center gap-1 mt-2">
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    onClick={() => handleMoveObjective(index, 'up')}
                    disabled={index === 0 || saving}
                    className="h-7 w-7"
                  >
                    <ArrowUpCircle className="h-5 w-5" />
                  </Button>
                  <Button
                    type="button"
                    size="icon"
                    variant="ghost"
                    onClick={() => handleMoveObjective(index, 'down')}
                    disabled={index === filteredObjectives.length - 1 || saving}
                    className="h-7 w-7"
                  >
                    <ArrowDownCircle className="h-5 w-5" />
                  </Button>
                </div>
                <div className="flex-1">
                  <Input
                    value={objective.description}
                    onChange={(e) => handleObjectiveChange(index, e.target.value)}
                    placeholder="Describe el objetivo del curso"
                    disabled={saving}
                  />
                </div>
                <Button
                  type="button"
                  size="icon"
                  variant="ghost"
                  onClick={() => handleRemoveObjective(index)}
                  disabled={saving}
                  className="h-10 w-10 text-red-500 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-5 w-5" />
                </Button>
              </div>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={handleAddObjective}
              disabled={saving}
              className="w-full"
            >
              {saving ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <PlusCircle className="h-4 w-4 mr-2" />
              )}
              Añadir Objetivo
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
});

export default CourseObjectivesManager;
