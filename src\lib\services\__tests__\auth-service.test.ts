/**
 * @fileoverview Unit tests for AuthService class
 * 
 * Tests authentication operations including login, registration, password management,
 * and profile updates with comprehensive error handling and validation scenarios.
 */

import { AuthService, LoginRequest, RegisterRequest, PasswordResetRequest, PasswordUpdateRequest } from '../auth-service';
import { RepositoryFactory } from '../../repositories';
import { AuthAdapter } from '../../adapters/auth';

// Mock dependencies
const mockRepositoryFactory = {
  users: {
    findOne: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
} as unknown as RepositoryFactory;

const mockAuthAdapter = {
  signInWithPassword: jest.fn(),
  signUp: jest.fn(),
  signOut: jest.fn(),
  resetPassword: jest.fn(),
  updatePassword: jest.fn(),
  getSession: jest.fn(),
  getUser: jest.fn(),
} as unknown as <PERSON>th<PERSON><PERSON>pt<PERSON>;

describe('AuthService', () => {
  let authService: AuthService;

  beforeEach(() => {
    jest.clearAllMocks();
    authService = new AuthService(mockRepositoryFactory, mockAuthAdapter);
  });

  describe('login', () => {
    const validLoginRequest: LoginRequest = {
      email: '<EMAIL>',
      password: 'password123',
      remember_me: true,
    };

    it('should successfully login a user', async () => {
      const mockAuthResult = {
        data: {
          session: { access_token: 'token', refresh_token: 'refresh' },
          user: { id: 'user-id', email: '<EMAIL>' },
        },
        error: null,
      };

      const mockProfile = {
        id: 'user-id',
        first_name: 'John',
        last_name: 'Doe',
        role: 'student',
      };

      mockAuthAdapter.signInWithPassword = jest.fn().mockResolvedValue(mockAuthResult);
      mockRepositoryFactory.users.findOne = jest.fn().mockResolvedValue(mockProfile);

      const result = await authService.login(validLoginRequest);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        session: mockAuthResult.data.session,
        user: mockAuthResult.data.user,
        profile: mockProfile,
        redirect_url: '/panel-alumno',
      });
    });

    it('should return validation error for missing email', async () => {
      const invalidRequest = { ...validLoginRequest, email: '' };

      const result = await authService.login(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.field).toBe('email');
    });

    it('should return validation error for invalid email format', async () => {
      const invalidRequest = { ...validLoginRequest, email: 'invalid-email' };

      const result = await authService.login(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.field).toBe('email');
    });

    it('should return validation error for missing password', async () => {
      const invalidRequest = { ...validLoginRequest, password: '' };

      const result = await authService.login(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.field).toBe('password');
    });

    it('should handle authentication failure', async () => {
      const mockAuthResult = {
        data: { session: null, user: null },
        error: { message: 'Invalid credentials' },
      };

      mockAuthAdapter.signInWithPassword = jest.fn().mockResolvedValue(mockAuthResult);

      const result = await authService.login(validLoginRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('AUTH_FAILED');
    });

    it('should handle user profile not found', async () => {
      const mockAuthResult = {
        data: {
          session: { access_token: 'token', refresh_token: 'refresh' },
          user: { id: 'user-id', email: '<EMAIL>' },
        },
        error: null,
      };

      mockAuthAdapter.signInWithPassword = jest.fn().mockResolvedValue(mockAuthResult);
      mockRepositoryFactory.users.findOne = jest.fn().mockResolvedValue(null);

      const result = await authService.login(validLoginRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('NOT_FOUND');
    });
  });

  describe('register', () => {
    const validRegisterRequest: RegisterRequest = {
      email: '<EMAIL>',
      password: 'password123',
      first_name: 'Jane',
      last_name: 'Doe',
      role: 'student',
    };

    it('should successfully register a new user', async () => {
      const mockAuthResult = {
        data: {
          session: { access_token: 'token', refresh_token: 'refresh' },
          user: { id: 'new-user-id', email: '<EMAIL>' },
        },
        error: null,
      };

      const mockProfile = {
        id: 'new-user-id',
        email: '<EMAIL>',
        first_name: 'Jane',
        last_name: 'Doe',
        role: 'student',
      };

      mockAuthAdapter.signUp = jest.fn().mockResolvedValue(mockAuthResult);
      mockRepositoryFactory.users.create = jest.fn().mockResolvedValue(mockProfile);

      const result = await authService.register(validRegisterRequest);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        session: mockAuthResult.data.session,
        user: mockAuthResult.data.user,
        profile: mockProfile,
        redirect_url: '/panel-alumno',
      });
    });

    it('should return validation error for missing required fields', async () => {
      const invalidRequest = { ...validRegisterRequest, first_name: '' };

      const result = await authService.register(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.field).toBe('first_name');
    });

    it('should return validation error for short password', async () => {
      const invalidRequest = { ...validRegisterRequest, password: '123' };

      const result = await authService.register(invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.field).toBe('password');
    });

    it('should handle registration failure', async () => {
      const mockAuthResult = {
        data: { session: null, user: null },
        error: { message: 'Email already exists' },
      };

      mockAuthAdapter.signUp = jest.fn().mockResolvedValue(mockAuthResult);

      const result = await authService.register(validRegisterRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('AUTH_FAILED');
    });
  });

  describe('requestPasswordReset', () => {
    it('should successfully request password reset', async () => {
      const request: PasswordResetRequest = { email: '<EMAIL>' };
      
      mockAuthAdapter.resetPassword = jest.fn().mockResolvedValue({
        data: {},
        error: null,
      });

      const result = await authService.requestPasswordReset(request);

      expect(result.success).toBe(true);
      expect(mockAuthAdapter.resetPassword).toHaveBeenCalledWith('<EMAIL>');
    });

    it('should return validation error for invalid email', async () => {
      const request: PasswordResetRequest = { email: 'invalid-email' };

      const result = await authService.requestPasswordReset(request);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('updatePassword', () => {
    it('should successfully update password', async () => {
      const request: PasswordUpdateRequest = {
        current_password: 'oldpassword',
        new_password: 'newpassword123',
      };

      mockAuthAdapter.updatePassword = jest.fn().mockResolvedValue({
        data: { user: { id: 'user-id' } },
        error: null,
      });

      const result = await authService.updatePassword(request);

      expect(result.success).toBe(true);
      expect(mockAuthAdapter.updatePassword).toHaveBeenCalledWith('newpassword123');
    });

    it('should return validation error for short new password', async () => {
      const request: PasswordUpdateRequest = {
        current_password: 'oldpassword',
        new_password: '123',
      };

      const result = await authService.updatePassword(request);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.field).toBe('new_password');
    });
  });

  describe('logout', () => {
    it('should successfully logout user', async () => {
      mockAuthAdapter.signOut = jest.fn().mockResolvedValue({
        error: null,
      });

      const result = await authService.logout();

      expect(result.success).toBe(true);
      expect(mockAuthAdapter.signOut).toHaveBeenCalled();
    });

    it('should handle logout failure', async () => {
      mockAuthAdapter.signOut = jest.fn().mockResolvedValue({
        error: { message: 'Logout failed' },
      });

      const result = await authService.logout();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('AUTH_FAILED');
    });
  });

  describe('getCurrentUser', () => {
    it('should return current user with profile', async () => {
      const mockUser = { id: 'user-id', email: '<EMAIL>' };
      const mockProfile = { id: 'user-id', first_name: 'John', last_name: 'Doe' };

      mockAuthAdapter.getUser = jest.fn().mockResolvedValue({
        data: { user: mockUser },
        error: null,
      });
      mockRepositoryFactory.users.findOne = jest.fn().mockResolvedValue(mockProfile);

      const result = await authService.getCurrentUser();

      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        user: mockUser,
        profile: mockProfile,
      });
    });

    it('should handle no authenticated user', async () => {
      mockAuthAdapter.getUser = jest.fn().mockResolvedValue({
        data: { user: null },
        error: null,
      });

      const result = await authService.getCurrentUser();

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('NOT_AUTHENTICATED');
    });
  });

  describe('helper methods', () => {
    it('should determine correct redirect URL based on role', () => {
      // This would test the private getRedirectUrl method if it were public
      // For now, we test it indirectly through login/register tests
      expect(true).toBe(true); // Placeholder
    });
  });
});
