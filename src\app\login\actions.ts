'use server';

import { createClient } from '@supabase/supabase-js'; // Use standard client
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { type Session } from '@supabase/supabase-js';

// Define cookie options (adjust domain/path/secure as needed for production)
const cookieOptions = {
  path: '/',
  maxAge: 60 * 60 * 24 * 7, // Example: 1 week
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const,
};

export async function login(formData: FormData) {
  const email = String(formData.get('email'));
  const password = String(formData.get('password'));
  const redirectTo = String(formData.get('redirectTo') || '/');
  // Get the cookie store
  const cookieStore = await cookies();

  // Use standard Supabase client
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  // Try to sign in
  const { data, error: signInError } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (signInError || !data.session) {
    console.error('Login Action Error:', signInError);
    const message = signInError?.message || 'Login failed. Please try again.';
    return redirect(`/login?error=${encodeURIComponent(message)}&redirectTo=${encodeURIComponent(redirectTo)}`);
  }

  const { session } = data;
  const { user } = session;

  // --- Manually set auth cookies ---
  // This mimics what @supabase/ssr helpers usually do.
  // The cookie name format is typically 'sb-<project-ref>-auth-token'
  // IMPORTANT: Replace <project-ref> with your actual Supabase project reference if known,
  // otherwise, this might not work correctly. Let's assume a generic name for now,
  // but this is a major potential point of failure.
  // A safer way would be to inspect the cookies set by Supabase in a working scenario.
  const cookieName = `sb-auth-token`; // Placeholder - NEEDS VERIFICATION
  try {
      cookieStore.set(cookieName, JSON.stringify(session), cookieOptions);
      console.log("Login Action: Manually set auth cookie.");
  } catch (error) {
      console.error("Login Action: Failed to set auth cookie:", error);
      // Redirect back with an error if cookie setting fails
      return redirect(`/login?error=${encodeURIComponent('Failed to set session cookie.')}&redirectTo=${encodeURIComponent(redirectTo)}`);
  }

  // No need to update last_sign_in as Supabase Auth automatically updates last_sign_in_at
  // in auth.users table when a user signs in

  // --- Fetch user role ---
  let userRole = 'student'; // Default role
  try {
      const { data: profile, error: profileError } = await supabase
          .from("users")
          .select("role")
          .eq("id", user.id)
          .single();

      if (profileError) {
          console.warn("Login Action: Error fetching user profile:", profileError);
          if (profileError.code === 'PGRST116') {
             console.log("Login Action: User profile not found, assuming default role.");
          } else {
            throw profileError;
          }
      } else if (profile?.role) {
          userRole = profile.role.toLowerCase();
      }
  } catch (error) {
      console.error("Login Action: Error processing user profile:", error);
      userRole = 'student';
      // Optionally redirect to login with profile error message
      // return redirect(`/login?error=${encodeURIComponent('Could not verify user profile.')}&redirectTo=${encodeURIComponent(redirectTo)}`);
  }

  // --- Determine final redirect path ---
  let finalRedirectPath = userRole === 'admin' ? '/panel-admin' : '/panel-alumno';
  const isValidRedirect = redirectTo === '/panel-admin' || redirectTo === '/panel-alumno';

  if (isValidRedirect) {
      if (userRole === 'admin') {
          finalRedirectPath = redirectTo;
      } else if (userRole === 'student' && redirectTo === '/panel-alumno') {
          finalRedirectPath = redirectTo;
      }
  }

  console.log(`Login Action: Redirecting to ${finalRedirectPath}`);
  return redirect(finalRedirectPath);
}
