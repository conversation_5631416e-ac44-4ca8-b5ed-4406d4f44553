/**
 * @fileoverview User Service - Business logic for user management operations
 *
 * This service provides comprehensive user management functionality including
 * user creation, profile management, search and filtering, bulk operations,
 * and user statistics. It serves as the business logic layer between the
 * presentation layer and the data repositories.
 *
 * Features:
 * - User CRUD operations with validation
 * - Advanced search and filtering capabilities
 * - Bulk user creation and management
 * - User statistics and analytics
 * - Role-based access control integration
 * - Profile management and updates
 * - User activation/deactivation
 * - Company association management
 *
 * @example Basic User Operations
 * ```typescript
 * import { userService } from '@/lib/services';
 *
 * // Create a new user
 * const createResult = await userService.createUser({
 *   email: '<EMAIL>',
 *   first_name: '<PERSON>',
 *   last_name: '<PERSON><PERSON>',
 *   role: 'student',
 *   identity_document: '12345678-9',
 *   phone: '+***********'
 * });
 *
 * if (createResult.success) {
 *   console.log('User created:', createResult.data.id);
 * }
 *
 * // Search users with filters
 * const searchResult = await userService.searchUsers({
 *   role: 'student',
 *   is_active: true,
 *   search_query: 'jane',
 *   page: 1,
 *   limit: 10
 * });
 * ```
 *
 * @example Bulk Operations
 * ```typescript
 * // Bulk create users
 * const bulkResult = await userService.bulkCreateUsers({
 *   users: [
 *     { email: '<EMAIL>', first_name: 'User', last_name: 'One', role: 'student' },
 *     { email: '<EMAIL>', first_name: 'User', last_name: 'Two', role: 'student' }
 *   ],
 *   skip_duplicates: true
 * });
 *
 * if (bulkResult.success) {
 *   console.log(`Created ${bulkResult.data.created.length} users`);
 * }
 * ```
 *
 * <AUTHOR> OTEC Development Team
 * @version 1.0.0
 * @since 2025-01-12
 */

import { BaseService, ServiceResponse, PaginationOptions, PaginatedResponse } from './base-service';
import { User } from '../adapters/database/types';
import { RepositoryFactory } from '../repositories';

// ============================================================================
// User Service Types
// ============================================================================

/**
 * Request payload for creating new users
 *
 * @example
 * ```typescript
 * const createRequest: CreateUserRequest = {
 *   email: '<EMAIL>',
 *   first_name: 'Jane',
 *   last_name: 'Doe',
 *   identity_document: '12345678-9',
 *   phone: '+***********',
 *   role: 'student',
 *   company_id: 'company-uuid-123'
 * };
 * ```
 */
export interface CreateUserRequest {
  /** User's email address (must be unique) */
  email: string;
  /** User's first name */
  first_name: string;
  /** User's last name */
  last_name: string;
  /** User's identity document (RUT, DNI, etc.) */
  identity_document?: string;
  /** User's phone number */
  phone?: string;
  /** User's role in the system */
  role: 'admin' | 'student' | 'instructor';
  /** Associated company ID for corporate users */
  company_id?: string;
}

/**
 * Request payload for updating existing users
 *
 * @example
 * ```typescript
 * const updateRequest: UpdateUserRequest = {
 *   first_name: 'John',
 *   last_name: 'Smith',
 *   phone: '+56987654321',
 *   is_active: true
 * };
 * ```
 */
export interface UpdateUserRequest {
  /** Updated first name */
  first_name?: string;
  /** Updated last name */
  last_name?: string;
  /** Updated identity document */
  identity_document?: string;
  /** Updated phone number */
  phone?: string;
  /** Updated user role */
  role?: 'admin' | 'student' | 'instructor';
  /** Updated company association */
  company_id?: string;
  /** Updated active status */
  is_active?: boolean;
}

/**
 * Search and filter options for user queries
 *
 * @example
 * ```typescript
 * const searchOptions: UserSearchOptions = {
 *   role: 'student',
 *   company_id: 'company-uuid',
 *   is_active: true,
 *   search_query: 'john doe',
 *   page: 1,
 *   limit: 20,
 *   orderBy: 'created_at',
 *   orderDirection: 'desc'
 * };
 * ```
 */
export interface UserSearchOptions extends PaginationOptions {
  /** Filter by user role */
  role?: 'admin' | 'student' | 'instructor';
  /** Filter by company association */
  company_id?: string;
  /** Filter by active status */
  is_active?: boolean;
  /** Search query for name, email, or identity document */
  search_query?: string;
}

/**
 * Request payload for bulk user creation operations
 *
 * @example
 * ```typescript
 * const bulkRequest: BulkCreateUsersRequest = {
 *   users: [
 *     { email: '<EMAIL>', first_name: 'User', last_name: 'One', role: 'student' },
 *     { email: '<EMAIL>', first_name: 'User', last_name: 'Two', role: 'student' }
 *   ],
 *   skip_duplicates: true
 * };
 * ```
 */
export interface BulkCreateUsersRequest {
  /** Array of users to create */
  users: CreateUserRequest[];
  /** Whether to skip users with duplicate emails */
  skip_duplicates?: boolean;
}

// ============================================================================
// User Service Implementation
// ============================================================================

export class UserService extends BaseService {
  constructor(repositories: RepositoryFactory) {
    super(repositories);
  }

  // ============================================================================
  // User CRUD Operations
  // ============================================================================

  async createUser(request: CreateUserRequest): Promise<ServiceResponse<User>> {
    this.logInfo('Creating user', { email: request.email, role: request.role });

    // Validation
    const validationError = this.validateCreateUserRequest(request);
    if (validationError) {
      return validationError;
    }

    // Check for unique email
    const emailExists = await this.repositories.users.findByEmail(request.email);
    if (emailExists) {
      return this.conflictError('User with this email already exists');
    }

    // Check for unique identity document if provided
    if (request.identity_document) {
      const identityExists = await this.repositories.users.findByIdentityDocument(
        request.identity_document
      );
      if (identityExists) {
        return this.conflictError('User with this identity document already exists');
      }
    }

    return this.handleRepositoryOperation(
      async () => {
        const userData = {
          ...request,
          first_name: this.sanitizeString(request.first_name),
          last_name: this.sanitizeString(request.last_name),
          email: request.email.toLowerCase().trim(),
          is_active: true,
          created_at: this.formatDate(new Date()),
          updated_at: this.formatDate(new Date())
        };

        return await this.repositories.users.create(userData);
      },
      'Failed to create user'
    );
  }

  async getUserById(id: string): Promise<ServiceResponse<User>> {
    const validationError = this.validateUUID(id, 'user_id');
    if (validationError) {
      return validationError;
    }

    return this.handleRepositoryOperation(
      async () => {
        const user = await this.repositories.users.findById(id);
        if (!user) {
          throw new Error('User not found');
        }
        return user;
      },
      'Failed to get user'
    );
  }

  async updateUser(id: string, request: UpdateUserRequest): Promise<ServiceResponse<User>> {
    this.logInfo('Updating user', { id, updates: Object.keys(request) });

    const validationError = this.validateUUID(id, 'user_id');
    if (validationError) {
      return validationError;
    }

    // Check if user exists
    const existingUser = await this.repositories.users.findById(id);
    if (!existingUser) {
      return this.notFoundError('User', id);
    }

    // Validate identity document uniqueness if being updated
    if (request.identity_document &&
        request.identity_document !== existingUser.identity_document) {
      const identityExists = await this.repositories.users.findByIdentityDocument(
        request.identity_document
      );
      if (identityExists && identityExists.id !== id) {
        return this.conflictError('User with this identity document already exists');
      }
    }

    return this.handleRepositoryOperation(
      async () => {
        const updateData = {
          ...request,
          first_name: request.first_name ? this.sanitizeString(request.first_name) : undefined,
          last_name: request.last_name ? this.sanitizeString(request.last_name) : undefined,
          updated_at: this.formatDate(new Date())
        };

        // Remove undefined values
        Object.keys(updateData).forEach(key => {
          if (updateData[key as keyof typeof updateData] === undefined) {
            delete updateData[key as keyof typeof updateData];
          }
        });

        return await this.repositories.users.update(id, updateData);
      },
      'Failed to update user'
    );
  }

  async deleteUser(id: string): Promise<ServiceResponse<boolean>> {
    this.logInfo('Deleting user', { id });

    const validationError = this.validateUUID(id, 'user_id');
    if (validationError) {
      return validationError;
    }

    // Check if user exists
    const existingUser = await this.repositories.users.findById(id);
    if (!existingUser) {
      return this.notFoundError('User', id);
    }

    // Soft delete by deactivating instead of hard delete
    return this.handleRepositoryOperation(
      async () => {
        await this.repositories.users.deactivateUser(id);
        return true;
      },
      'Failed to delete user'
    );
  }

  // ============================================================================
  // User Search and Filtering
  // ============================================================================

  async searchUsers(options: UserSearchOptions = {}): Promise<ServiceResponse<PaginatedResponse<User>>> {
    const { page, limit, offset } = this.validatePaginationOptions(options);

    return this.handleRepositoryOperation(
      async () => {
        let users: User[] = [];
        let total = 0;

        if (options.search_query) {
          users = await this.repositories.users.searchUsers(options.search_query);
          total = users.length;

          // Apply pagination to search results
          users = users.slice(offset, offset + limit);
        } else {
          // Build filters
          const filters = [];

          if (options.role) {
            filters.push({ column: 'role', operator: 'eq' as const, value: options.role });
          }

          if (options.company_id) {
            filters.push({ column: 'company_id', operator: 'eq' as const, value: options.company_id });
          }

          if (options.is_active !== undefined) {
            filters.push({ column: 'is_active', operator: 'eq' as const, value: options.is_active });
          }

          // Get total count
          total = await this.repositories.users.count(filters);

          // Get paginated results
          users = await this.repositories.users.findMany(filters, {
            limit,
            offset,
            orderBy: options.orderBy ? [{
              column: options.orderBy,
              ascending: options.orderDirection !== 'desc'
            }] : [{ column: 'created_at', ascending: false }]
          });
        }

        return {
          data: users,
          pagination: this.calculatePagination(total, page, limit)
        };
      },
      'Failed to search users'
    );
  }

  async getUsersByRole(role: 'admin' | 'student' | 'instructor'): Promise<ServiceResponse<User[]>> {
    return this.handleRepositoryOperation(
      async () => {
        return await this.repositories.users.findByRole(role);
      },
      `Failed to get users by role: ${role}`
    );
  }

  // ============================================================================
  // Bulk Operations
  // ============================================================================

  async bulkCreateUsers(request: BulkCreateUsersRequest): Promise<ServiceResponse<{
    created: User[];
    skipped: CreateUserRequest[];
    errors: { user: CreateUserRequest; error: string }[];
  }>> {
    this.logInfo('Bulk creating users', { count: request.users.length });

    const created: User[] = [];
    const skipped: CreateUserRequest[] = [];
    const errors: { user: CreateUserRequest; error: string }[] = [];

    for (const userData of request.users) {
      try {
        // Check for duplicates if skip_duplicates is enabled
        if (request.skip_duplicates) {
          const existingUser = await this.repositories.users.findByEmail(userData.email);
          if (existingUser) {
            skipped.push(userData);
            continue;
          }
        }

        const result = await this.createUser(userData);
        if (result.success && result.data) {
          created.push(result.data);
        } else {
          errors.push({
            user: userData,
            error: result.error?.message || 'Unknown error'
          });
        }
      } catch (error: any) {
        errors.push({
          user: userData,
          error: error.message || 'Unknown error'
        });
      }
    }

    return this.success({
      created,
      skipped,
      errors
    }, `Bulk operation completed: ${created.length} created, ${skipped.length} skipped, ${errors.length} errors`);
  }

  // ============================================================================
  // User Statistics
  // ============================================================================

  async getUserStatistics(): Promise<ServiceResponse<{
    total: number;
    active: number;
    byRole: Record<string, number>;
    recentlyCreated: number;
  }>> {
    return this.handleRepositoryOperation(
      async () => {
        const stats = await this.repositories.users.getUserStats();

        // Get recently created users (last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const recentUsers = await this.repositories.users.findMany([
          { column: 'created_at', operator: 'gte', value: thirtyDaysAgo.toISOString() }
        ]);

        return {
          ...stats,
          recentlyCreated: recentUsers.length
        };
      },
      'Failed to get user statistics'
    );
  }

  // ============================================================================
  // Validation Helpers
  // ============================================================================

  private validateCreateUserRequest(request: CreateUserRequest): ServiceResponse | null {
    // Required fields
    let error = this.validateRequired(request.email, 'email');
    if (error) return error;

    error = this.validateRequired(request.first_name, 'first_name');
    if (error) return error;

    error = this.validateRequired(request.last_name, 'last_name');
    if (error) return error;

    error = this.validateRequired(request.role, 'role');
    if (error) return error;

    // Email format
    error = this.validateEmail(request.email);
    if (error) return error;

    // Name lengths
    error = this.validateLength(request.first_name, 'first_name', 1, 100);
    if (error) return error;

    error = this.validateLength(request.last_name, 'last_name', 1, 100);
    if (error) return error;

    // Role validation
    if (!['admin', 'student', 'instructor'].includes(request.role)) {
      return this.validationError('role', 'Role must be admin, student, or instructor');
    }

    // Company ID validation if provided
    if (request.company_id) {
      error = this.validateUUID(request.company_id, 'company_id');
      if (error) return error;
    }

    return null;
  }
}
