"use client";

import { FormEvent, useEffect, useState } from "react";
import { useRouter, useParams } from "next/navigation"; // Import useParams
import { supabase } from "@/lib/supabase";
import Link from "next/link";

// Define a more specific type for the user data if possible
// based on the actual table structure from the image
type UserData = {
  id: string;
  first_name: string | null;
  last_name: string | null;
  role: string | null;
  company_id: string | null;
  is_active: boolean | null;
  phone: string | null; // Supabase returns numeric as string/number depending on client
  identity_document: string | null; // ID document (e.g., RUT)
  email: string | null;
  // full_name is derived, not directly fetched/updated usually
};

export default function EditStudent() { // Remove params from props
  const router = useRouter();
  const params = useParams(); // Use the hook to get params
  const studentId = params.id as string; // Get id from hook result, assert as string

  // Form state - Initialize with empty strings or appropriate defaults
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [role, setRole] = useState("");
  const [companyId, setCompanyId] = useState("");
  const [isActive, setIsActive] = useState(true); // Default to true based on image
  const [phone, setPhone] = useState("");
  const [documentoIdentidad, setDocumentoIdentidad] = useState(""); // Corrected state name
  const [email, setEmail] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [resetPassword, setResetPassword] = useState(false);

  // UI states
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Update formErrors type to include all validated fields
  const [formErrors, setFormErrors] = useState<{
    firstName?: string;
    lastName?: string;
    email?: string;
    newPassword?: string;
    confirmPassword?: string;
    role?: string; // Add if role needs validation
    documentoIdentidad?: string; // Add if needed
  }>({});
  const [success, setSuccess] = useState(false);

  // Fetch student data
  useEffect(() => {
    // Ensure studentId is available before fetching
    if (!studentId) {
        // This case might not be strictly necessary if Next.js guarantees id exists for [id] routes
        // but it's safe to keep.
        console.error("ID de alumno no encontrado en los parámetros.");
        setError("ID de alumno no válido.");
        setLoading(false);
        return;
    }
    async function fetchStudentData() {
      try {
        setLoading(true);
        setError(null);
        const { data, error: fetchError } = await supabase
          .from("users")
          .select("id, first_name, last_name, role, company_id, is_active, phone, identity_document, email") // Using English column name
          .eq("id", studentId)
          .single<UserData>(); // Use the specific type

        if (fetchError) {
            // Handle specific errors like user not found (PGRST116)
            if (fetchError.code === 'PGRST116') {
                 throw new Error("Alumno no encontrado o no tienes permiso para verlo.");
            }
            throw fetchError;
        }
        if (!data) throw new Error("Alumno no encontrado"); // Fallback if data is null without error

        // Set state with fallbacks for null values from DB
        setFirstName(data.first_name || "");
        setLastName(data.last_name || "");
        setRole(data.role || "student"); // Default role if null? Adjust as needed
        setCompanyId(data.company_id || "");
        setIsActive(data.is_active ?? true); // Default to true if null
        setPhone(data.phone || "");
        setDocumentoIdentidad(data.identity_document || ""); // Using English column name
        setEmail(data.email || "");
        // No need to setFullName state if it's derived

      } catch (error: any) {
        console.error("Error fetching student data:", error);
        setError(error.message || "Error al cargar los datos del alumno");
      } finally {
        setLoading(false);
      }
    }

    fetchStudentData();
  }, [studentId]); // Dependency array remains the same

  // Validate form
  const validateForm = () => {
    const currentErrors: typeof formErrors = {}; // Use the same type as state
    let isValid = true;

    // Validate first name
    if (!firstName.trim()) {
      currentErrors.firstName = "El nombre es obligatorio";
      isValid = false;
    }

    // Validate last name
    if (!lastName.trim()) {
      currentErrors.lastName = "El apellido es obligatorio";
      isValid = false;
    }

     // Validate role (example: ensure it's not empty)
     if (!role.trim()) {
      currentErrors.role = "El rol es obligatorio";
      isValid = false;
    }

    // Validate email
    if (!email.trim()) {
      currentErrors.email = "El correo electrónico es obligatorio";
      isValid = false;
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      currentErrors.email = "El correo electrónico no es válido";
      isValid = false;
    }

    // Validate password if reset is checked
    if (resetPassword) {
      if (!newPassword) {
        currentErrors.newPassword = "La nueva contraseña es obligatoria";
        isValid = false;
      } else if (newPassword.length < 6) {
        currentErrors.newPassword = "La contraseña debe tener al menos 6 caracteres";
        isValid = false;
      }

      if (newPassword !== confirmPassword) {
        currentErrors.confirmPassword = "Las contraseñas no coinciden";
        isValid = false;
      }
    }

    setFormErrors(currentErrors);
    return isValid;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(false);

    if (!validateForm()) {
      return;
    }

    setSaving(true);

    // Construct the update payload, handling potential nulls for optional fields
    // Use Partial<UserData> and add full_name separately
    const updatePayload: Partial<Omit<UserData, 'id'>> & { first_name: string } = {
        first_name: firstName,
        last_name: lastName,
        role: role,
        company_id: companyId || null,
        is_active: isActive,
        phone: phone || null,
        identity_document: documentoIdentidad || null, // Using English column name
        // full_name: `${firstName} ${lastName}`.trim(), // Recalculate full_name
        email: email,
    };


    try {
      // 1. Update user in users table
      const { error: profileError } = await supabase
        .from("users")
        .update(updatePayload)
        .eq("id", studentId);

      if (profileError) throw profileError;

      // 2. Handle password reset (if applicable and implemented)
      if (resetPassword && newPassword) {
        console.warn("Password reset requested but not implemented in this MVP.");
        // In a real app, call the admin function here
        // const { error: authError } = await supabase.auth.admin.updateUserById(...)
        // if (authError) throw authError;
        // For now, maybe show a specific message or handle partial success
        setError("Datos guardados, pero el restablecimiento de contraseña no está implementado.");
        // Don't set full success or redirect immediately if password part failed conceptually
      } else {
        setSuccess(true);
        setTimeout(() => {
          router.push(`/panel-admin/alumnos`); // Redirect to list page after edit
        }, 2000);
      }

    } catch (error: any) {
      console.error("Error updating student:", error);
      setError(error.message || "Error al actualizar el alumno. Por favor, intenta nuevamente.");
    } finally {
      setSaving(false);
    }
  };

  // Handle loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <p className="text-gray-500">Cargando datos del alumno...</p>
      </div>
    );
  }

   // Handle case where studentId might be invalid after trying to resolve params (though less likely now)
   if (!studentId) {
     return (
       <div className="flex justify-center items-center h-64">
         <p className="text-red-500">Error: ID de alumno no válido.</p>
       </div>
     );
   }

  // Handle error state during fetch
  if (error && !success) { // Show fetch error only if not in success state
      return (
          <div className="flex justify-center items-center h-64">
              <p className="text-red-500">Error al cargar datos: {error}</p>
          </div>
      );
  }


  return (
    <div>
      <div className="md:grid md:grid-cols-3 md:gap-6">
        <div className="md:col-span-1">
          <div className="px-4 sm:px-0">
            <h3 className="text-lg font-medium leading-6 text-gray-900">
              {/* Display email once loaded, fallback to ID */}
              Editar Alumno {email ? `(${email} - ID: ${studentId})` : `(ID: ${studentId})`}
            </h3>
            <p className="mt-1 text-sm text-gray-600">
              Actualiza la información del alumno en el sistema.
            </p>
            <div className="mt-5">
              <p className="text-sm text-gray-500">
                La actualización de correo electrónico puede requerir verificación adicional.
              </p>
              <p className="mt-3 text-sm text-gray-500">
                Los campos marcados con * son obligatorios.
              </p>
            </div>
          </div>
        </div>
        <div className="mt-5 md:mt-0 md:col-span-2">
          {success ? (
            <div className="shadow sm:rounded-md sm:overflow-hidden">
              <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                <div className="bg-green-50 border-l-4 border-green-500 p-4 mb-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg
                        className="h-5 w-5 text-green-400"
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-green-800">
                        Alumno Actualizado Exitosamente
                      </h3>
                      <div className="mt-2 text-sm text-green-700">
                        <p>
                          La información del alumno ha sido actualizada correctamente.
                        </p>
                        <p className="mt-2">
                          Serás redirigido al listado de alumnos en unos segundos...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <div className="shadow sm:rounded-md sm:overflow-hidden">
                <div className="px-4 py-5 bg-white space-y-6 sm:p-6">
                  {error && ( // Display submit errors here
                    <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-4">
                      <p className="text-sm text-red-700">{error}</p>
                    </div>
                  )}

                  {/* First Name */}
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                      Nombres *
                    </label>
                    <input
                      type="text"
                      name="firstName"
                      id="firstName"
                      value={firstName}
                      onChange={(e) => setFirstName(e.target.value)}
                      className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md ${
                        formErrors.firstName ? "border-red-500" : ""
                      }`}
                      required
                    />
                     {formErrors.firstName && (
                      <p className="mt-1 text-sm text-red-600">
                        {formErrors.firstName}
                      </p>
                    )}
                  </div>

                  {/* Last Name */}
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                      Apellidos *
                    </label>
                    <input
                      type="text"
                      name="lastName"
                      id="lastName"
                      value={lastName}
                      onChange={(e) => setLastName(e.target.value)}
                      className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md ${
                        formErrors.lastName ? "border-red-500" : ""
                      }`}
                      required
                    />
                     {formErrors.lastName && (
                      <p className="mt-1 text-sm text-red-600">
                        {formErrors.lastName}
                      </p>
                    )}
                  </div>

                   {/* Full Name (Read Only - Calculated) */}
                   <div>
                    <label htmlFor="calculatedFullName" className="block text-sm font-medium text-gray-700">
                      Nombre Completo (Calculado)
                    </label>
                    <input
                      type="text"
                      name="calculatedFullName"
                      id="calculatedFullName"
                      value={`${firstName} ${lastName}`.trim()}
                      readOnly
                      className={`mt-1 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-gray-100`}
                    />
                  </div>


                  {/* Role */}
                  <div>
                    <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                      Rol *
                    </label>
                    {/* Consider using a select dropdown if roles are predefined */}
                    <input
                      type="text"
                      name="role"
                      id="role"
                      value={role}
                      onChange={(e) => setRole(e.target.value)}
                      className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md ${
                        formErrors.role ? "border-red-500" : ""
                      }`}
                      required
                    />
                     {formErrors.role && (
                      <p className="mt-1 text-sm text-red-600">
                        {formErrors.role}
                      </p>
                    )}
                  </div>

                  {/* Company ID */}
                  <div>
                    <label htmlFor="companyId" className="block text-sm font-medium text-gray-700">
                      Company ID (Opcional)
                    </label>
                    <input
                      type="text" // Should be text for UUID
                      name="companyId"
                      id="companyId"
                      value={companyId}
                      onChange={(e) => setCompanyId(e.target.value)}
                      className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md`}
                    />
                  </div>

                  {/* Is Active */}
                  <div className="relative flex items-start">
                     <div className="flex items-center h-5">
                        <input
                          id="isActive"
                          name="isActive"
                          type="checkbox"
                          checked={isActive}
                          onChange={(e) => setIsActive(e.target.checked)}
                          className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                        />
                      </div>
                      <div className="ml-3 text-sm">
                        <label htmlFor="isActive" className="font-medium text-gray-700">
                          Usuario Activo
                        </label>
                         <p className="text-gray-500">Indica si el usuario puede iniciar sesión.</p>
                      </div>
                  </div>


                  {/* Phone */}
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                      Teléfono (Opcional)
                    </label>
                    <input
                      type="text" // Use text for better handling of formats like +57...
                      name="phone"
                      id="phone"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md`}
                    />
                  </div>

                  {/* Documento Identidad */}
                  <div>
                    <label htmlFor="documentoIdentidad" className="block text-sm font-medium text-gray-700">
                      Documento Identidad (Opcional)
                    </label>
                    <input
                      type="text"
                      name="documentoIdentidad" // Corrected name
                      id="documentoIdentidad"   // Corrected id
                      value={documentoIdentidad} // Corrected state variable
                      onChange={(e) => setDocumentoIdentidad(e.target.value)} // Corrected setter
                      className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md`}
                    />
                     {formErrors.documentoIdentidad && ( // Add error display if needed
                      <p className="mt-1 text-sm text-red-600">
                        {formErrors.documentoIdentidad}
                      </p>
                    )}
                  </div>

                  {/* Email */}
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Correo Electrónico *
                    </label>
                    <input
                      type="email"
                      name="email"
                      id="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md ${
                        formErrors.email ? "border-red-500" : ""
                      }`}
                      required
                    />
                    {formErrors.email && (
                      <p className="mt-1 text-sm text-red-600">
                        {formErrors.email}
                      </p>
                    )}
                  </div>

                  {/* Reset Password Toggle */}
                  <div className="relative flex items-start">
                    <div className="flex items-center h-5">
                      <input
                        id="resetPassword"
                        name="resetPassword"
                        type="checkbox"
                        checked={resetPassword}
                        onChange={(e) => setResetPassword(e.target.checked)}
                        className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded"
                      />
                    </div>
                    <div className="ml-3 text-sm">
                      <label htmlFor="resetPassword" className="font-medium text-gray-700">
                        Restablecer contraseña
                      </label>
                      <p className="text-gray-500">
                        Marca esta casilla para establecer una nueva contraseña para el alumno.
                      </p>
                    </div>
                  </div>

                  {/* Password Fields (shown only if resetPassword is true) */}
                  {resetPassword && (
                    <>
                      {/* New Password */}
                      <div>
                        <label htmlFor="newPassword" className="block text-sm font-medium text-gray-700">
                          Nueva Contraseña *
                        </label>
                        <input
                          type="password"
                          name="newPassword"
                          id="newPassword"
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md ${
                            formErrors.newPassword ? "border-red-500" : ""
                          }`}
                          required={resetPassword}
                        />
                        {formErrors.newPassword ? (
                          <p className="mt-1 text-sm text-red-600">
                            {formErrors.newPassword}
                          </p>
                        ) : (
                          <p className="mt-1 text-xs text-gray-500">
                            La contraseña debe tener al menos 6 caracteres.
                          </p>
                        )}
                      </div>

                      {/* Confirm Password */}
                      <div>
                        <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700">
                          Confirmar Contraseña *
                        </label>
                        <input
                          type="password"
                          name="confirmPassword"
                          id="confirmPassword"
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className={`mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md ${
                            formErrors.confirmPassword ? "border-red-500" : ""
                          }`}
                          required={resetPassword}
                        />
                        {formErrors.confirmPassword && (
                          <p className="mt-1 text-sm text-red-600">
                            {formErrors.confirmPassword}
                          </p>
                        )}
                      </div>
                    </>
                  )}

                  {/* Password Reset Note */}
                  {resetPassword && (
                    <div className="rounded-md bg-yellow-50 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg
                            className="h-5 w-5 text-yellow-400"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                          >
                            <path
                              fillRule="evenodd"
                              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                              clipRule="evenodd"
                            />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-yellow-800">
                            Nota sobre restablecimiento de contraseña
                          </h3>
                          <div className="mt-2 text-sm text-yellow-700">
                            <p>
                              En este MVP, el restablecimiento de contraseña mediante administrador está simulado y no será efectivo.
                              Para una implementación completa, esta función requeriría privilegios de administrador de Supabase.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
                <div className="px-4 py-3 bg-gray-50 text-right sm:px-6">
                  <Link
                    href={`/panel-admin/alumnos`} // Go back to list page
                    className="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 mr-3 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Cancelar
                  </Link>
                  <button
                    type="submit"
                    disabled={saving}
                    className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                  >
                    {saving ? "Guardando..." : "Guardar Cambios"}
                  </button>
                </div>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
}
