import type { CookieOptions } from "@supabase/ssr";
import { createServerClient } from "@supabase/ssr";

import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

export async function middleware(request: NextRequest) {
  // Definir claramente las rutas
  const PUBLIC_ROUTES = [
    "/login",
    "/register",
    "/",
    "/auth/callback",
    "/verificar-certificado",
    "/certificado-preview",
  ];
  const ADMIN_ROUTES = ["/panel-admin"];
  const STUDENT_ROUTES = ["/panel-alumno"];
  const PROTECTED_ROUTES = [...ADMIN_ROUTES, ...STUDENT_ROUTES];

  const path = request.nextUrl.pathname;
  console.log(`[Middleware] Procesando ruta: ${path}`);

  // Crear respuesta para modificar después
  const response = NextResponse.next({
    request: { headers: request.headers },
  });

  try {
    // PASO 1: Para rutas públicas, no hacer verificación de autenticación
    // Esto es importante para evitar ciclos de redirección
    if (
      PUBLIC_ROUTES.some(
        (route) => path === route || path.startsWith(`${route}/`),
      )
    ) {
      console.log(
        `[Middleware] Ruta pública detectada: ${path}, acceso permitido sin verificación`,
      );
      return response;
    }

    // PASO 2: Para rutas protegidas, crear cliente Supabase y verificar sesión
    console.log(`[Middleware] Verificando acceso a ruta protegida: ${path}`);

    // Crear cliente Supabase para verificar la sesión
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL as string,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY as string,
      {
        cookies: {
          get(name: string) {
            const cookie = request.cookies.get(name)?.value;
            console.log(
              `[Middleware] Leyendo cookie ${name}: ${cookie ? "presente" : "ausente"}`,
            );
            return cookie;
          },
          set(name: string, value: string, options: CookieOptions) {
            console.log(`[Middleware] Estableciendo cookie ${name}`);
            response.cookies.set({
              name,
              value,
              ...options,
            });
          },
          remove(name: string, options: CookieOptions) {
            console.log(`[Middleware] Eliminando cookie ${name}`);
            response.cookies.delete({
              name,
              ...options,
            });
          },
        },
      },
    );

    // Get user information
    const {
      data: { user },
      error,
    } = await supabase.auth.getUser();

    // Si es una ruta protegida y no hay sesión, redirigir a login
    if (!user) {
      console.log(`[Middleware] Usuario no autenticado, redirigiendo a login`);
      const callbackUrl = encodeURIComponent(path);
      return NextResponse.redirect(
        new URL(`/login?redirect=${callbackUrl}`, request.url),
      );
    }

    console.log(`[Middleware] Sesión detectada: ${user.id}`);

    // PASO 3: Para rutas de admin, verificar rol
    if (
      ADMIN_ROUTES.some(
        (route) => path === route || path.startsWith(`${route}/`),
      )
    ) {
      console.log(
        `[Middleware] Verificando permiso de administrador para: ${path}`,
      );
      try {
        const { data: profile, error } = await supabase
          .from("users")
          .select("role")
          .eq("id", user.id)
          .single();

        if (error) {
          console.error(
            `[Middleware] Error al verificar rol admin: ${error.message}`,
          );
          return NextResponse.redirect(new URL("/panel-alumno", request.url));
        }

        console.log(`[Middleware] Rol del usuario: ${profile?.role}`);
        if (!profile || profile.role.toLowerCase() !== "admin") {
          console.log(
            "[Middleware] Usuario no es admin, redirigiendo a panel-alumno",
          );
          return NextResponse.redirect(new URL("/panel-alumno", request.url));
        }

        console.log(
          `[Middleware] Acceso de administrador concedido para: ${path}`,
        );
      } catch (error) {
        console.error(
          "[Middleware] Error en middleware (verificación admin):",
          error,
        );
        return NextResponse.redirect(new URL("/panel-alumno", request.url));
      }
    }

    // Si pasa todas las verificaciones, permitir el acceso
    console.log(`[Middleware] Permitiendo acceso a ruta protegida: ${path}`);
    return response;
  } catch (error) {
    console.error("[Middleware] Error general en middleware:", error);

    // Si hay un error para una ruta protegida, redirigir a login por seguridad
    if (
      PROTECTED_ROUTES.some(
        (route) => path === route || path.startsWith(`${route}/`),
      )
    ) {
      return NextResponse.redirect(new URL("/login", request.url));
    }

    return response;
  }
}

// Especificar las rutas donde debe ejecutarse el middleware
export const config = {
  matcher: [
    "/panel-admin/:path*",
    "/panel-alumno/:path*",
    "/login",
    "/register",
    "/auth/callback",
    "/verificar-certificado",
    "/certificado-preview/:path*",
    "/",
  ],
};
