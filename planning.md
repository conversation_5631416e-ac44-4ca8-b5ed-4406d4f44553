# Planificación del Proyecto y Flujos de Trabajo

Este documento centraliza toda la planificación, reglas, flujos de trabajo y guías de despliegue del proyecto DOMUS OTEC.

## 1. Planificación e Historias de Usuario

### 1.1. Landing Page (/)

#### Historias de Usuario

- Como visitante, quiero ver una página principal atractiva que explique claramente los servicios de DOMUS OTEC
- Como visitante, quiero poder acceder fácilmente a las opciones de login/registro
- <PERSON> visitante, quiero poder navegar directamente a la verificación de certificados

#### Requisitos Técnicos

- Diseño moderno y responsive usando Tailwind CSS
- Secciones claramente definidas con información relevante
- CTAs prominentes para registro/login y verificación de certificados
- Optimización para SEO con metadatos apropiados

### 1.2. Autenticación y Roles

#### Historias de Usuario

- Como usuario nuevo, quiero poder registrarme con email y contraseña
- Como usuario registrado, quiero poder iniciar sesión de forma segura
- Como administrador, quiero acceder a funcionalidades específicas de gestión
- Como alumno, quiero acceder a mi panel personal con mis datos

#### Requisitos Técnicos

- Implementación de Supabase Auth para autenticación
- Middleware para protección de rutas según roles
- Tabla profiles vinculada a auth.users con roles definidos
- Manejo de sesiones y estados de autenticación

### 1.3. Gestión de Certificados

#### Historias de Usuario - Administrador

- Como admin, quiero crear nuevos certificados para alumnos
- Como admin, quiero generar códigos QR únicos para cada certificado
- Como admin, quiero poder editar y actualizar certificados existentes
- Como admin, quiero poder eliminar certificados si es necesario

#### Historias de Usuario - Alumno

- Como alumno, quiero ver mis certificados emitidos
- Como alumno, quiero descargar mis certificados en formato digital
- Como alumno, quiero compartir el enlace de verificación de mis certificados

#### Requisitos Técnicos

- CRUD completo para gestión de certificados
- Generación automática de códigos QR con URLs únicas
- Almacenamiento seguro de certificados en Supabase Storage
- RLS para garantizar acceso apropiado a certificados

### 1.4. Gestión de Alumnos

#### Historias de Usuario - Administrador

- Como admin, quiero registrar nuevos alumnos en el sistema
- Como admin, quiero ver un listado completo de alumnos
- Como admin, quiero buscar y filtrar alumnos por diferentes criterios
- Como admin, quiero editar información de alumnos
- Como admin, quiero dar de baja alumnos cuando sea necesario

#### Requisitos Técnicos

- CRUD completo para gestión de alumnos
- Sistema de búsqueda y filtrado eficiente
- Validación de datos en frontend y backend
- RLS para proteger datos sensibles de alumnos

### 1.5. Gestión de Notas y Asistencia

#### Historias de Usuario - Administrador

- Como admin, quiero registrar notas para cada alumno
- Como admin, quiero registrar asistencia por clase/sesión
- Como admin, quiero ver estadísticas de rendimiento
- Como admin, quiero generar reportes de notas y asistencia

#### Historias de Usuario - Alumno

- Como alumno, quiero ver mis calificaciones
- Como alumno, quiero ver mi registro de asistencia
- Como alumno, quiero ver estadísticas de mi rendimiento

#### Requisitos Técnicos

- Sistema de registro de notas con validación
- Sistema de registro de asistencia con estados múltiples
- Cálculo automático de promedios y estadísticas
- RLS para proteger registros académicos

### 1.6. Evaluaciones

#### Historias de Usuario - Administrador

- Como admin, quiero crear evaluaciones para los cursos
- Como admin, quiero asignar evaluaciones a alumnos
- Como admin, quiero registrar resultados de evaluaciones
- Como admin, quiero ver estadísticas de evaluaciones

#### Historias de Usuario - Alumno

- Como alumno, quiero ver mis evaluaciones pendientes
- Como alumno, quiero ver los resultados de mis evaluaciones
- Como alumno, quiero ver mi progreso en el curso

#### Requisitos Técnicos

- Sistema de gestión de evaluaciones
- Registro y seguimiento de resultados
- Estadísticas y análisis de rendimiento
- RLS para proteger datos de evaluaciones

### 1.7. Verificación QR Pública

#### Historias de Usuario - Público General

- Como verificador, quiero escanear un código QR y validar un certificado
- Como verificador, quiero ver los detalles del certificado verificado
- Como verificador, quiero confirmar la autenticidad del certificado
- Como estudiante, quiero buscar mis certificados usando mi documento de identidad
- Como estudiante, quiero ver todos mis certificados, incluso los desactualizados

#### Requisitos Técnicos

- Página pública para verificación de certificados
- Sistema de escaneo de códigos QR
- Validación en tiempo real de certificados
- Interfaz clara para mostrar resultados de verificación
- Búsqueda de certificados por documento de identidad
- Listado de todos los certificados de un usuario

### 1.8. UI/UX General

#### Requisitos Transversales

- Diseño consistente usando Shadcn/UI y Tailwind
- Validación en tiempo real de formularios
- Feedback visual para todas las acciones
- Diseño responsive para todos los dispositivos
- Sistema de notificaciones para acciones importantes
- Manejo apropiado de estados de carga y errores
- Navegación intuitiva y coherente
- Accesibilidad básica implementada

## 2. Flujo de Trabajo con Git (Git Flow)

Este documento describe el flujo de trabajo Git Flow adoptado para el desarrollo y despliegue del proyecto DOMUS OTEC.

### 2.1. Estructura de Ramas

- **main**: Rama principal que contiene el código en producción.
- **develop**: Rama de desarrollo donde se integran las nuevas características.
- **feature/***: Ramas para desarrollar nuevas características.
- **release/***: Ramas para preparar nuevas versiones para producción.
- **hotfix/***: Ramas para corregir errores críticos en producción.

### 2.2. Flujo de Trabajo

#### Desarrollo de Nuevas Características

1. Crear una rama `feature` desde `develop`:

   ```bash
   git checkout develop
   git pull
   git checkout -b feature/nombre-caracteristica
   ```

2. Desarrollar la característica en la rama `feature`.

3. Una vez completada, crear un Pull Request a `develop`.

4. Después de la revisión, fusionar la rama `feature` en `develop`.

#### Preparación de Versiones

1. Crear una rama `release` desde `develop`:

   ```bash
   git checkout develop
   git pull
   git checkout -b release/v1.0.0
   ```

2. Realizar ajustes finales, correcciones y actualizar versión.

3. Fusionar la rama `release` en `main` y `develop`:

   ```bash
   git checkout main
   git pull
   git merge release/v1.0.0
   git push

   git checkout develop
   git pull
   git merge release/v1.0.0
   git push
   ```

4. Etiquetar la versión en `main`:

   ```bash
   git checkout main
   git tag -a v1.0.0 -m "Versión 1.0.0"
   git push --tags
   ```

#### Corrección de Errores en Producción

1. Crear una rama `hotfix` desde `main`:

   ```bash
   git checkout main
   git pull
   git checkout -b hotfix/error-critico
   ```

2. Corregir el error.

3. Fusionar la rama `hotfix` en `main` y `develop`:

   ```bash
   git checkout main
   git pull
   git merge hotfix/error-critico
   git push

   git checkout develop
   git pull
   git merge hotfix/error-critico
   git push
   ```

4. Actualizar la etiqueta de versión si es necesario.

### 2.3. Integración con CI/CD

- Las ramas `main` y `develop` tienen CI/CD configurado para despliegue automático.
- Las ramas `feature/*` no se despliegan automáticamente.
- Las ramas `release/*` y `hotfix/*` tienen despliegues de previsualización.

### 2.4. Convenciones de Commit

Utilizamos el formato de Conventional Commits:

- `feat`: Nueva característica
- `fix`: Corrección de error
- `docs`: Cambios en documentación
- `style`: Cambios de formato (espacios, indentación, etc.)
- `refactor`: Refactorización de código
- `test`: Adición o corrección de pruebas
- `chore`: Cambios en el proceso de construcción o herramientas auxiliares

Ejemplo:

```
feat(certificados): añadir validación de QR
```

### 2.5. Despliegue en Vercel

El despliegue en Vercel está automatizado:

- Rama `main`: Despliegue a producción
- Rama `develop`: Despliegue a entorno de desarrollo
- Ramas `release/*` y `hotfix/*`: Despliegues de previsualización

Para más información sobre la configuración de Vercel, consulta el archivo `vercel.json` en la raíz del proyecto.

## 3. Flujo de Trabajo Manual (Alternativa a Git Flow Tool)

Como alternativa a la herramienta Git Flow, puedes seguir estos pasos para implementar manualmente el flujo de trabajo en el proyecto.

### 3.1. Configuración Inicial

1. **Asegúrate de tener la rama `main` como rama principal**:

   ```bash
   git checkout master
   git branch -m master main  # Si es necesario renombrar master a main
   ```

2. **Crea la rama `develop` desde `main`**:

   ```bash
   git checkout main
   git checkout -b develop
   git push -u origin develop
   ```

### 3.2. Desarrollo de Nuevas Características

1. **Crea una rama `feature` desde `develop`**:

   ```bash
   git checkout develop
   git pull
   git checkout -b feature/nombre-caracteristica
   ```

2. **Desarrolla la característica** en la rama `feature`.

3. **Fusiona la rama `feature` en `develop`**:

   ```bash
   git checkout develop
   git pull
   git merge --no-ff feature/nombre-caracteristica
   git push
   ```

4. **Elimina la rama `feature`** (opcional):

   ```bash
   git branch -d feature/nombre-caracteristica
   git push origin --delete feature/nombre-caracteristica  # Para eliminar la rama remota
   ```

### 3.3. Preparación de Versiones

1. **Crea una rama `release` desde `develop`**:

   ```bash
   git checkout develop
   git pull
   git checkout -b release/v1.0.0
   ```

2. **Realiza ajustes finales**, correcciones y actualiza la versión.

3. **Fusiona la rama `release` en `main`**:

   ```bash
   git checkout main
   git pull
   git merge --no-ff release/v1.0.0
   git push
   ```

4. **Fusiona la rama `release` en `develop`**:

   ```bash
   git checkout develop
   git pull
   git merge --no-ff release/v1.0.0
   git push
   ```

5. **Etiqueta la versión en `main`**:

   ```bash
   git checkout main
   git tag -a v1.0.0 -m "Versión 1.0.0"
   git push --tags
   ```

6. **Elimina la rama `release`** (opcional):

   ```bash
   git branch -d release/v1.0.0
   git push origin --delete release/v1.0.0  # Para eliminar la rama remota
   ```

### 3.4. Corrección de Errores en Producción

1. **Crea una rama `hotfix` desde `main`**:

   ```bash
   git checkout main
   git pull
   git checkout -b hotfix/error-critico
   ```

2. **Corrige el error**.

3. **Fusiona la rama `hotfix` en `main`**:

   ```bash
   git checkout main
   git pull
   git merge --no-ff hotfix/error-critico
   git push
   ```

4. **Fusiona la rama `hotfix` en `develop`**:

   ```bash
   git checkout develop
   git pull
   git merge --no-ff hotfix/error-critico
   git push
   ```

5. **Actualiza la etiqueta de versión** si es necesario:

   ```bash
   git checkout main
   git tag -a v1.0.1 -m "Versión 1.0.1"
   git push --tags
   ```

6. **Elimina la rama `hotfix`** (opcional):

   ```bash
   git branch -d hotfix/error-critico
   git push origin --delete hotfix/error-critico  # Para eliminar la rama remota
   ```

### 3.5. Integración con CI/CD

La configuración de CI/CD en los archivos `.github/workflows` y `vercel.json` ya está preparada para trabajar con este flujo de trabajo manual de Git Flow.

## 4. Reglas y Contexto del Proyecto

Este apartado define el contexto esencial y las reglas fundamentales que deben seguirse.

### 4.1. Resumen del Proyecto

- **Objetivo**: Crear un NVP funcional **rápidamente** para una plataforma web de gestión educativa/certificados.
- **Backend**: **Supabase** (Auth, DB PostgreSQL, Storage, Edge Functions).
- **Frontend**: **Next.js** (App Router, TypeScript).
- **Foco**: Funcionalidad esencial para pruebas y validación, velocidad de desarrollo, diseño limpio/moderno.
- **Público**: Administradores, Alumnos/Usuarios, Público General (verificación QR).

### 4.2. Stack Tecnológico Principal

- **Framework**: Next.js (App Router, TypeScript)
- **Backend/DB**: Supabase Platform
- **Interacción DB/Backend**: **@supabase/supabase-js** (Client Library) - **Priorizar sobre Prisma para NVP.**
- **UI/Estilos**: Tailwind CSS + **Shadcn/UI** (Usar componentes pre-construidos).
- **Generación QR**: `qrcode` (Node.js - usar en API Routes / Edge Functions).
- **Despliegue**: Vercel (Frontend), Supabase (Backend).

### 4.3. Instrucción Crítica: Base de Datos Supabase Existente

- **¡PRIORIDAD MÁXIMA ANTES DE CODIFICAR MODELOS/APIs!**
- **Revisar**: Inspeccionar **detalladamente** la estructura de la base de datos PostgreSQL **existente**.
- **Analizar**: Identificar tablas/columnas/relaciones relevantes (`users`, `profiles`, `students`, `certificates`, `courses`, etc.).
- **Adaptar o Modificar**:
  - Si el esquema existente **sirve**: Adaptar el código para **usarlo**.
  - Si el esquema existente **NO sirve**: Documentar y **realizar las modificaciones necesarias** (SQL en editor Supabase o Migraciones Supabase).
- **Implementar**: Basar **todo** el código de backend/DB en el esquema **revisado y finalizado**.
- **RLS**: Configurar **Row Level Security** en Supabase como parte fundamental de la seguridad de datos.

### 4.4. Módulos Principales del NVP (Scope)

1. Landing Page Estática (`/`)
2. Autenticación y Roles (Supabase Auth, tabla `profiles` con roles, Middleware)
3. Gestión de Certificados (Admin: CRUD simplificado, Generación/Almacenamiento QR; Alumno: Ver/Descargar)
4. Gestión de Alumnos (Admin: CRUD completo).
5. Notas/Asistencia (Admin: Registro simple; Alumno: Ver).
6. Evaluaciones (Admin: Registro simple; Alumno: Ver).
7. Verificación QR Pública (`/verificar-certificado`).

### 4.5. Flujo de Trabajo General (Hitos)

1. **Setup & Auth**: Next.js init, Conexión Supabase, Auth UI/Lógica, Layouts, Middleware.
2. **DB & API**: **Revisión/Adaptación DB (CRÍTICO)**, API Routes (Next.js) / Edge Functions (Supabase), RLS.
3. **UI & Funcionalidades**: Shadcn UI, Conexión UI-Backend (`supabase-js`), Validaciones.
4. **Refinamiento MVP**: Testing básico, Optimización, Preparación Deploy.

### 4.6. Principios de Codificación

- **Lenguaje**: TypeScript claro y bien tipado.
- **Estilo**: Seguir convenciones de Next.js, React, Tailwind. Comentar lógica compleja.
- **Errores**: Manejo básico de errores (frontend/backend).
- **Seguridad**: **Priorizar RLS en Supabase.** Validar entradas en backend. No exponer secretos.
- **MVP Estricto**: Enfocarse **solo** en las funcionalidades definidas. Evitar complejidad o features adicionales. **La velocidad es clave.**

## 5. Guía de Despliegue en Vercel

Este documento describe el proceso de despliegue del proyecto DOMUS OTEC en Vercel utilizando CI/CD.

### 5.1. Configuración Inicial

#### 5.1.1. Configuración de Vercel

1. Crea una cuenta en [Vercel](https://vercel.com) si aún no tienes una.
2. Conecta tu repositorio de GitHub a Vercel.
3. Configura un nuevo proyecto en Vercel:
   - Selecciona el repositorio `domus-otec`.
   - Configura el framework como Next.js.
   - Define las variables de entorno necesarias.

#### 5.1.2. Configuración de Variables de Entorno

En Vercel, configura las siguientes variables de entorno como secretos:

- `NEXT_PUBLIC_SUPABASE_URL`: URL de tu proyecto Supabase.
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Clave anónima de tu proyecto Supabase.

#### 5.1.3. Configuración de GitHub Actions

1. En GitHub, ve a `Settings > Secrets and variables > Actions`.
2. Añade los siguientes secretos:
   - `VERCEL_TOKEN`: Token de API de Vercel.
   - `NEXT_PUBLIC_SUPABASE_URL`: URL de tu proyecto Supabase.
   - `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Clave anónima de tu proyecto Supabase.

### 5.2. Flujo de Despliegue

#### 5.2.1. Despliegue Automático

El proyecto está configurado para desplegar automáticamente:

- **Producción**: Cuando se hace push a la rama `main`.
- **Desarrollo**: Cuando se hace push a la rama `develop`.
- **Previsualización**: Para ramas `release/*` y `hotfix/*`.

#### 5.2.2. Despliegue Manual

Si necesitas realizar un despliegue manual:

1. Instala la CLI de Vercel:

   ```bash
   npm install -g vercel
   ```

2. Inicia sesión en Vercel:

   ```bash
   vercel login
   ```

3. Despliega el proyecto:

   ```bash
   # Para entorno de desarrollo
   vercel

   # Para producción
   vercel --prod
   ```

### 5.3. Verificación y Solución de Problemas

#### 5.3.1. Verificación de Despliegue

- **Producción**: `https://domus-otec.vercel.app`
- **Previsualización**: URLs generadas automáticamente por Vercel.

#### 5.3.2. Solución de Errores Comunes

- **Error de construcción**: Revisa los logs en Vercel y asegúrate de que el proyecto construye localmente.
- **Variables de entorno faltantes**: Verifica que todas las variables estén configuradas en Vercel.
- **Problemas de conexión con Supabase**: Confirma que las credenciales y las políticas RLS son correctas.

#### 5.3.3. Rollback

1. En el dashboard de Vercel, ve a la sección "Deployments".
2. Encuentra el despliegue al que quieres revertir.
3. Haz clic en los tres puntos y selecciona "Promote to Production".

### 5.4. Monitoreo y Recursos

- **Monitoreo**: Utiliza las herramientas de Vercel (Analytics, Logs, Alertas).
- **Recursos Adicionales**:
  - [Documentación de Vercel](https://vercel.com/docs)
  - [Documentación de Next.js](https://nextjs.org/docs)
  - [Documentación de Supabase](https://supabase.io/docs)
