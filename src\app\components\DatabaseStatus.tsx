"use client";

import { useState, useEffect } from 'react';
import { supabase, tableExists } from '@/lib/supabase'; // Import tableExists instead

// Tipo para los resultados de diagnóstico
type DiagnosticResult = {
  name: string;
  status: 'success' | 'error' | 'warning' | 'pending';
  message: string;
};

export default function DatabaseStatus() {
  const [diagnostics, setDiagnostics] = useState<DiagnosticResult[]>([
    { name: 'Conexión', status: 'pending', message: 'Verificando conexión...' },
    { name: 'Autenticación', status: 'pending', message: 'Verificando sesión...' },
    { name: 'Tabla<PERSON>', status: 'pending', message: 'Verificando tablas...' },
    { name: 'Permisos', status: 'pending', message: 'Verificando permisos...' },
  ]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const runDiagnostics = async () => {
    setIsLoading(true);

    // Reset diagnostics
    setDiagnostics(prev =>
      prev.map(d => ({ ...d, status: 'pending', message: `Verificando ${d.name.toLowerCase()}...` }))
    );

    // 1. Verificar conexión básica
    // 1. Verificar conexión básica
    let connectionError: { message: string; code?: string } | null = null;
    // let connectionData: any = null; // Removed unused variable
    const start = Date.now();
    try {
        // Execute the query and handle potential errors, only destructure error
        const { error: queryError } = await supabase
            .from('_dummy_query_for_connection_test_') // Use a non-existent table
            .select('*', { head: true }) // Use head: true for efficiency
            .limit(0); // Use limit(0) for efficiency

        // connectionData = _data; // Removed assignment to unused variable
        connectionError = queryError;

    } catch (err) {
        // Catch network or other unexpected errors during fetch
        console.error("Connection test - fetch error:", err);
        connectionError = { message: err instanceof Error ? err.message : 'Error de red o inesperado' };
    }
    const elapsed = Date.now() - start;

    // Now process the result (connectionError)
    setDiagnostics(prev => {
      const updated = [...prev];
      // Check if there was an error, but ignore expected "relation does not exist" errors
      if (connectionError && connectionError.code !== '42P01' && !connectionError.message.includes('does not exist')) {
          updated[0] = {
            name: 'Conexión',
            status: 'error',
            message: `Error de conexión: ${connectionError.message || connectionError.code || 'Desconocido'}`
          };
      } else {
          // If no significant error, connection is considered successful
          updated[0] = {
            name: 'Conexión',
            status: 'success',
            message: `Conexión establecida (${elapsed}ms)`
          };
      }
      return updated;
    });
    // --- The original catch block below is no longer needed for this specific step ---
    /*
    } catch (err) {
      setDiagnostics(prev => {
        const updated = [...prev];
        updated[0] = {
          name: 'Conexión',
          status: 'error',
          message: `Error inesperado: ${err instanceof Error ? err.message : String(err)}`
        };
        return updated;
      });
    }
    */

      // --- This logic is now integrated into the try...catch block above ---
    // --- Removed original catch block as error handling is now within the try block ---

    // 2. Verificar autenticación
    try {
      const { data, error } = await supabase.auth.getSession();

      setDiagnostics(prev => {
        const updated = [...prev];
        if (error) {
          updated[1] = {
            name: 'Autenticación',
            status: 'error',
            message: `Error de autenticación: ${error.message}`
          };
        } else if (!data.session) {
          updated[1] = {
            name: 'Autenticación',
            status: 'warning',
            message: 'No hay sesión activa'
          };
        } else {
          const expiresAt = data.session.expires_at || 0;
          const now = Math.floor(Date.now() / 1000);
          const timeLeft = expiresAt - now;

          if (timeLeft < 300) { // menos de 5 minutos
            updated[1] = {
              name: 'Autenticación',
              status: 'warning',
              message: `Sesión expira pronto (${Math.floor(timeLeft / 60)} min)`
            };
          } else {
            updated[1] = {
              name: 'Autenticación',
              status: 'success',
              message: `Sesión válida (expira en ${Math.floor(timeLeft / 60)} min)`
            };
          }
        }
        return updated;
      });
    } catch (err) {
      setDiagnostics(prev => {
        const updated = [...prev];
        updated[1] = {
          name: 'Autenticación',
          status: 'error',
          message: `Error inesperado: ${err instanceof Error ? err.message : String(err)}`
        };
        return updated;
      });
    }

    // 3. Verificar tablas clave
    try {
      const requiredTables = ['users', 'certificates', 'courses'];
      const results = await Promise.all(
        requiredTables.map(async (tableName) => ({
          name: tableName,
          exists: await tableExists(tableName),
        }))
      );

      const missingTables = results.filter(r => !r.exists).map(r => r.name);

      setDiagnostics(prev => {
        const updated = [...prev];
        if (missingTables.length > 0) {
          updated[2] = {
            name: 'Tablas',
            status: 'error',
            message: `Faltan tablas clave: ${missingTables.join(', ')}`
          };
        } else {
           updated[2] = {
            name: 'Tablas',
            status: 'success',
            message: `Tablas clave (${requiredTables.join(', ')}) verificadas.`
          };
        }
        return updated;
      });
    } catch (err) {
      setDiagnostics(prev => {
        const updated = [...prev];
        updated[2] = {
          name: 'Tablas',
          status: 'error',
          message: `Error consultando tablas: ${err instanceof Error ? err.message : String(err)}`
        };
        return updated;
      });
    }

    // 4. Verificar permisos
    try {
      // Intentamos hacer algunas operaciones básicas para verificar permisos
      const permissionResults = [];

      // Intentar leer (necesita permisos SELECT)
      try {
        const { error: readError } = await supabase
          .from('users')
          .select('id')
          .limit(1)
          .maybeSingle();

        permissionResults.push({
          operation: 'SELECT',
          result: !readError || readError.code === 'PGRST116'
        });
      } catch { // Remove unused catch parameter entirely
        permissionResults.push({
          operation: 'SELECT',
          result: false
        });
      }

      // Intentar contar (otro tipo de SELECT)
      try {
        const { error: countError } = await supabase
          .from('users')
          .select('id', { count: 'exact', head: true });

        permissionResults.push({
          operation: 'COUNT',
          result: !countError || countError.code === 'PGRST116'
        });
      } catch { // Remove unused catch parameter entirely
        permissionResults.push({
          operation: 'COUNT',
          result: false
        });
      }

      const successCount = permissionResults.filter(p => p.result).length;
      const totalChecks = permissionResults.length;

      setDiagnostics(prev => {
        const updated = [...prev];
        if (successCount === 0) {
          updated[3] = {
            name: 'Permisos',
            status: 'error',
            message: 'No se tienen permisos básicos de lectura'
          };
        } else if (successCount < totalChecks) {
          updated[3] = {
            name: 'Permisos',
            status: 'warning',
            message: `Permisos parciales (${successCount}/${totalChecks} verificaciones exitosas)`
          };
        } else {
          updated[3] = {
            name: 'Permisos',
            status: 'success',
            message: 'Permisos básicos verificados correctamente'
          };
        }
        return updated;
      });
    } catch (err) {
      setDiagnostics(prev => {
        const updated = [...prev];
        updated[3] = {
          name: 'Permisos',
          status: 'error',
          message: `Error verificando permisos: ${err instanceof Error ? err.message : String(err)}`
        };
        return updated;
      });
    }

    setIsLoading(false);
  };

  // Obtener el color según el estado
  const getStatusColor = (status: 'success' | 'error' | 'warning' | 'pending') => {
    switch (status) {
      case 'success': return 'text-green-500';
      case 'error': return 'text-red-500';
      case 'warning': return 'text-yellow-500';
      default: return 'text-gray-400';
    }
  };

  // Ejecutar diagnóstico al abrir
  useEffect(() => {
    if (isOpen && !isLoading) {
      runDiagnostics();
    }
  }, [isOpen]);

  return (
    <div className="mt-2">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="text-sm text-gray-500 hover:text-indigo-600 flex items-center"
      >
        <span className={`w-2 h-2 rounded-full mr-1 ${diagnostics.some(d => d.status === 'error') ? 'bg-red-500' : 'bg-green-500'}`}></span>
        {isOpen ? 'Ocultar diagnóstico de base de datos' : 'Mostrar diagnóstico de base de datos'}
      </button>

      {isOpen && (
        <div className="mt-2 p-4 bg-gray-50 rounded-md border border-gray-200">
          <div className="flex justify-between mb-4">
            <h3 className="text-sm font-medium">Diagnóstico de conexión a Supabase</h3>
            <button
              onClick={runDiagnostics}
              disabled={isLoading}
              className="text-xs text-indigo-600 hover:text-indigo-800"
            >
              {isLoading ? 'Ejecutando...' : 'Ejecutar de nuevo'}
            </button>
          </div>

          <ul className="space-y-2">
            {diagnostics.map((item, index) => (
              <li key={index} className="flex items-start">
                <span className={`mt-0.5 mr-2 ${getStatusColor(item.status)}`}>
                  {item.status === 'success' && (
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  )}
                  {item.status === 'error' && (
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  )}
                  {item.status === 'warning' && (
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  )}
                  {item.status === 'pending' && (
                    <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  )}
                </span>
                <div>
                  <p className="text-sm font-medium">{item.name}</p>
                  <p className="text-xs text-gray-500">{item.message}</p>
                </div>
              </li>
            ))}
          </ul>

          <div className="mt-4 text-xs text-gray-500">
            <p>
              URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ?
                `${process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 25)}...` :
                'No configurada'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}