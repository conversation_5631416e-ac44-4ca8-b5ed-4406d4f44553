/**
 * @fileoverview Dashboard Sidebar Component
 *
 * Componente de navegación lateral responsivo para dashboards de QR CURSE.
 * Incluye navegación principal, navegación secundaria y controles de configuración.
 */

"use client";

import {
  Award,
  BarChart3,
  BookOpen,
  Building,
  FileText,
  HelpCircle,
  Home,
  Settings,
  Users,
  X,
} from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface DashboardSidebarProps {
  isOpen?: boolean;
  onClose?: () => void;
  userRole?: "admin" | "instructor" | "student";
}

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  roles?: ("admin" | "instructor" | "student")[];
  badge?: string;
}

/**
 * Elementos de navegación principal organizados por rol
 */
const navigationItems: NavigationItem[] = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: Home,
    roles: ["admin", "instructor", "student"],
  },
  {
    href: "/certificates",
    label: "Certificados",
    icon: Award,
    roles: ["admin", "instructor", "student"],
  },
  {
    href: "/users",
    label: "Usuarios",
    icon: Users,
    roles: ["admin"],
  },
  {
    href: "/courses",
    label: "Cursos",
    icon: BookOpen,
    roles: ["admin", "instructor"],
  },
  {
    href: "/companies",
    label: "Empresas",
    icon: Building,
    roles: ["admin"],
  },
  {
    href: "/reports",
    label: "Reportes",
    icon: BarChart3,
    roles: ["admin", "instructor"],
  },
  {
    href: "/templates",
    label: "Plantillas",
    icon: FileText,
    roles: ["admin"],
  },
];

/**
 * Elementos de navegación secundaria (configuración y ayuda)
 */
const secondaryItems: NavigationItem[] = [
  {
    href: "/settings",
    label: "Configuración",
    icon: Settings,
    roles: ["admin", "instructor", "student"],
  },
  {
    href: "/help",
    label: "Ayuda",
    icon: HelpCircle,
    roles: ["admin", "instructor", "student"],
  },
];

/**
 * Componente de sidebar responsivo para dashboard
 */
export function DashboardSidebar({
  isOpen = false,
  onClose,
  userRole = "student",
}: DashboardSidebarProps) {
  const pathname = usePathname();

  // Filtrar elementos de navegación según el rol del usuario
  const filteredNavItems = navigationItems.filter(
    (item) => !item.roles || item.roles.includes(userRole),
  );

  const filteredSecondaryItems = secondaryItems.filter(
    (item) => !item.roles || item.roles.includes(userRole),
  );

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={onClose}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-14 z-50 h-[calc(100vh-3.5rem)] w-64 transform border-r bg-background transition-transform duration-200 ease-in-out lg:translate-x-0",
          isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
        )}
        aria-label="Navegación principal"
      >
        <div className="flex h-full flex-col">
          {/* Close button for mobile */}
          <div className="flex items-center justify-between p-4 lg:hidden">
            <span className="font-semibold">Navegación</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              aria-label="Cerrar menú de navegación"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <ScrollArea className="flex-1 px-3">
            {/* Navegación principal */}
            <div className="space-y-2 py-4">
              <div className="px-3 py-2">
                <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
                  Principal
                </h2>
                <div className="space-y-1">
                  {filteredNavItems.map((item) => (
                    <SidebarItem
                      key={item.href}
                      href={item.href}
                      icon={item.icon}
                      isActive={pathname === item.href}
                      badge={item.badge}
                      onClick={onClose}
                    >
                      {item.label}
                    </SidebarItem>
                  ))}
                </div>
              </div>

              <Separator />

              {/* Navegación secundaria */}
              <div className="px-3 py-2">
                <h2 className="mb-2 px-4 text-lg font-semibold tracking-tight">
                  Configuración
                </h2>
                <div className="space-y-1">
                  {filteredSecondaryItems.map((item) => (
                    <SidebarItem
                      key={item.href}
                      href={item.href}
                      icon={item.icon}
                      isActive={pathname === item.href}
                      onClick={onClose}
                    >
                      {item.label}
                    </SidebarItem>
                  ))}
                </div>
              </div>
            </div>
          </ScrollArea>

          {/* Footer del sidebar */}
          <div className="border-t p-4">
            <div className="text-xs text-muted-foreground">
              <p>QR CURSE v1.0</p>
              <p>© 2024 Todos los derechos reservados</p>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}

/**
 * Componente individual de elemento de navegación
 */
interface SidebarItemProps {
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  children: React.ReactNode;
  isActive?: boolean;
  badge?: string;
  onClick?: () => void;
}

function SidebarItem({
  href,
  icon: Icon,
  children,
  isActive = false,
  badge,
  onClick,
}: SidebarItemProps) {
  return (
    <Link
      href={href}
      onClick={onClick}
      className={cn(
        "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:text-accent-foreground",
        isActive
          ? "bg-accent text-accent-foreground"
          : "text-muted-foreground hover:bg-accent",
      )}
    >
      <Icon className="h-4 w-4" />
      <span className="flex-1">{children}</span>
      {badge && (
        <span className="ml-auto flex h-6 w-6 shrink-0 items-center justify-center rounded-full bg-primary text-xs text-primary-foreground">
          {badge}
        </span>
      )}
    </Link>
  );
}

/**
 * Hook para determinar si el sidebar debe estar abierto por defecto
 */
export function useSidebarState() {
  const [isOpen, setIsOpen] = React.useState(false);

  // Cerrar sidebar en móvil cuando cambie la ruta
  const _pathname = usePathname();
  React.useEffect(() => {
    setIsOpen(false);
  }, []);

  return {
    isOpen,
    setIsOpen,
    toggle: () => setIsOpen(!isOpen),
    close: () => setIsOpen(false),
    open: () => setIsOpen(true),
  };
}

/**
 * Componente de sidebar compacto para espacios reducidos
 */
export function CompactSidebar({
  userRole = "student",
}: {
  userRole?: "admin" | "instructor" | "student";
}) {
  const pathname = usePathname();

  const filteredNavItems = navigationItems
    .filter((item) => !item.roles || item.roles.includes(userRole))
    .slice(0, 5); // Mostrar solo los primeros 5 elementos

  return (
    <aside className="fixed left-0 top-14 z-40 h-[calc(100vh-3.5rem)] w-16 border-r bg-background">
      <div className="flex h-full flex-col items-center py-4 space-y-2">
        {filteredNavItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex h-12 w-12 items-center justify-center rounded-lg transition-colors hover:bg-accent hover:text-accent-foreground",
              pathname === item.href
                ? "bg-accent text-accent-foreground"
                : "text-muted-foreground",
            )}
            title={item.label}
          >
            <item.icon className="h-5 w-5" />
          </Link>
        ))}
      </div>
    </aside>
  );
}
