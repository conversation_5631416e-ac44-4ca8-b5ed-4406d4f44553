"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "@/components/ui/use-toast";
import { InfoIcon } from "lucide-react";
import CourseInstructorSelector from "@/components/courses/CourseInstructorSelector";

export default function NuevoCursoPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [instructors, setInstructors] = useState<any[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    code: "",
    hours: "", // Añadido: estado para horas (string para input)
    instructor_id: "none", // Using "none" instead of empty string
    certificate_template_id: "default", // Using "default" instead of empty string
    status: "draft",
  });

  useEffect(() => {
    async function fetchData() {
      try {
        setLoadingData(true);

        // Ya no necesitamos cargar instructores aquí, se manejan en el componente CourseInstructorSelector

        // Fetch certificate templates
        const { data: templatesData, error: templatesError } = await supabase
          .from("certificate_templates")
          .select("id, name")
          .order("name", { ascending: true });

        if (templatesError) throw templatesError;
        setTemplates(templatesData || []);
      } catch (error: any) {
        console.error("Error fetching data:", error);
        toast({
          title: "Error",
          description: "No se pudieron cargar los datos necesarios. " + error.message,
          variant: "destructive",
        });
      } finally {
        setLoadingData(false);
      }
    }

    fetchData();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      // Preparar los datos asegurándose de que hours sea un número entero o null
      const dataToInsert = {
        ...formData,
        // Convertir a entero o null si está vacío/inválido
        hours: formData.hours === '' || isNaN(parseInt(formData.hours)) ? null : parseInt(formData.hours, 10),
        // Convert special values to null
        instructor_id: formData.instructor_id === 'none' ? null : formData.instructor_id,
        certificate_template_id: formData.certificate_template_id === 'default' ? null : formData.certificate_template_id,
      };

      // Validar que hours sea un número positivo si no es null
      if (dataToInsert.hours !== null && dataToInsert.hours < 0) {
          toast({
              title: "Error de validación",
              description: "Las horas deben ser un número positivo.",
              variant: "destructive",
          });
          setLoading(false);
          return;
      }


      const { data, error } = await supabase
        .from("courses")
        .insert([dataToInsert]) // Usar los datos preparados
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Curso creado",
        description: "El curso se ha creado correctamente.",
      });

      router.push(`/panel-admin/cursos/${data.id}`);
    } catch (error: any) {
      console.error("Error creating course:", error);
      toast({
        title: "Error",
        description: "No se pudo crear el curso. " + error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>Nuevo Curso</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-32 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" /> {/* Añadido Skeleton para horas */}
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-1/4 mx-auto" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <CardTitle>Nuevo Curso</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="title">Título del Curso</Label>
              <Input
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                required
                placeholder="Ej: Manejo de Extintores y Combate de Incendios"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Descripción</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                rows={4}
                placeholder="Describe el contenido y objetivos del curso"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="code">Código del Curso</Label>
              <Input
                id="code"
                name="code"
                value={formData.code}
                onChange={handleChange}
                placeholder="Ej: EXT-101"
              />
            </div>

            {/* Añadido: Campo para Horas */}
            <div className="space-y-2">
              <Label htmlFor="hours">Horas del Curso</Label>
              <Input
                id="hours"
                name="hours"
                type="number"
                value={formData.hours}
                onChange={handleChange}
                placeholder="Ej: 40"
                min="0"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="instructor_id">Instructor Principal</Label>
              <div className="pt-1">
                <CourseInstructorSelector
                  courseId="new"
                  currentInstructorId={formData.instructor_id}
                  onInstructorChange={(value) => handleSelectChange("instructor_id", value)}
                />
              </div>
              <div className="flex items-center gap-2 mt-2 text-sm text-gray-500">
                <InfoIcon className="h-4 w-4" />
                <span>Puedes agregar un nuevo instructor con el botón "Nuevo".</span>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="certificate_template_id">Plantilla de Certificado</Label>
              <Select
                value={formData.certificate_template_id}
                onValueChange={(value) => handleSelectChange("certificate_template_id", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona una plantilla" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="default">Plantilla predeterminada</SelectItem>
                  {templates.map((template) => (
                    <SelectItem key={template.id} value={template.id}>
                      {template.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Estado</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => handleSelectChange("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona un estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Borrador</SelectItem>
                  <SelectItem value="published">Publicado</SelectItem>
                  <SelectItem value="archived">Archivado</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push("/panel-admin/cursos")}
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button type="submit" disabled={loading}>
                {loading ? "Guardando..." : "Guardar Curso"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
