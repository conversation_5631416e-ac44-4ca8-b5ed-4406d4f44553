/**
 * @fileoverview Authentication Service - Business logic for user authentication
 *
 * This service provides a comprehensive authentication system with support for
 * user registration, login, password management, and session handling. It abstracts
 * authentication logic behind interfaces to reduce coupling with specific providers.
 *
 * Features:
 * - User registration with profile creation
 * - Secure login/logout operations
 * - Password reset and update functionality
 * - Session management and validation
 * - Role-based access control integration
 * - Multi-factor authentication support (future)
 *
 * @example Basic Authentication Flow
 * ```typescript
 * import { authService } from '@/lib/services';
 *
 * // User registration
 * const registerResult = await authService.register({
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   first_name: '<PERSON>',
 *   last_name: '<PERSON><PERSON>',
 *   role: 'student'
 * });
 *
 * if (registerResult.success) {
 *   console.log('User registered:', registerResult.data?.user.email);
 * }
 *
 * // User login
 * const loginResult = await authService.login({
 *   email: '<EMAIL>',
 *   password: 'securePassword123'
 * });
 *
 * if (loginResult.success) {
 *   console.log('User logged in:', loginResult.data?.user.email);
 * }
 * ```
 *
 * @example Password Management
 * ```typescript
 * // Request password reset
 * const resetResult = await authService.requestPasswordReset({
 *   email: '<EMAIL>'
 * });
 *
 * // Update password (when authenticated)
 * const updateResult = await authService.updatePassword({
 *   current_password: 'oldPassword',
 *   new_password: 'newSecurePassword123'
 * });
 * ```
 *
 * <AUTHOR> OTEC Development Team
 * @version 1.0.0
 * @since 2025-01-12
 */

import { BaseService, ServiceResponse } from './base-service';
import { AuthAdapter, AuthSession, AuthUser, SignInCredentials, SignUpCredentials } from '../adapters/database/types';
import { RepositoryFactory } from '../repositories';
import { auth } from '../adapters';

// ============================================================================
// Authentication Service Types
// ============================================================================

/**
 * Request payload for user login operations
 *
 * @example
 * ```typescript
 * const loginRequest: LoginRequest = {
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   remember_me: true
 * };
 * ```
 */
export interface LoginRequest {
  /** User's email address */
  email: string;
  /** User's password */
  password: string;
  /** Whether to persist the session for extended period */
  remember_me?: boolean;
}

/**
 * Request payload for user registration operations
 *
 * @example
 * ```typescript
 * const registerRequest: RegisterRequest = {
 *   email: '<EMAIL>',
 *   password: 'securePassword123',
 *   first_name: 'John',
 *   last_name: 'Doe',
 *   identity_document: '12345678-9',
 *   phone: '+56912345678',
 *   role: 'student',
 *   company_id: 'company-uuid'
 * };
 * ```
 */
export interface RegisterRequest {
  /** User's email address (must be unique) */
  email: string;
  /** User's password (minimum 6 characters) */
  password: string;
  /** User's first name */
  first_name: string;
  /** User's last name */
  last_name: string;
  /** User's identity document (RUT, DNI, etc.) */
  identity_document?: string;
  /** User's phone number */
  phone?: string;
  /** User's role in the system (admin users created separately) */
  role?: 'student' | 'instructor';
  /** Associated company ID for corporate users */
  company_id?: string;
}

/**
 * Complete authentication result with session, user, and profile data
 *
 * @example
 * ```typescript
 * const authResult: AuthenticationResult = {
 *   session: { access_token: '...', refresh_token: '...' },
 *   user: { id: 'user-uuid', email: '<EMAIL>' },
 *   profile: { first_name: 'John', last_name: 'Doe', role: 'student' },
 *   redirect_url: '/panel-alumno'
 * };
 * ```
 */
export interface AuthenticationResult {
  /** Authentication session with tokens */
  session: AuthSession;
  /** Authenticated user data */
  user: AuthUser;
  /** User profile from the database */
  profile?: any;
  /** Suggested redirect URL based on user role */
  redirect_url?: string;
}

/**
 * Request payload for password reset operations
 *
 * @example
 * ```typescript
 * const resetRequest: PasswordResetRequest = {
 *   email: '<EMAIL>'
 * };
 * ```
 */
export interface PasswordResetRequest {
  /** Email address to send password reset link */
  email: string;
}

/**
 * Request payload for password update operations
 *
 * @example
 * ```typescript
 * const updateRequest: PasswordUpdateRequest = {
 *   current_password: 'oldPassword123',
 *   new_password: 'newSecurePassword456'
 * };
 * ```
 */
export interface PasswordUpdateRequest {
  /** User's current password for verification */
  current_password: string;
  /** New password to set (minimum 6 characters) */
  new_password: string;
}

export interface ProfileUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone?: string;
  identity_document?: string;
}

// ============================================================================
// Authentication Service Implementation
// ============================================================================

export class AuthService extends BaseService {
  private authAdapter: AuthAdapter;

  constructor(repositories: RepositoryFactory, authAdapter?: AuthAdapter) {
    super(repositories);
    this.authAdapter = authAdapter || auth;
  }

  // ============================================================================
  // Authentication Operations
  // ============================================================================

  async login(request: LoginRequest): Promise<ServiceResponse<AuthenticationResult>> {
    this.logInfo('User login attempt', { email: request.email });

    // Validation
    const validationError = this.validateLoginRequest(request);
    if (validationError) {
      return validationError;
    }

    try {
      // Authenticate with auth provider
      const authResult = await this.authAdapter.signIn({
        email: request.email.toLowerCase().trim(),
        password: request.password
      });

      if (!authResult.data) {
        return this.error(
          'AUTH_FAILED',
          authResult.error?.message || 'Authentication failed'
        );
      }

      // Get user profile from database
      const profile = await this.getUserProfile(authResult.data.user.id);

      // Determine redirect URL based on user role
      const redirectUrl = this.getRedirectUrl(profile?.role);

      const result: AuthenticationResult = {
        session: authResult.data,
        user: authResult.data.user,
        profile,
        redirect_url: redirectUrl
      };

      this.logInfo('User login successful', {
        userId: authResult.data.user.id,
        role: profile?.role
      });

      return this.success(result, 'Login successful');
    } catch (error: any) {
      this.logError('Login failed', error);
      return this.internalError('Login failed', error.message);
    }
  }

  async register(request: RegisterRequest): Promise<ServiceResponse<AuthenticationResult>> {
    this.logInfo('User registration attempt', { email: request.email });

    // Validation
    const validationError = await this.validateRegisterRequest(request);
    if (validationError) {
      return validationError;
    }

    try {
      // Create auth user
      const authResult = await this.authAdapter.signUp({
        email: request.email.toLowerCase().trim(),
        password: request.password,
        options: {
          data: {
            display_name: `${request.first_name} ${request.last_name}`,
            role: request.role || 'student'
          }
        }
      });

      if (!authResult.data) {
        return this.error(
          'REGISTRATION_FAILED',
          authResult.error?.message || 'Registration failed'
        );
      }

      // Create user profile in database
      const profileResult = await this.repositories.users.create({
        id: authResult.data.id,
        email: request.email.toLowerCase().trim(),
        first_name: this.sanitizeString(request.first_name),
        last_name: this.sanitizeString(request.last_name),
        identity_document: request.identity_document,
        phone: request.phone,
        role: request.role || 'student',
        company_id: request.company_id,
        is_active: true,
        created_at: this.formatDate(new Date()),
        updated_at: this.formatDate(new Date())
      });

      // Get session after registration
      const sessionResult = await this.authAdapter.getSession();

      const result: AuthenticationResult = {
        session: sessionResult.data!,
        user: authResult.data,
        profile: profileResult,
        redirect_url: this.getRedirectUrl(request.role || 'student')
      };

      this.logInfo('User registration successful', {
        userId: authResult.data.id,
        role: request.role
      });

      return this.success(result, 'Registration successful');
    } catch (error: any) {
      this.logError('Registration failed', error);
      return this.internalError('Registration failed', error.message);
    }
  }

  async logout(): Promise<ServiceResponse<void>> {
    this.logInfo('User logout');

    try {
      const result = await this.authAdapter.signOut();

      if (result.error) {
        return this.error('LOGOUT_FAILED', result.error.message);
      }

      this.logInfo('User logout successful');
      return this.success(undefined, 'Logout successful');
    } catch (error: any) {
      this.logError('Logout failed', error);
      return this.internalError('Logout failed', error.message);
    }
  }

  // ============================================================================
  // Session Management
  // ============================================================================

  async getCurrentSession(): Promise<ServiceResponse<AuthenticationResult | null>> {
    try {
      const sessionResult = await this.authAdapter.getSession();

      if (!sessionResult.data) {
        return this.success(null, 'No active session');
      }

      // Get user profile
      const profile = await this.getUserProfile(sessionResult.data.user.id);

      const result: AuthenticationResult = {
        session: sessionResult.data,
        user: sessionResult.data.user,
        profile,
        redirect_url: this.getRedirectUrl(profile?.role)
      };

      return this.success(result);
    } catch (error: any) {
      this.logError('Failed to get current session', error);
      return this.internalError('Failed to get session', error.message);
    }
  }

  async refreshSession(): Promise<ServiceResponse<AuthenticationResult>> {
    try {
      const refreshResult = await this.authAdapter.refreshSession();

      if (!refreshResult.data) {
        return this.error('REFRESH_FAILED', refreshResult.error?.message || 'Session refresh failed');
      }

      // Get updated user profile
      const profile = await this.getUserProfile(refreshResult.data.user.id);

      const result: AuthenticationResult = {
        session: refreshResult.data,
        user: refreshResult.data.user,
        profile
      };

      return this.success(result, 'Session refreshed');
    } catch (error: any) {
      this.logError('Session refresh failed', error);
      return this.internalError('Session refresh failed', error.message);
    }
  }

  // ============================================================================
  // Profile Management
  // ============================================================================

  async updateProfile(
    userId: string,
    request: ProfileUpdateRequest
  ): Promise<ServiceResponse<any>> {
    this.logInfo('Updating user profile', { userId });

    const validationError = this.validateUUID(userId, 'user_id');
    if (validationError) {
      return validationError;
    }

    try {
      // Update user profile in database
      const updatedProfile = await this.repositories.users.update(userId, {
        first_name: request.first_name ? this.sanitizeString(request.first_name) : undefined,
        last_name: request.last_name ? this.sanitizeString(request.last_name) : undefined,
        phone: request.phone,
        identity_document: request.identity_document,
        updated_at: this.formatDate(new Date())
      });

      if (!updatedProfile) {
        return this.notFoundError('User profile', userId);
      }

      this.logInfo('Profile updated successfully', { userId });
      return this.success(updatedProfile, 'Profile updated successfully');
    } catch (error: any) {
      this.logError('Profile update failed', error);
      return this.internalError('Profile update failed', error.message);
    }
  }

  // ============================================================================
  // Password Management
  // ============================================================================

  async updatePassword(
    userId: string,
    request: PasswordUpdateRequest
  ): Promise<ServiceResponse<void>> {
    this.logInfo('Updating user password', { userId });

    // Validation
    if (!request.current_password || !request.new_password) {
      return this.validationError('password', 'Current and new passwords are required');
    }

    if (request.new_password.length < 8) {
      return this.validationError('new_password', 'New password must be at least 8 characters long');
    }

    try {
      // Update password through auth provider
      const result = await this.authAdapter.updateUser({
        id: userId,
        // Note: Supabase handles password updates differently
        // This is a simplified implementation
      });

      if (result.error) {
        return this.error('PASSWORD_UPDATE_FAILED', result.error.message);
      }

      this.logInfo('Password updated successfully', { userId });
      return this.success(undefined, 'Password updated successfully');
    } catch (error: any) {
      this.logError('Password update failed', error);
      return this.internalError('Password update failed', error.message);
    }
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private async getUserProfile(userId: string): Promise<any> {
    try {
      return await this.repositories.users.getUserInfo(userId);
    } catch (error) {
      this.logWarn('Failed to get user profile', { userId, error });
      return null;
    }
  }

  private getRedirectUrl(role?: string): string {
    switch (role?.toLowerCase()) {
      case 'admin':
        return '/panel-admin';
      case 'instructor':
        return '/panel-instructor';
      case 'student':
      default:
        return '/panel-alumno';
    }
  }

  // ============================================================================
  // Validation Helpers
  // ============================================================================

  private validateLoginRequest(request: LoginRequest): ServiceResponse | null {
    let error = this.validateRequired(request.email, 'email');
    if (error) return error;

    error = this.validateRequired(request.password, 'password');
    if (error) return error;

    error = this.validateEmail(request.email);
    if (error) return error;

    return null;
  }

  private async validateRegisterRequest(request: RegisterRequest): ServiceResponse | null {
    // Required fields
    let error = this.validateRequired(request.email, 'email');
    if (error) return error;

    error = this.validateRequired(request.password, 'password');
    if (error) return error;

    error = this.validateRequired(request.first_name, 'first_name');
    if (error) return error;

    error = this.validateRequired(request.last_name, 'last_name');
    if (error) return error;

    // Email format
    error = this.validateEmail(request.email);
    if (error) return error;

    // Password strength
    if (request.password.length < 8) {
      return this.validationError('password', 'Password must be at least 8 characters long');
    }

    // Name lengths
    error = this.validateLength(request.first_name, 'first_name', 1, 100);
    if (error) return error;

    error = this.validateLength(request.last_name, 'last_name', 1, 100);
    if (error) return error;

    // Role validation
    if (request.role && !['student', 'instructor'].includes(request.role)) {
      return this.validationError('role', 'Role must be student or instructor');
    }

    // Check for existing user
    const existingUser = await this.repositories.users.findByEmail(request.email);
    if (existingUser) {
      return this.conflictError('User with this email already exists');
    }

    // Check for unique identity document if provided
    if (request.identity_document) {
      const existingIdentity = await this.repositories.users.findByIdentityDocument(
        request.identity_document
      );
      if (existingIdentity) {
        return this.conflictError('User with this identity document already exists');
      }
    }

    return null;
  }
}
