/**
 * Authentication Service
 * 
 * This file implements business logic for authentication operations.
 * It abstracts authentication logic behind interfaces to reduce coupling
 * with specific authentication providers (Supabase Auth, etc.).
 */

import { BaseService, ServiceResponse } from './base-service';
import { AuthAdapter, AuthSession, AuthUser, SignInCredentials, SignUpCredentials } from '../adapters/database/types';
import { RepositoryFactory } from '../repositories';
import { auth } from '../adapters';

// ============================================================================
// Authentication Service Types
// ============================================================================

export interface LoginRequest {
  email: string;
  password: string;
  remember_me?: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  identity_document?: string;
  phone?: string;
  role?: 'student' | 'instructor'; // Admin users are created separately
  company_id?: string;
}

export interface AuthenticationResult {
  session: AuthSession;
  user: AuthUser;
  profile?: any; // User profile from the database
  redirect_url?: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordUpdateRequest {
  current_password: string;
  new_password: string;
}

export interface ProfileUpdateRequest {
  first_name?: string;
  last_name?: string;
  phone?: string;
  identity_document?: string;
}

// ============================================================================
// Authentication Service Implementation
// ============================================================================

export class AuthService extends BaseService {
  private authAdapter: AuthAdapter;

  constructor(repositories: RepositoryFactory, authAdapter?: AuthAdapter) {
    super(repositories);
    this.authAdapter = authAdapter || auth;
  }

  // ============================================================================
  // Authentication Operations
  // ============================================================================

  async login(request: LoginRequest): Promise<ServiceResponse<AuthenticationResult>> {
    this.logInfo('User login attempt', { email: request.email });

    // Validation
    const validationError = this.validateLoginRequest(request);
    if (validationError) {
      return validationError;
    }

    try {
      // Authenticate with auth provider
      const authResult = await this.authAdapter.signIn({
        email: request.email.toLowerCase().trim(),
        password: request.password
      });

      if (!authResult.data) {
        return this.error(
          'AUTH_FAILED',
          authResult.error?.message || 'Authentication failed'
        );
      }

      // Get user profile from database
      const profile = await this.getUserProfile(authResult.data.user.id);

      // Determine redirect URL based on user role
      const redirectUrl = this.getRedirectUrl(profile?.role);

      const result: AuthenticationResult = {
        session: authResult.data,
        user: authResult.data.user,
        profile,
        redirect_url: redirectUrl
      };

      this.logInfo('User login successful', { 
        userId: authResult.data.user.id, 
        role: profile?.role 
      });

      return this.success(result, 'Login successful');
    } catch (error: any) {
      this.logError('Login failed', error);
      return this.internalError('Login failed', error.message);
    }
  }

  async register(request: RegisterRequest): Promise<ServiceResponse<AuthenticationResult>> {
    this.logInfo('User registration attempt', { email: request.email });

    // Validation
    const validationError = await this.validateRegisterRequest(request);
    if (validationError) {
      return validationError;
    }

    try {
      // Create auth user
      const authResult = await this.authAdapter.signUp({
        email: request.email.toLowerCase().trim(),
        password: request.password,
        options: {
          data: {
            display_name: `${request.first_name} ${request.last_name}`,
            role: request.role || 'student'
          }
        }
      });

      if (!authResult.data) {
        return this.error(
          'REGISTRATION_FAILED',
          authResult.error?.message || 'Registration failed'
        );
      }

      // Create user profile in database
      const profileResult = await this.repositories.users.create({
        id: authResult.data.id,
        email: request.email.toLowerCase().trim(),
        first_name: this.sanitizeString(request.first_name),
        last_name: this.sanitizeString(request.last_name),
        identity_document: request.identity_document,
        phone: request.phone,
        role: request.role || 'student',
        company_id: request.company_id,
        is_active: true,
        created_at: this.formatDate(new Date()),
        updated_at: this.formatDate(new Date())
      });

      // Get session after registration
      const sessionResult = await this.authAdapter.getSession();
      
      const result: AuthenticationResult = {
        session: sessionResult.data!,
        user: authResult.data,
        profile: profileResult,
        redirect_url: this.getRedirectUrl(request.role || 'student')
      };

      this.logInfo('User registration successful', { 
        userId: authResult.data.id, 
        role: request.role 
      });

      return this.success(result, 'Registration successful');
    } catch (error: any) {
      this.logError('Registration failed', error);
      return this.internalError('Registration failed', error.message);
    }
  }

  async logout(): Promise<ServiceResponse<void>> {
    this.logInfo('User logout');

    try {
      const result = await this.authAdapter.signOut();
      
      if (result.error) {
        return this.error('LOGOUT_FAILED', result.error.message);
      }

      this.logInfo('User logout successful');
      return this.success(undefined, 'Logout successful');
    } catch (error: any) {
      this.logError('Logout failed', error);
      return this.internalError('Logout failed', error.message);
    }
  }

  // ============================================================================
  // Session Management
  // ============================================================================

  async getCurrentSession(): Promise<ServiceResponse<AuthenticationResult | null>> {
    try {
      const sessionResult = await this.authAdapter.getSession();
      
      if (!sessionResult.data) {
        return this.success(null, 'No active session');
      }

      // Get user profile
      const profile = await this.getUserProfile(sessionResult.data.user.id);

      const result: AuthenticationResult = {
        session: sessionResult.data,
        user: sessionResult.data.user,
        profile,
        redirect_url: this.getRedirectUrl(profile?.role)
      };

      return this.success(result);
    } catch (error: any) {
      this.logError('Failed to get current session', error);
      return this.internalError('Failed to get session', error.message);
    }
  }

  async refreshSession(): Promise<ServiceResponse<AuthenticationResult>> {
    try {
      const refreshResult = await this.authAdapter.refreshSession();
      
      if (!refreshResult.data) {
        return this.error('REFRESH_FAILED', refreshResult.error?.message || 'Session refresh failed');
      }

      // Get updated user profile
      const profile = await this.getUserProfile(refreshResult.data.user.id);

      const result: AuthenticationResult = {
        session: refreshResult.data,
        user: refreshResult.data.user,
        profile
      };

      return this.success(result, 'Session refreshed');
    } catch (error: any) {
      this.logError('Session refresh failed', error);
      return this.internalError('Session refresh failed', error.message);
    }
  }

  // ============================================================================
  // Profile Management
  // ============================================================================

  async updateProfile(
    userId: string, 
    request: ProfileUpdateRequest
  ): Promise<ServiceResponse<any>> {
    this.logInfo('Updating user profile', { userId });

    const validationError = this.validateUUID(userId, 'user_id');
    if (validationError) {
      return validationError;
    }

    try {
      // Update user profile in database
      const updatedProfile = await this.repositories.users.update(userId, {
        first_name: request.first_name ? this.sanitizeString(request.first_name) : undefined,
        last_name: request.last_name ? this.sanitizeString(request.last_name) : undefined,
        phone: request.phone,
        identity_document: request.identity_document,
        updated_at: this.formatDate(new Date())
      });

      if (!updatedProfile) {
        return this.notFoundError('User profile', userId);
      }

      this.logInfo('Profile updated successfully', { userId });
      return this.success(updatedProfile, 'Profile updated successfully');
    } catch (error: any) {
      this.logError('Profile update failed', error);
      return this.internalError('Profile update failed', error.message);
    }
  }

  // ============================================================================
  // Password Management
  // ============================================================================

  async updatePassword(
    userId: string, 
    request: PasswordUpdateRequest
  ): Promise<ServiceResponse<void>> {
    this.logInfo('Updating user password', { userId });

    // Validation
    if (!request.current_password || !request.new_password) {
      return this.validationError('password', 'Current and new passwords are required');
    }

    if (request.new_password.length < 8) {
      return this.validationError('new_password', 'New password must be at least 8 characters long');
    }

    try {
      // Update password through auth provider
      const result = await this.authAdapter.updateUser({
        id: userId,
        // Note: Supabase handles password updates differently
        // This is a simplified implementation
      });

      if (result.error) {
        return this.error('PASSWORD_UPDATE_FAILED', result.error.message);
      }

      this.logInfo('Password updated successfully', { userId });
      return this.success(undefined, 'Password updated successfully');
    } catch (error: any) {
      this.logError('Password update failed', error);
      return this.internalError('Password update failed', error.message);
    }
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  private async getUserProfile(userId: string): Promise<any> {
    try {
      return await this.repositories.users.getUserInfo(userId);
    } catch (error) {
      this.logWarn('Failed to get user profile', { userId, error });
      return null;
    }
  }

  private getRedirectUrl(role?: string): string {
    switch (role?.toLowerCase()) {
      case 'admin':
        return '/panel-admin';
      case 'instructor':
        return '/panel-instructor';
      case 'student':
      default:
        return '/panel-alumno';
    }
  }

  // ============================================================================
  // Validation Helpers
  // ============================================================================

  private validateLoginRequest(request: LoginRequest): ServiceResponse | null {
    let error = this.validateRequired(request.email, 'email');
    if (error) return error;

    error = this.validateRequired(request.password, 'password');
    if (error) return error;

    error = this.validateEmail(request.email);
    if (error) return error;

    return null;
  }

  private async validateRegisterRequest(request: RegisterRequest): ServiceResponse | null {
    // Required fields
    let error = this.validateRequired(request.email, 'email');
    if (error) return error;

    error = this.validateRequired(request.password, 'password');
    if (error) return error;

    error = this.validateRequired(request.first_name, 'first_name');
    if (error) return error;

    error = this.validateRequired(request.last_name, 'last_name');
    if (error) return error;

    // Email format
    error = this.validateEmail(request.email);
    if (error) return error;

    // Password strength
    if (request.password.length < 8) {
      return this.validationError('password', 'Password must be at least 8 characters long');
    }

    // Name lengths
    error = this.validateLength(request.first_name, 'first_name', 1, 100);
    if (error) return error;

    error = this.validateLength(request.last_name, 'last_name', 1, 100);
    if (error) return error;

    // Role validation
    if (request.role && !['student', 'instructor'].includes(request.role)) {
      return this.validationError('role', 'Role must be student or instructor');
    }

    // Check for existing user
    const existingUser = await this.repositories.users.findByEmail(request.email);
    if (existingUser) {
      return this.conflictError('User with this email already exists');
    }

    // Check for unique identity document if provided
    if (request.identity_document) {
      const existingIdentity = await this.repositories.users.findByIdentityDocument(
        request.identity_document
      );
      if (existingIdentity) {
        return this.conflictError('User with this identity document already exists');
      }
    }

    return null;
  }
}
