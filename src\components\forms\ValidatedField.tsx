'use client';

import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { CheckCircle } from 'lucide-react';

type FieldType = 'text' | 'email' | 'number' | 'date' | 'tel' | 'password' | 'select';

export interface ValidatedFieldProps {
  id: string;
  label: string;
  type: FieldType;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  error?: string | null;
  touched?: boolean;
  setTouched?: (touched: boolean) => void;
  onBlur?: () => void;
  placeholder?: string;
  min?: string | number;
  max?: string | number;
  step?: string | number;
  options?: { value: string; label: string }[];
  required?: boolean;
  disabled?: boolean;
  className?: string;
}

export function ValidatedField({
  id,
  label,
  type,
  value,
  onChange,
  error,
  touched = false,
  setTouched,
  onBlur,
  placeholder,
  min,
  max,
  step,
  options,
  required = false,
  disabled = false,
  className = '',
}: ValidatedFieldProps) {
  const handleBlur = () => {
    if (setTouched) {
      setTouched(true);
    }
    if (onBlur) {
      onBlur();
    }
  };

  const showError = error && touched;
  const showSuccess = touched && !error && value;

  return (
    <div className={`space-y-2 ${className}`}>
      <Label
        htmlFor={id}
        className={showError ? 'text-red-500' : ''}
      >
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
        {showSuccess && <CheckCircle className="inline h-4 w-4 text-green-500 ml-1" />}
      </Label>

      {type === 'select' ? (
        <select
          id={id}
          value={value}
          onChange={onChange as React.ChangeEventHandler<HTMLSelectElement>}
          onBlur={handleBlur}
          disabled={disabled}
          className={`w-full p-2 border rounded ${
            showError ? 'border-red-500 focus:ring-red-500' : ''
          } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        >
          <option value="">{placeholder || `Seleccionar ${label.toLowerCase()}`}</option>
          {options?.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
      ) : (
        <Input
          id={id}
          type={type}
          value={value}
          onChange={onChange}
          onBlur={handleBlur}
          min={min}
          max={max}
          step={step}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          className={`${
            showError ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
          } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
        />
      )}

      {showError && (
        <p className="text-sm text-red-500 mt-1">{error}</p>
      )}
    </div>
  );
}