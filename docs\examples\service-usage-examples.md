# Service Usage Examples

This document provides comprehensive examples of how to use the DOMUS OTEC service layer for common operations.

## Table of Contents

- [Authentication Service Examples](#authentication-service-examples)
- [User Service Examples](#user-service-examples)
- [Certificate Service Examples](#certificate-service-examples)
- [Error Handling Patterns](#error-handling-patterns)
- [Advanced Usage Patterns](#advanced-usage-patterns)

## Authentication Service Examples

### Basic User Registration

```typescript
import { authService } from '@/lib/services';

async function registerNewUser() {
  const registrationData = {
    email: '<EMAIL>',
    password: 'securePassword123',
    first_name: '<PERSON>',
    last_name: '<PERSON>',
    identity_document: '12345678-9',
    phone: '+56912345678',
    role: 'student' as const,
  };

  const result = await authService.register(registrationData);

  if (result.success) {
    console.log('User registered successfully:', result.data?.user.email);
    console.log('Redirect to:', result.data?.redirect_url);
    
    // Handle successful registration
    return {
      user: result.data?.user,
      profile: result.data?.profile,
      redirectUrl: result.data?.redirect_url,
    };
  } else {
    console.error('Registration failed:', result.error?.message);
    
    // Handle specific error types
    switch (result.error?.code) {
      case 'VALIDATION_ERROR':
        console.error(`Validation error in field: ${result.error.field}`);
        break;
      case 'AUTH_FAILED':
        console.error('Authentication provider error');
        break;
      default:
        console.error('Unknown registration error');
    }
    
    throw new Error(result.error?.message || 'Registration failed');
  }
}
```

### User Login with Error Handling

```typescript
import { authService } from '@/lib/services';

async function loginUser(email: string, password: string, rememberMe = false) {
  const loginData = {
    email,
    password,
    remember_me: rememberMe,
  };

  try {
    const result = await authService.login(loginData);

    if (result.success) {
      const { user, profile, redirect_url } = result.data!;
      
      console.log(`Welcome back, ${profile.first_name}!`);
      
      // Store user session info if needed
      localStorage.setItem('user_role', profile.role);
      
      return {
        success: true,
        user,
        profile,
        redirectUrl: redirect_url,
      };
    } else {
      return {
        success: false,
        error: result.error?.message || 'Login failed',
        field: result.error?.field,
      };
    }
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      error: 'Network error or service unavailable',
    };
  }
}
```

### Password Reset Flow

```typescript
import { authService } from '@/lib/services';

async function requestPasswordReset(email: string) {
  const result = await authService.requestPasswordReset({ email });

  if (result.success) {
    console.log('Password reset email sent successfully');
    return { success: true, message: 'Check your email for reset instructions' };
  } else {
    console.error('Password reset failed:', result.error?.message);
    return { success: false, error: result.error?.message };
  }
}

async function updateUserPassword(currentPassword: string, newPassword: string) {
  const result = await authService.updatePassword({
    current_password: currentPassword,
    new_password: newPassword,
  });

  if (result.success) {
    console.log('Password updated successfully');
    return { success: true, message: 'Password updated successfully' };
  } else {
    console.error('Password update failed:', result.error?.message);
    return { success: false, error: result.error?.message };
  }
}
```

## User Service Examples

### Creating and Managing Users

```typescript
import { userService } from '@/lib/services';

async function createNewUser() {
  const userData = {
    email: '<EMAIL>',
    first_name: 'Carlos',
    last_name: 'Rodríguez',
    identity_document: '87654321-0',
    phone: '+56987654321',
    role: 'instructor' as const,
    company_id: 'company-uuid-123',
  };

  const result = await userService.createUser(userData);

  if (result.success) {
    console.log('User created:', result.data);
    return result.data;
  } else {
    if (result.error?.code === 'DUPLICATE_EMAIL') {
      console.error('Email already exists');
      throw new Error('A user with this email already exists');
    }
    
    console.error('User creation failed:', result.error?.message);
    throw new Error(result.error?.message || 'Failed to create user');
  }
}
```

### Advanced User Search

```typescript
import { userService } from '@/lib/services';

async function searchUsers(filters: {
  role?: 'admin' | 'student' | 'instructor';
  company?: string;
  active?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}) {
  const searchOptions = {
    role: filters.role,
    company_id: filters.company,
    is_active: filters.active,
    search_query: filters.search,
    page: filters.page || 1,
    limit: filters.limit || 10,
    orderBy: 'created_at',
    orderDirection: 'desc' as const,
  };

  const result = await userService.searchUsers(searchOptions);

  if (result.success) {
    const { data, pagination } = result.data!;
    
    console.log(`Found ${pagination.total} users`);
    console.log(`Page ${pagination.page} of ${pagination.totalPages}`);
    
    return {
      users: data,
      pagination,
      hasMore: pagination.hasNext,
    };
  } else {
    console.error('User search failed:', result.error?.message);
    return {
      users: [],
      pagination: null,
      hasMore: false,
    };
  }
}
```

### Bulk User Operations

```typescript
import { userService } from '@/lib/services';

async function bulkCreateUsers(usersData: Array<{
  email: string;
  first_name: string;
  last_name: string;
  role: 'student' | 'instructor';
  identity_document?: string;
}>) {
  const bulkRequest = {
    users: usersData,
    skip_duplicates: true,
  };

  const result = await userService.bulkCreateUsers(bulkRequest);

  if (result.success) {
    const { created, skipped, errors } = result.data!;
    
    console.log(`Successfully created ${created.length} users`);
    if (skipped.length > 0) {
      console.log(`Skipped ${skipped.length} duplicate users`);
    }
    if (errors.length > 0) {
      console.log(`Failed to create ${errors.length} users`);
      errors.forEach(error => console.error('Error:', error));
    }
    
    return {
      success: true,
      created: created.length,
      skipped: skipped.length,
      errors: errors.length,
      details: { created, skipped, errors },
    };
  } else {
    console.error('Bulk user creation failed:', result.error?.message);
    return {
      success: false,
      error: result.error?.message,
    };
  }
}
```

## Certificate Service Examples

### Issuing Certificates

```typescript
import { certificateService } from '@/lib/services';

async function issueCertificate(userId: string, courseId: string, attendancePercentage: number) {
  const certificateData = {
    user_id: userId,
    course_id: courseId,
    attendance_percentage: attendancePercentage,
    template_id: 'default-template',
  };

  const result = await certificateService.issueCertificate(certificateData);

  if (result.success) {
    const certificate = result.data!;
    
    console.log(`Certificate issued: ${certificate.certificate_number}`);
    console.log(`QR Code: ${certificate.qr_code}`);
    
    return {
      success: true,
      certificate,
      qrCode: certificate.qr_code,
      certificateNumber: certificate.certificate_number,
    };
  } else {
    console.error('Certificate issuance failed:', result.error?.message);
    
    if (result.error?.code === 'NOT_FOUND') {
      throw new Error('User or course not found');
    }
    
    throw new Error(result.error?.message || 'Failed to issue certificate');
  }
}
```

### Certificate Verification

```typescript
import { certificateService } from '@/lib/services';

async function verifyCertificate(qrCode: string) {
  const result = await certificateService.verifyCertificateByQR(qrCode);

  if (result.success) {
    const verification = result.data!;
    
    if (verification.valid) {
      const { certificate } = verification;
      
      console.log('Certificate is valid!');
      console.log(`Certificate Number: ${certificate?.certificate_number}`);
      console.log(`Issued to: ${certificate?.user?.first_name} ${certificate?.user?.last_name}`);
      console.log(`Course: ${certificate?.course?.name}`);
      
      return {
        valid: true,
        certificate: certificate,
        holderName: `${certificate?.user?.first_name} ${certificate?.user?.last_name}`,
        courseName: certificate?.course?.name,
        issueDate: certificate?.issue_date,
      };
    } else {
      console.log('Certificate is invalid:', verification.reason);
      
      return {
        valid: false,
        reason: verification.reason,
      };
    }
  } else {
    console.error('Certificate verification failed:', result.error?.message);
    return {
      valid: false,
      reason: 'Verification service error',
    };
  }
}
```

### Certificate Search and Management

```typescript
import { certificateService } from '@/lib/services';

async function searchCertificates(filters: {
  userId?: string;
  courseId?: string;
  status?: 'active' | 'revoked' | 'expired';
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
}) {
  const searchOptions = {
    user_id: filters.userId,
    course_id: filters.courseId,
    status: filters.status,
    issue_date_from: filters.dateFrom,
    issue_date_to: filters.dateTo,
    page: filters.page || 1,
    limit: filters.limit || 20,
    orderBy: 'issue_date',
    orderDirection: 'desc' as const,
  };

  const result = await certificateService.searchCertificates(searchOptions);

  if (result.success) {
    const { data, pagination } = result.data!;
    
    return {
      certificates: data.map(cert => ({
        id: cert.id,
        number: cert.certificate_number,
        status: cert.status,
        issueDate: cert.issue_date,
        qrCode: cert.qr_code,
        // Add user and course info if available
        holderName: cert.user ? `${cert.user.first_name} ${cert.user.last_name}` : 'Unknown',
        courseName: cert.course?.name || 'Unknown Course',
      })),
      pagination,
    };
  } else {
    console.error('Certificate search failed:', result.error?.message);
    return {
      certificates: [],
      pagination: null,
    };
  }
}
```

## Error Handling Patterns

### Comprehensive Error Handler

```typescript
import { ServiceResponse } from '@/lib/services/base-service';

function handleServiceError<T>(result: ServiceResponse<T>, context: string) {
  if (result.success) {
    return result.data;
  }

  const error = result.error!;
  
  console.error(`${context} failed:`, error.message);
  
  switch (error.code) {
    case 'VALIDATION_ERROR':
      throw new Error(`Validation failed for ${error.field}: ${error.message}`);
    
    case 'NOT_FOUND':
      throw new Error(`Resource not found: ${error.message}`);
    
    case 'UNAUTHORIZED':
      throw new Error(`Access denied: ${error.message}`);
    
    case 'CONFLICT':
      throw new Error(`Conflict: ${error.message}`);
    
    case 'DUPLICATE_EMAIL':
      throw new Error('Email address is already in use');
    
    case 'AUTH_FAILED':
      throw new Error('Authentication failed. Please check your credentials.');
    
    case 'REPOSITORY_ERROR':
      throw new Error('Database operation failed. Please try again.');
    
    default:
      throw new Error(`Operation failed: ${error.message}`);
  }
}

// Usage example
async function safeUserCreation(userData: any) {
  try {
    const result = await userService.createUser(userData);
    return handleServiceError(result, 'User creation');
  } catch (error) {
    console.error('User creation error:', error);
    throw error;
  }
}
```

## Advanced Usage Patterns

### Service Composition

```typescript
import { authService, userService, certificateService } from '@/lib/services';

async function completeStudentOnboarding(studentData: {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  identity_document: string;
  phone: string;
  courseId: string;
}) {
  try {
    // Step 1: Register the user
    const authResult = await authService.register({
      email: studentData.email,
      password: studentData.password,
      first_name: studentData.first_name,
      last_name: studentData.last_name,
      identity_document: studentData.identity_document,
      phone: studentData.phone,
      role: 'student',
    });

    if (!authResult.success) {
      throw new Error(`Registration failed: ${authResult.error?.message}`);
    }

    const userId = authResult.data!.user.id;

    // Step 2: Enroll in course (this would be a separate service)
    console.log(`Enrolling user ${userId} in course ${studentData.courseId}`);

    // Step 3: Return complete onboarding result
    return {
      success: true,
      user: authResult.data!.user,
      profile: authResult.data!.profile,
      redirectUrl: authResult.data!.redirect_url,
      message: 'Student onboarding completed successfully',
    };

  } catch (error) {
    console.error('Student onboarding failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
```

### Batch Processing with Progress Tracking

```typescript
async function processCertificateBatch(
  certificateRequests: Array<{
    user_id: string;
    course_id: string;
    attendance_percentage: number;
  }>,
  onProgress?: (completed: number, total: number) => void
) {
  const results = {
    successful: [] as any[],
    failed: [] as any[],
  };

  for (let i = 0; i < certificateRequests.length; i++) {
    const request = certificateRequests[i];
    
    try {
      const result = await certificateService.issueCertificate(request);
      
      if (result.success) {
        results.successful.push({
          request,
          certificate: result.data,
        });
      } else {
        results.failed.push({
          request,
          error: result.error?.message,
        });
      }
    } catch (error) {
      results.failed.push({
        request,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    // Report progress
    if (onProgress) {
      onProgress(i + 1, certificateRequests.length);
    }

    // Add small delay to avoid overwhelming the system
    await new Promise(resolve => setTimeout(resolve, 100));
  }

  return results;
}
```

This comprehensive guide covers the most common usage patterns for the DOMUS OTEC service layer. Each example includes proper error handling and demonstrates best practices for working with the service APIs.
