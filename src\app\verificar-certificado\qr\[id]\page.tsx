"use client";

import { Download, Printer } from "lucide-react";
import Image from "next/image";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { supabase } from "@/lib/supabase";

export default function CertificateQRPage() {
  const params = useParams();
  const certificateId = params.id as string;

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [certificateData, setCertificateData] = useState<any>(null);
  const [userData, setUserData] = useState<any>(null);
  const [courseData, setCourseData] = useState<any>(null);

  useEffect(() => {
    async function fetchCertificateData() {
      if (!certificateId) return;

      setLoading(true);
      setError(null);

      try {
        // Fetch certificate details
        const { data: certificate, error: certError } = await supabase
          .from("certificates")
          .select("*")
          .eq("id", certificateId)
          .single();

        if (certError) {
          throw new Error("No se encontró el certificado");
        }

        setCertificateData(certificate);

        // Fetch user data
        const { data: user, error: userError } = await supabase
          .from("users")
          .select("first_name, last_name, identity_document")
          .eq("id", certificate.user_id)
          .single();

        if (userError) {
          throw new Error("No se encontró la información del estudiante");
        }

        setUserData(user);

        // Fetch course data
        const { data: course, error: courseError } = await supabase
          .from("courses")
          .select("title")
          .eq("id", certificate.course_id)
          .single();

        if (courseError) {
          throw new Error("No se encontró la información del curso");
        }

        setCourseData(course);
      } catch (error: any) {
        console.error("Error fetching certificate data:", error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    }

    fetchCertificateData();
  }, [certificateId]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "-";
    return new Date(dateString).toLocaleDateString("es-CL", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const handleDownload = () => {
    if (!certificateData?.qr_code) return;

    // Create a temporary link element
    const link = document.createElement("a");
    link.href = certificateData.qr_code;
    link.download = `certificado-${certificateData.certificate_number}-qr.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex justify-center items-center">
        <div className="flex flex-col items-center">
          <div className="bg-white rounded-full p-2 shadow-lg mb-6">
            <Image
              src="/images/logo1.svg"
              alt="DOMUS OTEC Logo"
              width={100}
              height={100}
              className="w-20 h-auto"
              style={{ filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.15))" }}
              priority
            />
          </div>
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
          <p className="mt-4 text-gray-600">
            Cargando información del certificado...
          </p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-white">
        <section className="hero-gradient w-full py-12">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-xl mx-auto text-center">
              <div className="flex justify-center mb-4">
                <div className="bg-white rounded-full p-2 shadow-lg">
                  <Image
                    src="/images/logo1.svg"
                    alt="DOMUS OTEC Logo"
                    width={120}
                    height={120}
                    className="w-24 h-auto"
                    style={{
                      filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.15))",
                    }}
                    priority
                  />
                </div>
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">
                Verificación de Certificado
              </h1>
            </div>
          </div>
        </section>

        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <Card className="max-w-md mx-auto shadow-xl border border-red-200 overflow-hidden">
              <CardHeader className="text-center bg-red-50">
                <CardTitle className="text-xl font-bold text-red-600">
                  Error
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-center">{error}</p>
                <div className="mt-6 flex justify-center">
                  <Button
                    onClick={() => window.history.back()}
                    className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                  >
                    Volver
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  if (!certificateData || !userData || !courseData) {
    return (
      <div className="min-h-screen bg-white">
        <section className="hero-gradient w-full py-12">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-xl mx-auto text-center">
              <div className="flex justify-center mb-4">
                <div className="bg-white rounded-full p-2 shadow-lg">
                  <Image
                    src="/images/logo1.svg"
                    alt="DOMUS OTEC Logo"
                    width={120}
                    height={120}
                    className="w-24 h-auto"
                    style={{
                      filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.15))",
                    }}
                    priority
                  />
                </div>
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">
                Verificación de Certificado
              </h1>
            </div>
          </div>
        </section>

        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <Card className="max-w-md mx-auto shadow-xl border border-yellow-200 overflow-hidden">
              <CardHeader className="text-center bg-yellow-50">
                <CardTitle className="text-xl font-bold text-yellow-600">
                  Información no disponible
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-center">
                  No se pudo cargar la información del certificado.
                </p>
                <div className="mt-6 flex justify-center">
                  <Button
                    onClick={() => window.history.back()}
                    className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                  >
                    Volver
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  if (!certificateData.qr_code) {
    return (
      <div className="min-h-screen bg-white">
        <section className="hero-gradient w-full py-12">
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-xl mx-auto text-center">
              <div className="flex justify-center mb-4">
                <div className="bg-white rounded-full p-2 shadow-lg">
                  <Image
                    src="/images/logo1.svg"
                    alt="DOMUS OTEC Logo"
                    width={120}
                    height={120}
                    className="w-24 h-auto"
                    style={{
                      filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.15))",
                    }}
                    priority
                  />
                </div>
              </div>
              <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">
                Verificación de Certificado
              </h1>
            </div>
          </div>
        </section>

        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <Card className="max-w-md mx-auto shadow-xl border border-yellow-200 overflow-hidden">
              <CardHeader className="text-center bg-yellow-50">
                <CardTitle className="text-xl font-bold text-yellow-600">
                  QR no disponible
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <p className="text-center">
                  Este certificado no tiene un código QR asociado.
                </p>
                <div className="mt-6 flex justify-center">
                  <Button
                    onClick={() => window.history.back()}
                    className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg"
                  >
                    Volver
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero section con gradiente como en la página de inicio */}
      <section className="hero-gradient w-full py-12">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-xl mx-auto text-center">
            {/* Logo en la parte superior */}
            <div className="flex justify-center mb-4">
              <div className="bg-white rounded-full p-2 shadow-lg">
                <Image
                  src="/images/logo1.svg"
                  alt="DOMUS OTEC Logo"
                  width={120}
                  height={120}
                  className="w-24 h-auto"
                  style={{
                    filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.15))",
                  }}
                  priority
                />
              </div>
            </div>
            <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">
              Verificación de Certificado
            </h1>
            <p className="text-md md:text-lg text-white/90 mb-4 leading-relaxed">
              Código QR para validación de certificación profesional
            </p>
          </div>
        </div>
      </section>

      <section className="py-12 bg-white">
        <div className="container mx-auto px-4">
          <Card className="max-w-md mx-auto shadow-xl border border-border/50 overflow-hidden">
            <CardHeader className="text-center bg-gradient-to-r from-primary/10 to-secondary/10 border-b border-border/50">
              <CardTitle className="text-xl font-bold text-primary">
                Código QR del Certificado
              </CardTitle>
              <CardDescription className="text-gray-700">
                Certificado N° {certificateData.certificate_number}
              </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex flex-col items-center">
                <div className="mb-6 p-4 bg-white border border-primary/20 rounded-lg shadow-lg">
                  <div className="relative w-72 h-72">
                    <Image
                      src={certificateData.qr_code}
                      alt="Código QR de verificación"
                      fill
                      style={{ objectFit: "contain" }}
                      priority
                      className="drop-shadow-sm"
                    />
                  </div>
                </div>

                <div className="w-full space-y-3 mb-6 bg-gradient-to-br from-primary/5 to-secondary/5 p-5 rounded-lg border border-primary/20 shadow-md">
                  <div className="text-sm">
                    <span className="font-semibold text-primary">
                      Profesional:
                    </span>{" "}
                    {userData.first_name} {userData.last_name}
                  </div>
                  {userData.identity_document && (
                    <div className="text-sm">
                      <span className="font-semibold text-primary">RUT:</span>{" "}
                      {userData.identity_document}
                    </div>
                  )}
                  <div className="text-sm">
                    <span className="font-semibold text-primary">Curso:</span>{" "}
                    {courseData.title}
                  </div>
                  <div className="text-sm">
                    <span className="font-semibold text-primary">
                      Fecha de emisión:
                    </span>{" "}
                    {formatDate(certificateData.issue_date)}
                  </div>
                  {certificateData.expiry_date && (
                    <div className="text-sm">
                      <span className="font-semibold text-primary">
                        Fecha de vencimiento:
                      </span>{" "}
                      {formatDate(certificateData.expiry_date)}
                    </div>
                  )}
                </div>

                <div className="flex gap-3 w-full justify-center">
                  <Button
                    onClick={handleDownload}
                    className="btn-gradient text-white px-4 py-2 rounded-lg font-medium shadow-lg whitespace-nowrap transition-all duration-300"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Descargar QR
                  </Button>
                  <Button
                    onClick={() => window.print()}
                    variant="outline"
                    className="border-primary/50 text-primary hover:bg-primary/5"
                  >
                    <Printer className="mr-2 h-4 w-4" />
                    Imprimir
                  </Button>
                </div>

                <p className="mt-6 text-sm text-center text-gray-600">
                  Este código QR puede ser escaneado para verificar la
                  autenticidad del certificado.
                  <br />
                  La verificación garantiza que las competencias han sido
                  adquiridas a través de programas acreditados.
                </p>
                <div className="mt-4 text-xs text-center text-gray-500">
                  © DOMUS OTEC - Sistema de Verificación de Certificados
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
