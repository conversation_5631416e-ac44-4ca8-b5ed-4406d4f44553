'use client'

import { useEffect } from 'react'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Registrar el error en un servicio de monitoreo
    console.error('Error global no capturado:', error)
  }, [error])

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center px-4 bg-gray-50">
          <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-8">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-red-600 mb-4">Error del Sistema</h2>
              <div className="bg-red-50 p-4 rounded-md mb-6">
                <p className="text-sm text-red-800">
                  {error.message || 'Ha ocurrido un error crítico en la aplicación.'}
                </p>
              </div>
              <button
                onClick={() => reset()}
                className="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Reiniciar la aplicación
              </button>
            </div>
          </div>
        </div>
      </body>
    </html>
  )
}