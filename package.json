{"name": "context-engineeringfor-qrcourse", "description": "A robust, open-source scaffold for online course and certification platforms, ready for rapid rebranding and deployment.", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "seed:small": "ts-node --project tsconfig.seeds.json seeds/seed.ts SMALL", "seed:medium": "ts-node --project tsconfig.seeds.json seeds/seed.ts MEDIUM", "seed:large": "ts-node --project tsconfig.seeds.json seeds/seed.ts LARGE", "seed:verify": "ts-node --project tsconfig.seeds.json seeds/verify-seed.ts", "seed:cleanup": "ts-node --project tsconfig.seeds.json seeds/cleanup.ts"}, "dependencies": {"@faker-js/faker": "^8.4.1", "@radix-ui/react-alert-dialog": "^1.1.11", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.50.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "dotenv": "^16.4.7", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "papaparse": "^5.5.2", "pg": "^8.16.3", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-to-print": "^3.0.5", "styled-components": "^6.1.17", "tailwind-merge": "^3.0.2", "tw-animate-css": "^1.2.5", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@biomejs/biome": "2.1.1", "@eslint/eslintrc": "^3", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/qrcode": "^1.5.5", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.16", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.2.4", "jest": "^30.0.4", "jest-environment-jsdom": "^30.0.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "ts-node": "^10.9.2", "typescript": "^5"}}