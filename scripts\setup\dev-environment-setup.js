#!/usr/bin/env node

/**
 * @fileoverview Development Environment Setup Script for DOMUS OTEC
 * 
 * Script automatizado para configurar el entorno de desarrollo que incluye:
 * - Verificación de prerequisitos
 * - Configuración de variables de entorno
 * - Instalación de dependencias
 * - Configuración de Git hooks
 * - Verificación de herramientas de desarrollo
 * - Setup inicial de base de datos (opcional)
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');
const readline = require('readline');

// Configuración del script
const SETUP_CONFIG = {
  requiredNodeVersion: '18.0.0',
  requiredNpmVersion: '9.0.0',
  
  // Archivos de configuración
  envExampleFile: '.env.example',
  envLocalFile: '.env.local',
  
  // Dependencias opcionales
  optionalTools: [
    { name: 'supabase', command: 'supabase --version', description: 'Supabase CLI' },
    { name: 'vercel', command: 'vercel --version', description: 'Vercel CLI' },
  ],
  
  // Git hooks
  gitHooks: {
    'pre-commit': 'npm run lint && npm run type-check',
    'pre-push': 'npm test',
  },
};

/**
 * Clase principal para configuración del entorno de desarrollo
 */
class DevEnvironmentSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    
    this.setupResults = {
      prerequisites: false,
      dependencies: false,
      environment: false,
      gitHooks: false,
      verification: false,
    };
  }

  /**
   * Ejecuta el proceso completo de configuración
   */
  async run() {
    try {
      console.log('🚀 DOMUS OTEC - Development Environment Setup');
      console.log('='.repeat(50));
      
      // Verificar prerequisitos
      await this.checkPrerequisites();
      
      // Configurar entorno
      await this.setupEnvironment();
      
      // Instalar dependencias
      await this.installDependencies();
      
      // Configurar Git hooks
      await this.setupGitHooks();
      
      // Verificar configuración
      await this.verifySetup();
      
      // Mostrar resumen
      await this.showSummary();
      
      console.log('\n✅ Setup completado exitosamente!');
      console.log('🚀 Ejecuta "npm run dev" para iniciar el servidor de desarrollo');
      
    } catch (error) {
      console.error('\n❌ Error durante el setup:', error.message);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  /**
   * Verificar prerequisitos del sistema
   */
  async checkPrerequisites() {
    console.log('\n📋 Verificando prerequisitos...');
    
    // Verificar Node.js
    try {
      const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
      console.log(`✅ Node.js: ${nodeVersion}`);
      
      if (!this.isVersionCompatible(nodeVersion.slice(1), SETUP_CONFIG.requiredNodeVersion)) {
        throw new Error(`Node.js ${SETUP_CONFIG.requiredNodeVersion}+ requerido, encontrado ${nodeVersion}`);
      }
    } catch (error) {
      throw new Error('Node.js no encontrado. Instalar desde https://nodejs.org');
    }
    
    // Verificar npm
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      console.log(`✅ npm: v${npmVersion}`);
      
      if (!this.isVersionCompatible(npmVersion, SETUP_CONFIG.requiredNpmVersion)) {
        console.log(`⚠️  npm ${SETUP_CONFIG.requiredNpmVersion}+ recomendado, encontrado v${npmVersion}`);
      }
    } catch (error) {
      throw new Error('npm no encontrado');
    }
    
    // Verificar Git
    try {
      const gitVersion = execSync('git --version', { encoding: 'utf8' }).trim();
      console.log(`✅ Git: ${gitVersion}`);
    } catch (error) {
      throw new Error('Git no encontrado. Instalar desde https://git-scm.com');
    }
    
    // Verificar herramientas opcionales
    console.log('\n🔧 Verificando herramientas opcionales...');
    for (const tool of SETUP_CONFIG.optionalTools) {
      try {
        const version = execSync(tool.command, { encoding: 'utf8', stdio: 'pipe' }).trim();
        console.log(`✅ ${tool.description}: ${version}`);
      } catch {
        console.log(`⚠️  ${tool.description}: No instalado (opcional)`);
      }
    }
    
    this.setupResults.prerequisites = true;
  }

  /**
   * Configurar variables de entorno
   */
  async setupEnvironment() {
    console.log('\n⚙️  Configurando variables de entorno...');
    
    // Verificar si .env.local ya existe
    try {
      await fs.access(SETUP_CONFIG.envLocalFile);
      const overwrite = await this.askQuestion(
        `📄 ${SETUP_CONFIG.envLocalFile} ya existe. ¿Sobrescribir? (y/N): `
      );
      
      if (overwrite.toLowerCase() !== 'y' && overwrite.toLowerCase() !== 'yes') {
        console.log('⏭️  Saltando configuración de entorno');
        this.setupResults.environment = true;
        return;
      }
    } catch {
      // Archivo no existe, continuar
    }
    
    // Leer archivo de ejemplo
    let envExample = '';
    try {
      envExample = await fs.readFile(SETUP_CONFIG.envExampleFile, 'utf8');
    } catch {
      console.log('⚠️  .env.example no encontrado, creando configuración básica...');
      envExample = this.getDefaultEnvTemplate();
    }
    
    // Configurar variables interactivamente
    const envConfig = await this.configureEnvironmentVariables(envExample);
    
    // Guardar configuración
    await fs.writeFile(SETUP_CONFIG.envLocalFile, envConfig);
    console.log(`✅ Variables de entorno guardadas en ${SETUP_CONFIG.envLocalFile}`);
    
    this.setupResults.environment = true;
  }

  /**
   * Instalar dependencias del proyecto
   */
  async installDependencies() {
    console.log('\n📦 Instalando dependencias...');
    
    try {
      console.log('🔄 Ejecutando npm install...');
      execSync('npm install', { stdio: 'inherit' });
      console.log('✅ Dependencias instaladas exitosamente');
      
      this.setupResults.dependencies = true;
    } catch (error) {
      throw new Error('Error instalando dependencias: ' + error.message);
    }
  }

  /**
   * Configurar Git hooks
   */
  async setupGitHooks() {
    console.log('\n🪝 Configurando Git hooks...');
    
    try {
      // Verificar si estamos en un repositorio Git
      execSync('git rev-parse --git-dir', { stdio: 'pipe' });
    } catch {
      console.log('⚠️  No es un repositorio Git, saltando configuración de hooks');
      this.setupResults.gitHooks = true;
      return;
    }
    
    const setupHooks = await this.askQuestion(
      '🪝 ¿Configurar Git hooks para calidad de código? (Y/n): '
    );
    
    if (setupHooks.toLowerCase() === 'n' || setupHooks.toLowerCase() === 'no') {
      console.log('⏭️  Saltando configuración de Git hooks');
      this.setupResults.gitHooks = true;
      return;
    }
    
    try {
      // Crear directorio de hooks si no existe
      const hooksDir = path.join('.git', 'hooks');
      await fs.mkdir(hooksDir, { recursive: true });
      
      // Configurar cada hook
      for (const [hookName, command] of Object.entries(SETUP_CONFIG.gitHooks)) {
        const hookPath = path.join(hooksDir, hookName);
        const hookContent = `#!/bin/sh\n${command}\n`;
        
        await fs.writeFile(hookPath, hookContent);
        
        // Hacer ejecutable (en sistemas Unix)
        if (process.platform !== 'win32') {
          execSync(`chmod +x "${hookPath}"`);
        }
        
        console.log(`✅ Hook ${hookName} configurado`);
      }
      
      this.setupResults.gitHooks = true;
    } catch (error) {
      console.log(`⚠️  Error configurando Git hooks: ${error.message}`);
    }
  }

  /**
   * Verificar que todo esté configurado correctamente
   */
  async verifySetup() {
    console.log('\n🔍 Verificando configuración...');
    
    const checks = [
      {
        name: 'Linting',
        command: 'npm run lint',
        description: 'Verificar estándares de código',
      },
      {
        name: 'Type checking',
        command: 'npm run type-check',
        description: 'Verificar tipos TypeScript',
      },
      {
        name: 'Tests',
        command: 'npm test -- --passWithNoTests --watchAll=false',
        description: 'Ejecutar tests',
      },
      {
        name: 'Build',
        command: 'npm run build',
        description: 'Verificar build de producción',
      },
    ];
    
    let allPassed = true;
    
    for (const check of checks) {
      try {
        console.log(`🔄 ${check.description}...`);
        execSync(check.command, { stdio: 'pipe' });
        console.log(`✅ ${check.name}: OK`);
      } catch (error) {
        console.log(`❌ ${check.name}: FAILED`);
        console.log(`   Error: ${error.message.split('\n')[0]}`);
        allPassed = false;
      }
    }
    
    this.setupResults.verification = allPassed;
    
    if (!allPassed) {
      console.log('\n⚠️  Algunas verificaciones fallaron. Revisar errores arriba.');
    }
  }

  /**
   * Mostrar resumen de la configuración
   */
  async showSummary() {
    console.log('\n📊 Resumen de configuración:');
    console.log('='.repeat(30));
    
    const results = [
      ['Prerequisitos', this.setupResults.prerequisites],
      ['Variables de entorno', this.setupResults.environment],
      ['Dependencias', this.setupResults.dependencies],
      ['Git hooks', this.setupResults.gitHooks],
      ['Verificación', this.setupResults.verification],
    ];
    
    results.forEach(([name, status]) => {
      const icon = status ? '✅' : '❌';
      console.log(`${icon} ${name}`);
    });
    
    console.log('\n📚 Próximos pasos:');
    console.log('1. Ejecutar "npm run dev" para iniciar desarrollo');
    console.log('2. Abrir http://localhost:3000 en el navegador');
    console.log('3. Revisar documentación en docs/');
    console.log('4. Unirse al canal de desarrollo del equipo');
    
    if (!this.setupResults.verification) {
      console.log('\n⚠️  Nota: Resolver errores de verificación antes de continuar');
    }
  }

  /**
   * Configurar variables de entorno interactivamente
   */
  async configureEnvironmentVariables(envExample) {
    console.log('\n📝 Configurando variables de entorno...');
    console.log('💡 Presiona Enter para usar valores por defecto\n');
    
    const lines = envExample.split('\n');
    const configuredLines = [];
    
    for (const line of lines) {
      if (line.trim() === '' || line.startsWith('#')) {
        configuredLines.push(line);
        continue;
      }
      
      const [key, defaultValue] = line.split('=');
      if (!key) {
        configuredLines.push(line);
        continue;
      }
      
      const prompt = `${key}${defaultValue ? ` (${defaultValue})` : ''}: `;
      const value = await this.askQuestion(prompt);
      
      const finalValue = value.trim() || defaultValue || '';
      configuredLines.push(`${key}=${finalValue}`);
    }
    
    return configuredLines.join('\n');
  }

  /**
   * Obtener template por defecto de variables de entorno
   */
  getDefaultEnvTemplate() {
    return `# DOMUS OTEC Environment Configuration

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=DOMUS OTEC

# Development
NODE_ENV=development
`;
  }

  /**
   * Hacer pregunta al usuario
   */
  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }

  /**
   * Verificar compatibilidad de versiones
   */
  isVersionCompatible(current, required) {
    const currentParts = current.split('.').map(Number);
    const requiredParts = required.split('.').map(Number);
    
    for (let i = 0; i < Math.max(currentParts.length, requiredParts.length); i++) {
      const currentPart = currentParts[i] || 0;
      const requiredPart = requiredParts[i] || 0;
      
      if (currentPart > requiredPart) return true;
      if (currentPart < requiredPart) return false;
    }
    
    return true;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  const setup = new DevEnvironmentSetup();
  setup.run().catch(error => {
    console.error('💥 Error fatal:', error);
    process.exit(1);
  });
}

module.exports = { DevEnvironmentSetup, SETUP_CONFIG };
