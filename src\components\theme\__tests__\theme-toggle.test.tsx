/**
 * @fileoverview Unit tests for ThemeToggle component
 * 
 * Tests the theme toggle functionality, accessibility features,
 * and different variants of the theme toggle component.
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { useTheme } from 'next-themes';
import { ThemeToggle, CompactThemeToggle, ThemeStatus, useThemeAware } from '../theme-toggle';

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: jest.fn(),
}));

const mockUseTheme = useTheme as jest.MockedFunction<typeof useTheme>;

describe('ThemeToggle', () => {
  const mockSetTheme = jest.fn();

  beforeEach(() => {
    mockSetTheme.mockClear();
    mockUseTheme.mockReturnValue({
      theme: 'light',
      setTheme: mockSetTheme,
      systemTheme: 'light',
      themes: ['light', 'dark', 'system'],
      resolvedTheme: 'light',
      forcedTheme: undefined,
    });
  });

  describe('Full ThemeToggle (with dropdown)', () => {
    it('should render theme toggle button', () => {
      render(<ThemeToggle />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      expect(button).toBeInTheDocument();
    });

    it('should show dropdown menu when clicked', () => {
      render(<ThemeToggle />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      fireEvent.click(button);
      
      expect(screen.getByText('Light')).toBeInTheDocument();
      expect(screen.getByText('Dark')).toBeInTheDocument();
      expect(screen.getByText('System')).toBeInTheDocument();
    });

    it('should call setTheme when option is selected', () => {
      render(<ThemeToggle />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      fireEvent.click(button);
      
      const darkOption = screen.getByText('Dark');
      fireEvent.click(darkOption);
      
      expect(mockSetTheme).toHaveBeenCalledWith('dark');
    });

    it('should highlight current theme', () => {
      mockUseTheme.mockReturnValue({
        theme: 'dark',
        setTheme: mockSetTheme,
        systemTheme: 'light',
        themes: ['light', 'dark', 'system'],
        resolvedTheme: 'dark',
        forcedTheme: undefined,
      });

      render(<ThemeToggle />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      fireEvent.click(button);
      
      const darkOption = screen.getByText('Dark').closest('div');
      expect(darkOption).toHaveClass('bg-accent');
    });

    it('should hide labels when showLabels is false', () => {
      render(<ThemeToggle showLabels={false} />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      fireEvent.click(button);
      
      expect(screen.queryByText('Light')).not.toBeInTheDocument();
      expect(screen.queryByText('Dark')).not.toBeInTheDocument();
      expect(screen.queryByText('System')).not.toBeInTheDocument();
    });
  });

  describe('Simple ThemeToggle', () => {
    it('should render simple toggle button', () => {
      render(<ThemeToggle simple />);
      
      const button = screen.getByRole('button', { name: /switch to dark mode/i });
      expect(button).toBeInTheDocument();
    });

    it('should toggle between light and dark modes', () => {
      render(<ThemeToggle simple />);
      
      const button = screen.getByRole('button', { name: /switch to dark mode/i });
      fireEvent.click(button);
      
      expect(mockSetTheme).toHaveBeenCalledWith('dark');
    });

    it('should show correct label for dark mode', () => {
      mockUseTheme.mockReturnValue({
        theme: 'dark',
        setTheme: mockSetTheme,
        systemTheme: 'dark',
        themes: ['light', 'dark', 'system'],
        resolvedTheme: 'dark',
        forcedTheme: undefined,
      });

      render(<ThemeToggle simple />);
      
      const button = screen.getByRole('button', { name: /switch to light mode/i });
      expect(button).toBeInTheDocument();
    });
  });

  describe('CompactThemeToggle', () => {
    it('should render compact theme toggle', () => {
      render(<CompactThemeToggle />);
      
      const button = screen.getByRole('button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('h-8', 'w-8');
    });

    it('should apply custom className', () => {
      render(<CompactThemeToggle className="custom-class" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveClass('custom-class');
    });
  });

  describe('ThemeStatus', () => {
    it('should display current theme status', () => {
      render(<ThemeStatus />);
      
      expect(screen.getByText('Light mode')).toBeInTheDocument();
    });

    it('should display system theme with resolved theme', () => {
      mockUseTheme.mockReturnValue({
        theme: 'system',
        setTheme: mockSetTheme,
        systemTheme: 'dark',
        themes: ['light', 'dark', 'system'],
        resolvedTheme: 'dark',
        forcedTheme: undefined,
      });

      render(<ThemeStatus />);
      
      expect(screen.getByText('System (Dark)')).toBeInTheDocument();
    });

    it('should apply custom className', () => {
      render(<ThemeStatus className="custom-status" />);
      
      const status = screen.getByText('Light mode').closest('div');
      expect(status).toHaveClass('custom-status');
    });
  });

  describe('useThemeAware hook', () => {
    it('should return theme utilities', () => {
      const TestComponent = () => {
        const themeUtils = useThemeAware();
        return (
          <div>
            <span data-testid="theme">{themeUtils.theme}</span>
            <span data-testid="effective-theme">{themeUtils.effectiveTheme}</span>
            <span data-testid="is-dark">{themeUtils.isDark.toString()}</span>
            <span data-testid="is-light">{themeUtils.isLight.toString()}</span>
            <span data-testid="is-system">{themeUtils.isSystem.toString()}</span>
          </div>
        );
      };

      render(<TestComponent />);
      
      expect(screen.getByTestId('theme')).toHaveTextContent('light');
      expect(screen.getByTestId('effective-theme')).toHaveTextContent('light');
      expect(screen.getByTestId('is-dark')).toHaveTextContent('false');
      expect(screen.getByTestId('is-light')).toHaveTextContent('true');
      expect(screen.getByTestId('is-system')).toHaveTextContent('false');
    });

    it('should handle system theme correctly', () => {
      mockUseTheme.mockReturnValue({
        theme: 'system',
        setTheme: mockSetTheme,
        systemTheme: 'dark',
        themes: ['light', 'dark', 'system'],
        resolvedTheme: 'dark',
        forcedTheme: undefined,
      });

      const TestComponent = () => {
        const themeUtils = useThemeAware();
        return (
          <div>
            <span data-testid="effective-theme">{themeUtils.effectiveTheme}</span>
            <span data-testid="is-dark">{themeUtils.isDark.toString()}</span>
            <span data-testid="is-system">{themeUtils.isSystem.toString()}</span>
          </div>
        );
      };

      render(<TestComponent />);
      
      expect(screen.getByTestId('effective-theme')).toHaveTextContent('dark');
      expect(screen.getByTestId('is-dark')).toHaveTextContent('true');
      expect(screen.getByTestId('is-system')).toHaveTextContent('true');
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA labels', () => {
      render(<ThemeToggle />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      expect(button).toHaveAttribute('aria-label', 'Toggle theme menu');
    });

    it('should have screen reader text', () => {
      render(<ThemeToggle />);
      
      expect(screen.getByText('Toggle theme')).toHaveClass('sr-only');
    });

    it('should be keyboard accessible', () => {
      render(<ThemeToggle />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      
      // Focus the button
      button.focus();
      expect(button).toHaveFocus();
      
      // Press Enter to open dropdown
      fireEvent.keyDown(button, { key: 'Enter', code: 'Enter' });
      
      // Should show dropdown options
      expect(screen.getByText('Light')).toBeInTheDocument();
    });
  });

  describe('Theme variants', () => {
    it('should apply different button variants', () => {
      const { rerender } = render(<ThemeToggle variant="outline" />);
      
      let button = screen.getByRole('button', { name: /toggle theme menu/i });
      expect(button).toHaveClass('border');
      
      rerender(<ThemeToggle variant="ghost" />);
      button = screen.getByRole('button', { name: /toggle theme menu/i });
      expect(button).toHaveClass('hover:bg-accent');
    });

    it('should apply different sizes', () => {
      const { rerender } = render(<ThemeToggle size="sm" />);
      
      let button = screen.getByRole('button', { name: /toggle theme menu/i });
      expect(button).toHaveClass('h-8');
      
      rerender(<ThemeToggle size="lg" />);
      button = screen.getByRole('button', { name: /toggle theme menu/i });
      expect(button).toHaveClass('h-10');
    });

    it('should apply custom className', () => {
      render(<ThemeToggle className="custom-theme-toggle" />);
      
      const button = screen.getByRole('button', { name: /toggle theme menu/i });
      expect(button).toHaveClass('custom-theme-toggle');
    });
  });
});
