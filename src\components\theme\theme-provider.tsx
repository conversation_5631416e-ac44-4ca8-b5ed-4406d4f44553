/**
 * @fileoverview Theme Provider Component
 * 
 * This component provides theme context to the entire application, enabling
 * dark/light mode switching with system preference detection. Built on top
 * of next-themes for optimal performance and SSR compatibility.
 * 
 * @example Basic Usage
 * ```tsx
 * import { ThemeProvider } from '@/components/theme/theme-provider';
 * 
 * export default function RootLayout({ children }: { children: React.ReactNode }) {
 *   return (
 *     <html lang="en" suppressHydrationWarning>
 *       <body>
 *         <ThemeProvider
 *           attribute="class"
 *           defaultTheme="system"
 *           enableSystem
 *         >
 *           {children}
 *         </ThemeProvider>
 *       </body>
 *     </html>
 *   );
 * }
 * ```
 * 
 * <AUTHOR> OTEC Development Team
 * @version 1.0.0
 * @since 2025-01-12
 */

'use client';

import * as React from 'react';
import { ThemeProvider as NextThemesProvider } from 'next-themes';
import { type ThemeProviderProps } from 'next-themes/dist/types';

/**
 * Theme provider component that wraps the application with theme context
 * 
 * Features:
 * - Automatic system preference detection
 * - Smooth theme transitions
 * - SSR-safe hydration
 * - Persistent theme selection
 * 
 * @param props - Theme provider configuration
 * @returns JSX element providing theme context
 */
export function ThemeProvider({ children, ...props }: ThemeProviderProps) {
  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;
}

/**
 * Default theme provider with optimized settings for DOMUS OTEC
 * 
 * Pre-configured with:
 * - Class-based theme switching
 * - System preference detection
 * - Smooth transitions disabled during theme change
 * - Default to system preference
 * 
 * @example
 * ```tsx
 * import { DefaultThemeProvider } from '@/components/theme/theme-provider';
 * 
 * export default function App({ children }: { children: React.ReactNode }) {
 *   return (
 *     <DefaultThemeProvider>
 *       {children}
 *     </DefaultThemeProvider>
 *   );
 * }
 * ```
 */
export function DefaultThemeProvider({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
      storageKey="domus-otec-theme"
    >
      {children}
    </ThemeProvider>
  );
}

/**
 * Hook to access theme context with type safety
 * 
 * @returns Theme context with current theme, setTheme function, and utilities
 * 
 * @example
 * ```tsx
 * import { useThemeContext } from '@/components/theme/theme-provider';
 * 
 * function ThemeToggle() {
 *   const { theme, setTheme, systemTheme } = useThemeContext();
 *   
 *   return (
 *     <button onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}>
 *       Current theme: {theme}
 *     </button>
 *   );
 * }
 * ```
 */
export { useTheme as useThemeContext } from 'next-themes';

/**
 * Theme configuration constants
 */
export const THEME_CONFIG = {
  /** Available theme options */
  themes: ['light', 'dark', 'system'] as const,
  
  /** Default theme when no preference is set */
  defaultTheme: 'system' as const,
  
  /** Storage key for persisting theme preference */
  storageKey: 'domus-otec-theme',
  
  /** CSS attribute used for theme switching */
  attribute: 'class' as const,
  
  /** Whether to enable system preference detection */
  enableSystem: true,
  
  /** Whether to disable transitions during theme changes */
  disableTransitionOnChange: true,
} as const;

/**
 * Type definitions for theme system
 */
export type Theme = typeof THEME_CONFIG.themes[number];
export type ThemeConfig = typeof THEME_CONFIG;

/**
 * Utility function to get the effective theme (resolves 'system' to actual theme)
 * 
 * @param theme - Current theme setting
 * @param systemTheme - System preference theme
 * @returns Effective theme ('light' or 'dark')
 * 
 * @example
 * ```tsx
 * const effectiveTheme = getEffectiveTheme('system', 'dark'); // Returns 'dark'
 * const effectiveTheme = getEffectiveTheme('light', 'dark'); // Returns 'light'
 * ```
 */
export function getEffectiveTheme(
  theme: string | undefined,
  systemTheme: string | undefined
): 'light' | 'dark' {
  if (theme === 'system') {
    return systemTheme === 'dark' ? 'dark' : 'light';
  }
  return theme === 'dark' ? 'dark' : 'light';
}

/**
 * Utility function to check if dark mode is active
 * 
 * @param theme - Current theme setting
 * @param systemTheme - System preference theme
 * @returns True if dark mode is active
 * 
 * @example
 * ```tsx
 * const isDark = isDarkMode('system', 'dark'); // Returns true
 * const isDark = isDarkMode('light', 'dark'); // Returns false
 * ```
 */
export function isDarkMode(
  theme: string | undefined,
  systemTheme: string | undefined
): boolean {
  return getEffectiveTheme(theme, systemTheme) === 'dark';
}

/**
 * Theme provider with error boundary for graceful fallbacks
 * 
 * @param children - Child components
 * @returns Theme provider with error handling
 */
export function SafeThemeProvider({ children }: { children: React.ReactNode }) {
  return (
    <React.Suspense fallback={<div className="min-h-screen bg-background">{children}</div>}>
      <DefaultThemeProvider>
        {children}
      </DefaultThemeProvider>
    </React.Suspense>
  );
}
