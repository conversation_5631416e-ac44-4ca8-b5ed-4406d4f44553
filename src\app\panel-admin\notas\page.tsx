'use client';

import { useEffect, useState } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2 } from 'lucide-react';
import { UserCombobox, UserOption } from '@/components/ui/user-combobox';

interface Grade {
  id: string;
  user_id: string;
  assessment_id: string;
  score: number;
  feedback: string;
  created_at: string;
  updated_at: string;
}

interface User {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
}

interface Assessment {
  id: string;
  title: string;
  course_id: string;
  type: string;
  max_score: number;
}

export default function GradesManagement() {
  const [grades, setGrades] = useState<Grade[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [assessments, setAssessments] = useState<Assessment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedUser, setSelectedUser] = useState<string>('');
  const [selectedAssessment, setSelectedAssessment] = useState<string>('');
  const [scoreValue, setScoreValue] = useState<string>('');
  const [feedback, setFeedback] = useState<string>('');
  const supabase = createClientComponentClient();

  useEffect(() => {
    fetchData();
  }, []);

  async function fetchData() {
    try {
      setLoading(true);
      setError(null);

      // Fetch grades with user and assessment details
      const { data: gradesData, error: gradesError } = await supabase
        .from('grades')
        .select(`
          *,
          users:user_id(id, first_name, last_name, email),
          assessments:assessment_id(id, title, course_id, type, max_score)
        `);

      if (gradesError) throw gradesError;

      // Fetch users
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('id, first_name, last_name, email')
        .eq('role', 'student');

      if (usersError) throw usersError;

      // Fetch assessments
      const { data: assessmentsData, error: assessmentsError } = await supabase
        .from('assessments')
        .select('id, title, course_id, type, max_score');

      if (assessmentsError) throw assessmentsError;

      setGrades(gradesData || []);
      setUsers(usersData || []);
      setAssessments(assessmentsData || []);
    } catch (error) {
      setError('Error al cargar los datos');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!selectedUser || !selectedAssessment || !scoreValue) {
      setError('Por favor complete todos los campos requeridos');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const { error } = await supabase.from('grades').insert({
        user_id: selectedUser,
        assessment_id: selectedAssessment,
        score: parseFloat(scoreValue),
        feedback,
      });

      if (error) throw error;

      // Reset form and refresh data
      setSelectedUser('');
      setSelectedAssessment('');
      setScoreValue('');
      setFeedback('');
      await fetchData();
    } catch (error) {
      setError('Error al guardar la nota');
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Gestión de Notas</h1>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className="space-y-4 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="student">Estudiante</Label>
            <UserCombobox
              options={users.map(user => ({
                value: user.id,
                label: `${user.first_name} ${user.last_name}`.trim(),
                email: user.email,
                identity_document: user.identity_document
              }))}
              value={selectedUser}
              onChange={setSelectedUser}
              placeholder="Buscar estudiante por nombre, email o RUT..."
              emptyMessage="No se encontraron estudiantes."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="assessment">Evaluación</Label>
            <select
              id="assessment"
              value={selectedAssessment}
              onChange={(e) => setSelectedAssessment(e.target.value)}
              className="w-full p-2 border rounded"
            >
              <option value="">Seleccionar evaluación</option>
              {assessments.map((assessment) => (
                <option key={assessment.id} value={assessment.id}>
                  {assessment.title} ({assessment.type})
                </option>
              ))}
            </select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="score">Puntaje</Label>
            <Input
              id="score"
              type="number"
              min="1"
              step="0.1"
              value={scoreValue}
              onChange={(e) => setScoreValue(e.target.value)}
              placeholder="Ingrese el puntaje"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="feedback">Retroalimentación</Label>
            <Input
              id="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              placeholder="Retroalimentación opcional"
            />
          </div>
        </div>

        <Button type="submit" disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Guardando...
            </>
          ) : (
            'Guardar Nota'
          )}
        </Button>
      </form>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Estudiante</TableHead>
              <TableHead>Evaluación</TableHead>
              <TableHead>Puntaje</TableHead>
              <TableHead>Retroalimentación</TableHead>
              <TableHead>Fecha</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {grades.map((grade) => (
              <TableRow key={grade.id}>
                <TableCell>
                  {(grade.users as any)?.first_name && (grade.users as any)?.last_name
                    ? `${(grade.users as any).first_name} ${(grade.users as any).last_name}`.trim()
                    : 'No disponible'}
                </TableCell>
                <TableCell>
                  {(grade.assessments as any)?.title || 'No disponible'}
                </TableCell>
                <TableCell>{grade.score}</TableCell>
                <TableCell>{grade.feedback || '-'}</TableCell>
                <TableCell>
                  {new Date(grade.created_at).toLocaleDateString()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}