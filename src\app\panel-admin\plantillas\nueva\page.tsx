"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { toast } from "@/components/ui/use-toast";
import { v4 as uuidv4 } from 'uuid';
import SignatureUploader from "@/components/certificates/SignatureUploader";

export default function NuevaPlantillaPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [tempTemplateId] = useState(uuidv4());
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    template_type: "certificate",
    signature_url: "",
    html_template: `<div class="certificate">
  <div class="header">
    <div class="logo">{{logo}}</div>
    <div class="qr-code">
      <div class="qr-image">{{qrCode}}</div>
      <div class="qr-text">Verificar Certificado</div>
    </div>
    <div class="title">CERTIFICADO DE FINALIZACIÓN</div>
    <div class="subtitle">Curso Profesional</div>
  </div>
  <div class="content">
    <p class="statement">Se otorga el presente certificado a:</p>
    <h2 class="participant-name">{{participantName}}</h2>
    <div class="document-number">RUT: {{documentNumber}}</div>
    <p class="statement">Por haber completado satisfactoriamente el curso de:</p>
    <h3 class="course-title">{{courseTitle}}</h3>
    <div class="details">
      <div class="detail-row">
        <span class="label">Duración:</span>
        <span class="value">{{duration}}</span>
      </div>
      <div class="detail-row">
        <span class="label">Fecha de Finalización:</span>
        <span class="value">{{completionDate}}</span>
      </div>
      <div class="detail-row">
        <span class="label">Número de Certificado:</span>
        <span class="value">{{certificateNumber}}</span>
      </div>
    </div>
  </div>
  <div class="footer">
    <div class="signature">
      <div class="signature-image">{{instructorSignature}}</div>
      <div class="signature-line"></div>
      <p>{{instructorName}}</p>
      <p>Instructor</p>
    </div>
    <div class="signature">
      <div class="signature-line"></div>
      <p>{{issuingAuthority}}</p>
      <p>DOMUS</p>
    </div>
  </div>
</div>`,
    css_styles: `.certificate {
  width: 11in;
  height: 8.5in;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px solid #000;
  padding: 20px;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
  background-color: #f8f8f8;
  position: relative;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 20px;
  position: relative;
}

.logo {
  width: 180px;
  height: 100px;
  margin-bottom: 20px;
  position: absolute;
  top: 20px;
  right: 50px;
}

.qr-code {
  position: absolute;
  top: 20px;
  left: 50px;
  width: 150px;
  height: 150px;
  background-color: white;
  border: 1px solid #eaeaea;
  padding: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 10;
  overflow: hidden;
  box-sizing: border-box;
}

.qr-code > div:first-child {
  width: 120px;
  height: 120px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  margin-bottom: 5px;
}

.qr-code img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block;
}

.qr-text {
  font-size: 11px;
  text-align: center;
  font-weight: bold;
  color: #219bf9;
  background-color: white;
  padding: 2px 5px;
  border-radius: 3px;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.title {
  font-size: 3.5em;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
  margin-top: 150px;
  letter-spacing: 1px;
}

.subtitle {
  font-size: 1.8em;
  color: #555;
  margin-bottom: 20px;
  text-align: center;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.statement {
  font-size: 1.2em;
  color: #333;
  line-height: 1.5;
  text-align: center;
  margin-bottom: 10px;
}

.participant-name {
  font-size: 2.5em;
  color: #007bff;
  text-align: center;
  margin: 15px 0;
  text-transform: uppercase;
  font-weight: bold;
  letter-spacing: 1px;
}

.document-number {
  font-size: 1.4em;
  color: #444;
  text-align: center;
  margin-bottom: 25px;
  font-weight: bold;
  border: 1px solid #ddd;
  display: inline-block;
  padding: 5px 15px;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.7);
}

.course-title {
  font-size: 1.8em;
  color: #333;
  text-align: center;
  margin-bottom: 30px;
  font-weight: bold;
  padding: 0 20px;
}

.details {
  width: 80%;
  margin-bottom: 30px;
  border: 1px solid #e0e0ff;
  padding: 20px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.5);
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.label {
  font-weight: bold;
  color: #007bff;
}

.value {
  color: #555;
  font-weight: 500;
}

.footer {
  display: flex;
  justify-content: space-between;
  width: 80%;
  padding-top: 30px;
  border-top: 1px solid #007bff;
  margin-top: 20px;
}

.signature {
  text-align: center;
  width: 200px;
}

.signature-line {
  width: 150px;
  height: 1px;
  background-color: #000;
  margin: 5px auto;
}

.signature-image {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

@media print {
  .certificate {
    border: none;
    background-color: #fff;
  }

  .qr-code {
    box-shadow: none;
    border: 1px solid #ddd;
    background-color: white !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }
}`,
    is_default: false,
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSwitchChange = (name: string, checked: boolean) => {
    setFormData((prev) => ({ ...prev, [name]: checked }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      const { data, error } = await supabase
        .from("certificate_templates")
        .insert([formData])
        .select()
        .single();

      if (error) throw error;

      toast({
        title: "Plantilla creada",
        description: "La plantilla se ha creado correctamente.",
      });

      router.push(`/panel-admin/plantillas/${data.id}`);
    } catch (error: any) {
      console.error("Error creating template:", error);
      toast({
        title: "Error",
        description: "No se pudo crear la plantilla. " + error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Nueva Plantilla de Certificado</h1>
        <Button
          variant="outline"
          onClick={() => router.push("/panel-admin/plantillas")}
        >
          Cancelar
        </Button>
      </div>

      <form onSubmit={handleSubmit}>
        <Tabs defaultValue="info">
          <TabsList className="mb-4">
            <TabsTrigger value="info">Información Básica</TabsTrigger>
            <TabsTrigger value="html">Plantilla HTML</TabsTrigger>
            <TabsTrigger value="css">Estilos CSS</TabsTrigger>
            <TabsTrigger value="preview">Vista Previa</TabsTrigger>
          </TabsList>

          <TabsContent value="info">
            <Card>
              <CardHeader>
                <CardTitle>Información de la Plantilla</CardTitle>
                <CardDescription>Datos básicos de la plantilla de certificado</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nombre de la Plantilla</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    placeholder="Ej: Certificado Profesional"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Descripción</Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleChange}
                    rows={3}
                    placeholder="Describe el propósito y características de esta plantilla"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="template_type">Tipo de Plantilla</Label>
                  <Select
                    value={formData.template_type}
                    onValueChange={(value) => handleSelectChange("template_type", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona un tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="certificate">Certificado</SelectItem>
                      <SelectItem value="diploma">Diploma</SelectItem>
                      <SelectItem value="attendance">Asistencia</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2 pt-4">
                  <Switch
                    id="is_default"
                    checked={formData.is_default}
                    onCheckedChange={(checked) => handleSwitchChange("is_default", checked)}
                  />
                  <Label htmlFor="is_default">Establecer como plantilla predeterminada</Label>
                </div>

                <div className="pt-6 border-t mt-6">
                  <h3 className="text-lg font-medium mb-4">Firma para el Certificado</h3>
                  <SignatureUploader
                    templateId={tempTemplateId}
                    currentSignatureUrl={formData.signature_url}
                    onSignatureUploaded={(url) => {
                      setFormData(prev => ({ ...prev, signature_url: url }));
                    }}
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    Esta firma se mostrará en los certificados generados con esta plantilla.
                  </p>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="html">
            <Card>
              <CardHeader>
                <CardTitle>Plantilla HTML</CardTitle>
                <CardDescription>
                  Estructura HTML del certificado. Usa variables como {'{{participantName}}'}, {'{{courseTitle}}'}, etc.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  id="html_template"
                  name="html_template"
                  value={formData.html_template}
                  onChange={handleChange}
                  rows={20}
                  className="font-mono text-sm"
                />

                <div className="mt-4 bg-gray-50 p-4 rounded-md">
                  <h3 className="font-medium mb-2">Variables disponibles:</h3>
                  <ul className="list-disc pl-5 space-y-1 text-sm">
                    <li><code>{'{{participantName}}'}</code> - Nombre del estudiante</li>
                    <li><code>{'{{documentNumber}}'}</code> - RUT o documento de identidad</li>
                    <li><code>{'{{courseTitle}}'}</code> - Título del curso</li>
                    <li><code>{'{{certificateNumber}}'}</code> - Número de certificado</li>
                    <li><code>{'{{completionDate}}'}</code> - Fecha de finalización</li>
                    <li><code>{'{{duration}}'}</code> - Duración del curso</li>
                    <li><code>{'{{instructorName}}'}</code> - Nombre del instructor</li>
                    <li><code>{'{{issuingAuthority}}'}</code> - Autoridad emisora</li>
                    <li><code>{'{{logo}}'}</code> - Logo de la institución</li>
                    <li><code>{'{{qrCode}}'}</code> - Código QR de verificación</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="css">
            <Card>
              <CardHeader>
                <CardTitle>Estilos CSS</CardTitle>
                <CardDescription>
                  Estilos CSS para personalizar la apariencia del certificado
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Textarea
                  id="css_styles"
                  name="css_styles"
                  value={formData.css_styles}
                  onChange={handleChange}
                  rows={20}
                  className="font-mono text-sm"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preview">
            <Card>
              <CardHeader>
                <CardTitle>Vista Previa</CardTitle>
                <CardDescription>
                  Previsualización de cómo se verá el certificado
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-100 p-4 rounded-md">
                  <p className="text-center text-gray-500 mb-4">
                    La vista previa estará disponible después de guardar la plantilla
                  </p>

                  <div className="aspect-[11/8.5] bg-white border rounded-md flex items-center justify-center">
                    <div className="text-center p-4">
                      <p className="text-gray-400 mb-2">Vista previa no disponible</p>
                      <p className="text-sm text-gray-400">
                        Guarda la plantilla primero para ver una previsualización
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end mt-6">
          <Button type="submit" disabled={loading}>
            {loading ? "Guardando..." : "Guardar Plantilla"}
          </Button>
        </div>
      </form>
    </div>
  );
}
