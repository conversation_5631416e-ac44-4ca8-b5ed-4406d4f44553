# Testing Guide

Comprehensive testing strategy and guidelines for the DOMUS OTEC platform.

## Table of Contents

- [Testing Philosophy](#testing-philosophy)
- [Testing Stack](#testing-stack)
- [Test Structure](#test-structure)
- [Unit Testing](#unit-testing)
- [Integration Testing](#integration-testing)
- [Component Testing](#component-testing)
- [API Testing](#api-testing)
- [Test Utilities](#test-utilities)
- [Best Practices](#best-practices)

## Testing Philosophy

Our testing approach follows the testing pyramid:

1. **Unit Tests (70%)**: Fast, isolated tests for individual functions and components
2. **Integration Tests (20%)**: Tests for component interactions and service integrations
3. **End-to-End Tests (10%)**: Full user journey tests

### Testing Principles

- **Test Behavior, Not Implementation**: Focus on what the code does, not how it does it
- **Write Tests First**: TDD approach for critical business logic
- **Maintainable Tests**: Tests should be easy to read and maintain
- **Fast Feedback**: Tests should run quickly and provide clear error messages
- **Realistic Testing**: Use realistic data and scenarios

## Testing Stack

- **Jest**: Test runner and assertion library
- **React Testing Library**: Component testing utilities
- **MSW (Mock Service Worker)**: API mocking
- **Testing Library User Event**: User interaction simulation
- **Supertest**: API endpoint testing
- **Factory Functions**: Test data generation

## Test Structure

### Directory Structure

```
src/
├── __tests__/
│   ├── integration/
│   │   ├── api-routes.test.ts
│   │   └── user-flows.test.ts
│   └── utils/
│       ├── test-utils.tsx
│       └── factories.ts
├── components/
│   └── __tests__/
│       ├── button.test.tsx
│       └── form.test.tsx
├── lib/
│   ├── services/
│   │   └── __tests__/
│   │       ├── auth-service.test.ts
│   │       ├── user-service.test.ts
│   │       └── integration.test.ts
│   └── repositories/
│       └── __tests__/
│           └── base-repository.test.ts
└── app/
    └── api/
        └── __tests__/
            └── auth.test.ts
```

### Test File Naming

- Unit tests: `*.test.ts` or `*.test.tsx`
- Integration tests: `*.integration.test.ts`
- Component tests: `*.component.test.tsx`
- API tests: `*.api.test.ts`

## Unit Testing

### Service Layer Testing

```typescript
// src/lib/services/__tests__/user-service.test.ts
import { UserService } from '../user-service';
import { createMockRepositoryFactory } from '../../__tests__/utils/factories';

describe('UserService', () => {
  let userService: UserService;
  let mockRepositoryFactory: any;

  beforeEach(() => {
    mockRepositoryFactory = createMockRepositoryFactory();
    userService = new UserService(mockRepositoryFactory);
  });

  describe('createUser', () => {
    it('should create a user successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        role: 'student' as const,
      };

      const mockUser = { id: 'user-123', ...userData };
      mockRepositoryFactory.users.create.mockResolvedValue(mockUser);

      const result = await userService.createUser(userData);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockUser);
      expect(mockRepositoryFactory.users.create).toHaveBeenCalledWith(
        expect.objectContaining(userData)
      );
    });

    it('should handle validation errors', async () => {
      const invalidUserData = {
        email: 'invalid-email',
        first_name: '',
        last_name: 'Doe',
        role: 'student' as const,
      };

      const result = await userService.createUser(invalidUserData);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
    });

    it('should handle duplicate email errors', async () => {
      const userData = {
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        role: 'student' as const,
      };

      mockRepositoryFactory.users.findByEmail.mockResolvedValue({ id: 'existing-user' });

      const result = await userService.createUser(userData);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('DUPLICATE_EMAIL');
    });
  });
});
```

### Utility Function Testing

```typescript
// src/lib/utils/__tests__/validation.test.ts
import { validateEmail, validateChileanRUT } from '../validation';

describe('Validation Utils', () => {
  describe('validateEmail', () => {
    it('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        '<EMAIL>',
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('validateChileanRUT', () => {
    it('should validate correct RUT formats', () => {
      const validRUTs = [
        '12345678-9',
        '1234567-8',
        '12.345.678-9',
      ];

      validRUTs.forEach(rut => {
        expect(validateChileanRUT(rut)).toBe(true);
      });
    });

    it('should reject invalid RUT formats', () => {
      const invalidRUTs = [
        '12345678-0', // Wrong check digit
        '123456789',  // No hyphen
        '12345678-',  // Missing check digit
      ];

      invalidRUTs.forEach(rut => {
        expect(validateChileanRUT(rut)).toBe(false);
      });
    });
  });
});
```

## Integration Testing

### Service Integration Tests

```typescript
// src/lib/services/__tests__/integration.test.ts
import { BaseService } from '../base-service';
import { RepositoryFactory } from '../../repositories';

class TestService extends BaseService {
  constructor() {
    super({} as RepositoryFactory);
  }

  async testComplexOperation(data: {
    email: string;
    name: string;
  }) {
    // Validate inputs
    const emailValidation = this.validateEmail(data.email);
    if (emailValidation) return emailValidation;

    const nameValidation = this.validateRequired(data.name, 'name');
    if (nameValidation) return nameValidation;

    // Process data
    return this.success({
      id: this.generateId(),
      email: this.sanitizeString(data.email),
      name: this.sanitizeString(data.name),
    });
  }
}

describe('Service Integration', () => {
  let service: TestService;

  beforeEach(() => {
    service = new TestService();
  });

  it('should handle complete validation and processing flow', async () => {
    const testData = {
      email: '  <EMAIL>  ',
      name: '  John Doe  ',
    };

    const result = await service.testComplexOperation(testData);

    expect(result.success).toBe(true);
    expect(result.data?.email).toBe('<EMAIL>');
    expect(result.data?.name).toBe('John Doe');
    expect(result.data?.id).toMatch(/^[0-9a-f-]{36}$/);
  });

  it('should handle validation errors in complex flows', async () => {
    const testData = {
      email: 'invalid-email',
      name: 'John Doe',
    };

    const result = await service.testComplexOperation(testData);

    expect(result.success).toBe(false);
    expect(result.error?.code).toBe('VALIDATION_ERROR');
    expect(result.error?.field).toBe('email');
  });
});
```

## Component Testing

### React Component Testing

```typescript
// src/components/__tests__/user-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { UserForm } from '../user-form';
import { TestWrapper } from '../../__tests__/utils/test-utils';

describe('UserForm', () => {
  const mockOnSubmit = jest.fn();

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('should render all form fields', () => {
    render(
      <TestWrapper>
        <UserForm onSubmit={mockOnSubmit} />
      </TestWrapper>
    );

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/first name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/last name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/role/i)).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <UserForm onSubmit={mockOnSubmit} />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /submit/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/first name is required/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should submit valid form data', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <UserForm onSubmit={mockOnSubmit} />
      </TestWrapper>
    );

    // Fill out form
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    
    // Select role
    await user.click(screen.getByLabelText(/role/i));
    await user.click(screen.getByText(/student/i));

    // Submit form
    await user.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>',
        first_name: 'John',
        last_name: 'Doe',
        role: 'student',
      });
    });
  });

  it('should handle form submission errors', async () => {
    const user = userEvent.setup();
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'));
    
    render(
      <TestWrapper>
        <UserForm onSubmit={mockOnSubmit} />
      </TestWrapper>
    );

    // Fill and submit form
    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/first name/i), 'John');
    await user.type(screen.getByLabelText(/last name/i), 'Doe');
    await user.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(screen.getByText(/submission failed/i)).toBeInTheDocument();
    });
  });
});
```

### Theme Component Testing

```typescript
// src/components/theme/__tests__/theme-toggle.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from 'next-themes';
import { ThemeToggle } from '../theme-toggle';

// Mock next-themes
jest.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    setTheme: jest.fn(),
    resolvedTheme: 'light',
  }),
  ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
}));

describe('ThemeToggle', () => {
  it('should render theme toggle button', () => {
    render(
      <ThemeProvider>
        <ThemeToggle />
      </ThemeProvider>
    );

    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(screen.getByText(/toggle theme/i)).toBeInTheDocument();
  });

  it('should show theme options when clicked', () => {
    render(
      <ThemeProvider>
        <ThemeToggle />
      </ThemeProvider>
    );

    fireEvent.click(screen.getByRole('button'));

    expect(screen.getByText(/light/i)).toBeInTheDocument();
    expect(screen.getByText(/dark/i)).toBeInTheDocument();
    expect(screen.getByText(/system/i)).toBeInTheDocument();
  });
});
```

## API Testing

### API Route Testing

```typescript
// src/app/api/__tests__/auth.test.ts
import { POST } from '../auth/login/route';
import { NextRequest } from 'next/server';

// Mock Supabase
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      signInWithPassword: jest.fn(),
    },
  },
}));

describe('/api/auth/login', () => {
  it('should handle successful login', async () => {
    const mockUser = {
      id: 'user-123',
      email: '<EMAIL>',
    };

    const mockSession = {
      access_token: 'mock-token',
      user: mockUser,
    };

    require('@/lib/supabase').supabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: mockUser, session: mockSession },
      error: null,
    });

    const request = new NextRequest('http://localhost:3000/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.data.user.email).toBe('<EMAIL>');
  });

  it('should handle login errors', async () => {
    require('@/lib/supabase').supabase.auth.signInWithPassword.mockResolvedValue({
      data: { user: null, session: null },
      error: { message: 'Invalid credentials' },
    });

    const request = new NextRequest('http://localhost:3000/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(401);
    expect(data.success).toBe(false);
    expect(data.error.message).toBe('Invalid credentials');
  });
});
```

## Test Utilities

### Test Wrapper Component

```typescript
// src/__tests__/utils/test-utils.tsx
import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { ThemeProvider } from 'next-themes';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

interface TestWrapperProps {
  children: React.ReactNode;
}

export function TestWrapper({ children }: TestWrapperProps) {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider attribute="class" defaultTheme="light">
        {children}
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export function renderWithProviders(
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) {
  return render(ui, { wrapper: TestWrapper, ...options });
}

export * from '@testing-library/react';
export { renderWithProviders as render };
```

### Factory Functions

```typescript
// src/__tests__/utils/factories.ts
export function createMockUser(overrides = {}) {
  return {
    id: 'user-123',
    email: '<EMAIL>',
    first_name: 'John',
    last_name: 'Doe',
    role: 'student',
    is_active: true,
    created_at: '2024-01-15T10:00:00Z',
    ...overrides,
  };
}

export function createMockCertificate(overrides = {}) {
  return {
    id: 'cert-123',
    certificate_number: 'CERT-2024-001',
    user_id: 'user-123',
    course_id: 'course-456',
    qr_code: 'QR-CODE-DATA',
    issue_date: '2024-01-15',
    status: 'active',
    ...overrides,
  };
}

export function createMockRepositoryFactory() {
  return {
    users: {
      create: jest.fn(),
      findById: jest.fn(),
      findByEmail: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      search: jest.fn(),
    },
    certificates: {
      create: jest.fn(),
      findById: jest.fn(),
      findByQRCode: jest.fn(),
      update: jest.fn(),
      revokeCertificate: jest.fn(),
      search: jest.fn(),
    },
  };
}
```

## Best Practices

### 1. Test Organization

- Group related tests using `describe` blocks
- Use descriptive test names that explain the expected behavior
- Follow the AAA pattern: Arrange, Act, Assert

### 2. Mocking Strategy

- Mock external dependencies (APIs, databases)
- Use real implementations for internal utilities
- Mock at the boundary of your system

### 3. Test Data

- Use factory functions for consistent test data
- Make test data realistic but minimal
- Avoid hardcoded values that might change

### 4. Assertions

- Test behavior, not implementation details
- Use specific assertions over generic ones
- Test both success and error cases

### 5. Performance

- Keep tests fast and isolated
- Use `beforeEach` for setup, `afterEach` for cleanup
- Avoid unnecessary async operations

### 6. Maintenance

- Update tests when requirements change
- Remove obsolete tests
- Keep test code as clean as production code

This testing guide ensures comprehensive coverage and maintainable test suites for the DOMUS OTEC platform.
